﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.SharedClass.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.EF.DefaultMigration
{
    public class DefaultDynoUser : IEntityTypeConfiguration<UserEntity>
    {
        private readonly ConfigurationDefaultId _defaultId;
        private readonly Configuration _config;

        public DefaultDynoUser(ConfigurationDefaultId defaultId,
            Configuration config)
        {
            _defaultId = defaultId;
            _config= config;
        }

        public void Configure(EntityTypeBuilder<UserEntity> builder)
        {
            var dynoAdmin = new UserEntity
            {
                Id = _defaultId.UserId,
                PinCode = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(_defaultId.CompanyId.ToString()))),
                FullName = "Dyno Root",
                UserName = _defaultId.UserId.ToString(),
                NormalizedUserName = _defaultId.UserId.ToString().ToUpper(),
                Email = _config.Email,
                NormalizedEmail = _config.Email.ToUpper(),
                EmailConfirmed = true,
                PhoneNumberConfirmed = true,
                DateOfBirth = DateTime.UtcNow,
                CreationTime = DateTime.UtcNow,
                Gender = Shared.Enum.Gender.Other,
                AuthentificationSource = Shared.Enum.AuthentificationSource.Dyno,
                CreatorUserId= _defaultId.UserId,
                UserType = Shared.Enum.UserType.SuperAdmin,
                CompanyId = _defaultId.CompanyId
            };
            var hasher = new PasswordHasher<UserEntity>();
            dynoAdmin.PasswordHash = hasher.HashPassword(dynoAdmin, _config.EmailPassword);
            builder.HasData(dynoAdmin);
        }
    }
}
