﻿using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.Service.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.ResponseAPI;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.Shared.Enum;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Pagination;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("Api/[controller]")]
    [ApiController]
    public class AuthAdminController : ControllerBase
    {
        private readonly IAuthAdminService _authService;
        private readonly IEmailingService _emailService;
        private readonly ILogger<AuthAdminController> _logger;
        public AuthAdminController(IAuthAdminService authService,
            ILogger<AuthAdminController> logger,
            IEmailingService emailService) 
        { 
            _authService = authService;
            _emailService = emailService;
            _logger = logger;
        }

        [Route("Register")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Register([FromBody] AdminRegisterDTO user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                string? createdUserId = HttpContext.User.FindFirstValue("Id");
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    user.CreatorUserType = enumUserType;
                    ResponseAPI<AuthResponseDTO> response = await _authService.Register(user, createdUserId != null ? new Guid(createdUserId) : null, new Guid(companyId));
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Register)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("Login")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult Login([FromBody] AdminLoginDTO loginDto, [FromHeader] string acceptLanguage)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                LanguageType language = LanguageType.En;
                _ = Enum.TryParse(acceptLanguage, out language);

                ResponseAPI<AuthResponseDTO> response = _authService.Login(loginDto, language);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Login)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Logout")]
        [HttpGet]
        //[Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Logout()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                

                string? authorizationHeader = HttpContext.Request.Headers["Authorization"];

                if (!string.IsNullOrEmpty(authorizationHeader) && authorizationHeader.StartsWith("Bearer "))
                {
                    string token = authorizationHeader.Substring("Bearer ".Length);
                    string? userId = HttpContext.User.FindFirstValue("Id");
                    if (userId == null)
                    {
                        return Unauthorized(new ResponseAPI<AuthResponseDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<AuthResponseDTO> response = _authService.LogOut(new Guid(userId), token);
                    return Ok(response);


                }

                return Unauthorized(new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "No valid token provided."
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Register)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("RefreshToken")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult RefreshToken(UserTokenDTO token)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(token.Token);

                // Access the "Id" claim
                string? userId = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "Id")?.Value;
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                token.UserId = new Guid(userId);

                ResponseAPI<AuthResponseDTO> response = _authService.GetRefreshToken(token);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Logout)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("ForgetPassword")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public async Task<IActionResult> ForgetPassword([FromBody] ForgetPasswordDTO forgetPassword)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = await _authService.ForgetPassword(forgetPassword.Email);
                if(response.StatusCode== System.Net.HttpStatusCode.OK)
                {
                    ResponseAPI<bool> sendEmail = _emailService.SendResetPasswordEmail(forgetPassword, response.ObjectValue.Token);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Logout)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("CreatePassword")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public async Task<IActionResult> CreatePassword([FromBody] ForgetPasswordDTO forgetPassword)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = await _authService.ForgetPassword(forgetPassword.Email);
              /*  if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response.);
                    *//*ResponseAPI<bool> sendEmail = _emailService.SendResetPasswordEmail(forgetPassword, response.ObjectValue.Token);*//*
                }*/
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Logout)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("ResetPassword")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDTO resetPassword)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = await _authService.ResetPassword(resetPassword);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Logout)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }



    }
}
