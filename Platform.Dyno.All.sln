﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33403.182
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Platform.Dyno.AccessManagement", "Platform.Dyno.AccessManagement", "{7BDF88E2-A024-41A3-9F26-78E4E053AA1D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Platform.Dyno.Payment", "Platform.Dyno.Payment", "{373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Platform.Dyno.Shared", "Platform.Dyno.Shared", "{7E3A679B-F5EC-4CE2-8D3B-B3EC30EA29F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Shared", "Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj", "{01E612AA-24FC-4642-8E46-9E25C47D33FC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Platform.Dyno.Notification", "Platform.Dyno.Notification", "{B5E006B4-EE06-4D87-AAB5-2AE323A736D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.WebAPI", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Notification\Platform.Dyno.Notification\Platform.Dyno.Notification.WebAPI.csproj", "{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.Business", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Notification\Platform.Dyno.Notification.Business\Platform.Dyno.Notification.Business.csproj", "{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.DTO", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Notification\Platform.Dyno.Notification.DTO\Platform.Dyno.Notification.DTO.csproj", "{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Application\Application.csproj", "{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Domain\Domain.csproj", "{479245B1-A3A8-4F44-ADBC-26FBA5CFA778}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Infrastructure\Infrastructure.csproj", "{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Payment.WebAPI", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.WebAPI\Platform.Dyno.Payment.WebAPI.csproj", "{6D46F953-CC35-4B77-B291-70AD5B3BC513}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Payment.DTO", "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.DTO\Platform.Dyno.Payment.DTO.csproj", "{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Server.Kafka", "Platform.Dyno.Microservice.Backend\Server.Kafka\Server.Kafka\Server.Kafka.csproj", "{84947056-D867-4075-85CA-7D6F047B9DE8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.Business", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.Business\Platform.Dyno.AccessManagement.Business.csproj", "{CF1226FF-94E0-4E37-B257-3F0767B3BA7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.BusinessModel", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.BusinessModel\Platform.Dyno.AccessManagement.BusinessModel.csproj", "{E318FB3B-EA02-4496-8017-20D790CCD196}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.DataModel", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.DataModel\Platform.Dyno.AccessManagement.DataModel.csproj", "{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.DTO", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.DTO\Platform.Dyno.AccessManagement.DTO.csproj", "{F7042FB5-CEA6-4593-A1C5-442263A72B0D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.EF", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.EF\Platform.Dyno.AccessManagement.EF.csproj", "{8B0A01E8-BC51-433A-9B0F-FEED094C0081}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Dyno.AccessManagement.WebAPI", "Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessManagement\Platform.Dyno.AccessManagement.WebAPI\Platform.Dyno.AccessManagement.WebAPI.csproj", "{F745840E-CD61-480C-985F-84CEB6D48DD9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{01E612AA-24FC-4642-8E46-9E25C47D33FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{01E612AA-24FC-4642-8E46-9E25C47D33FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{01E612AA-24FC-4642-8E46-9E25C47D33FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{01E612AA-24FC-4642-8E46-9E25C47D33FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F}.Release|Any CPU.Build.0 = Release|Any CPU
		{479245B1-A3A8-4F44-ADBC-26FBA5CFA778}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{479245B1-A3A8-4F44-ADBC-26FBA5CFA778}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{479245B1-A3A8-4F44-ADBC-26FBA5CFA778}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{479245B1-A3A8-4F44-ADBC-26FBA5CFA778}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D46F953-CC35-4B77-B291-70AD5B3BC513}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D46F953-CC35-4B77-B291-70AD5B3BC513}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D46F953-CC35-4B77-B291-70AD5B3BC513}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D46F953-CC35-4B77-B291-70AD5B3BC513}.Release|Any CPU.Build.0 = Release|Any CPU
		{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{84947056-D867-4075-85CA-7D6F047B9DE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84947056-D867-4075-85CA-7D6F047B9DE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84947056-D867-4075-85CA-7D6F047B9DE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84947056-D867-4075-85CA-7D6F047B9DE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CF1226FF-94E0-4E37-B257-3F0767B3BA7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CF1226FF-94E0-4E37-B257-3F0767B3BA7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CF1226FF-94E0-4E37-B257-3F0767B3BA7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CF1226FF-94E0-4E37-B257-3F0767B3BA7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{E318FB3B-EA02-4496-8017-20D790CCD196}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E318FB3B-EA02-4496-8017-20D790CCD196}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E318FB3B-EA02-4496-8017-20D790CCD196}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E318FB3B-EA02-4496-8017-20D790CCD196}.Release|Any CPU.Build.0 = Release|Any CPU
		{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7042FB5-CEA6-4593-A1C5-442263A72B0D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7042FB5-CEA6-4593-A1C5-442263A72B0D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7042FB5-CEA6-4593-A1C5-442263A72B0D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7042FB5-CEA6-4593-A1C5-442263A72B0D}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B0A01E8-BC51-433A-9B0F-FEED094C0081}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B0A01E8-BC51-433A-9B0F-FEED094C0081}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B0A01E8-BC51-433A-9B0F-FEED094C0081}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B0A01E8-BC51-433A-9B0F-FEED094C0081}.Release|Any CPU.Build.0 = Release|Any CPU
		{F745840E-CD61-480C-985F-84CEB6D48DD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F745840E-CD61-480C-985F-84CEB6D48DD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F745840E-CD61-480C-985F-84CEB6D48DD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F745840E-CD61-480C-985F-84CEB6D48DD9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{01E612AA-24FC-4642-8E46-9E25C47D33FC} = {7E3A679B-F5EC-4CE2-8D3B-B3EC30EA29F2}
		{8E0F85F2-57EB-4E4C-B8A3-9C1AF0B2F628} = {B5E006B4-EE06-4D87-AAB5-2AE323A736D3}
		{AF0C8B5E-9A40-4DEE-8D0B-E6D79687C5B1} = {B5E006B4-EE06-4D87-AAB5-2AE323A736D3}
		{8BC75931-AD48-4D48-87A5-E5C8D50FDF9B} = {B5E006B4-EE06-4D87-AAB5-2AE323A736D3}
		{A9D74976-A3EC-41E9-AEA9-03EB41B24B5F} = {373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}
		{479245B1-A3A8-4F44-ADBC-26FBA5CFA778} = {373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}
		{1BDAC3E2-8F2E-4C9C-867D-2BA73D6AF143} = {373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}
		{6D46F953-CC35-4B77-B291-70AD5B3BC513} = {373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}
		{112E4EF7-0188-4DC9-A741-FB4A1E53AC2A} = {373388E4-14C7-4ED1-8B1A-DD19A14DCF0D}
		{CF1226FF-94E0-4E37-B257-3F0767B3BA7E} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
		{E318FB3B-EA02-4496-8017-20D790CCD196} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
		{A95B9D71-4EF0-4351-BF04-C48FB33EF5A3} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
		{F7042FB5-CEA6-4593-A1C5-442263A72B0D} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
		{8B0A01E8-BC51-433A-9B0F-FEED094C0081} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
		{F745840E-CD61-480C-985F-84CEB6D48DD9} = {7BDF88E2-A024-41A3-9F26-78E4E053AA1D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9F9D6F62-7AB7-4486-BCF5-5595F8E8983F}
	EndGlobalSection
EndGlobal
