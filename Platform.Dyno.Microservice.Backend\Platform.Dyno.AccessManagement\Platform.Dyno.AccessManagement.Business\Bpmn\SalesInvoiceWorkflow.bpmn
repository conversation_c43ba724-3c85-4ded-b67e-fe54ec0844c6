<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xdu3jn" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.19.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="Process_SalesInvoice_Management" name="Sales Invoice Management" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:serviceTask id="Activity_1wast8y" name="Send Notification SA" camunda:type="external" camunda:topic="SI_Send_Notification_New_SI">
      <bpmn:incoming>Flow_1djmybl</bpmn:incoming>
      <bpmn:incoming>Flow_0dpnstt</bpmn:incoming>
      <bpmn:outgoing>Flow_1b3ua8z</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1n5m1ax" name="Status SI : Email Sent" camunda:type="external" camunda:topic="SI_Status_Email_Sent">
      <bpmn:incoming>Flow_0bncapp</bpmn:incoming>
      <bpmn:outgoing>Flow_1djmybl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1b0s858" name="Log-Error: Email Failed" camunda:type="external" camunda:topic="SI_Log_Error_Email_Failed">
      <bpmn:incoming>Flow_0gpf44f</bpmn:incoming>
      <bpmn:outgoing>Flow_0dpnstt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1bqbpu7" name="Status SI : Email Failed" camunda:type="external" camunda:topic="SI_Status_Email_Failed">
      <bpmn:incoming>Flow_1sw8bqf</bpmn:incoming>
      <bpmn:outgoing>Flow_0gpf44f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1rv5vxk" name="Send Notification SA : SI PDF failed" camunda:type="external" camunda:topic="SI_Notification_PDF_Failed">
      <bpmn:incoming>Flow_03vxsi6</bpmn:incoming>
      <bpmn:outgoing>Flow_08z61ty</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_022cdbo" name="Status SI : PDF failed" camunda:type="external" camunda:topic="SI_Status_PDF_Failed">
      <bpmn:incoming>Flow_1aebetc</bpmn:incoming>
      <bpmn:incoming>Flow_1ijh2vd</bpmn:incoming>
      <bpmn:outgoing>Flow_18p28f0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0vd3ese" name="Log-Error: PDF Failed" camunda:type="external" camunda:topic="SI_Log_Error_PDF_Failed">
      <bpmn:incoming>Flow_18p28f0</bpmn:incoming>
      <bpmn:outgoing>Flow_03vxsi6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0c1uma3" name="Send Email" camunda:type="external" camunda:topic="Send_Email_SI">
      <bpmn:incoming>Flow_0f7ga3i</bpmn:incoming>
      <bpmn:outgoing>Flow_0q1rp61</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_110l2fy">
      <bpmn:incoming>Flow_0q1rp61</bpmn:incoming>
      <bpmn:outgoing>Flow_0bncapp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sw8bqf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_08l6jvu" name="Status SI: PDF Generated" camunda:type="external" camunda:topic="SI_Status_PDF_Generated">
      <bpmn:incoming>Flow_1nvo3iy</bpmn:incoming>
      <bpmn:outgoing>Flow_0f7ga3i</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1uxdjtt">
      <bpmn:incoming>Flow_0lpwrp3</bpmn:incoming>
      <bpmn:outgoing>Flow_1aebetc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1nvo3iy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_0sacem3" name="Create SI PDF" camunda:type="external" camunda:topic="Create_SI_PDF">
      <bpmn:incoming>Flow_1a69l3s</bpmn:incoming>
      <bpmn:outgoing>Flow_0lpwrp3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="Event_1xiu7ke">
      <bpmn:outgoing>Flow_05mlnv4</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_0te5nqr">
      <bpmn:incoming>Flow_1b3ua8z</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_07itya0" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1szo20y">
      <bpmn:incoming>Flow_1tyeohr</bpmn:incoming>
      <bpmn:outgoing>Flow_1a69l3s</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ijh2vd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_15r9cd9" name="Add instance to SI" camunda:type="external" camunda:topic="Add_Instance_To_SI">
      <bpmn:incoming>Flow_05mlnv4</bpmn:incoming>
      <bpmn:outgoing>Flow_0305q5x</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1djmybl" sourceRef="Activity_1n5m1ax" targetRef="Activity_1wast8y" />
    <bpmn:sequenceFlow id="Flow_0dpnstt" sourceRef="Activity_1b0s858" targetRef="Activity_1wast8y" />
    <bpmn:sequenceFlow id="Flow_1b3ua8z" sourceRef="Activity_1wast8y" targetRef="Event_0te5nqr" />
    <bpmn:sequenceFlow id="Flow_0bncapp" sourceRef="Gateway_110l2fy" targetRef="Activity_1n5m1ax">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Send_Email_SI ==  true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0gpf44f" sourceRef="Activity_1bqbpu7" targetRef="Activity_1b0s858" />
    <bpmn:sequenceFlow id="Flow_1sw8bqf" sourceRef="Gateway_110l2fy" targetRef="Activity_1bqbpu7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Send_Email_SI == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03vxsi6" sourceRef="Activity_0vd3ese" targetRef="Activity_1rv5vxk" />
    <bpmn:sequenceFlow id="Flow_08z61ty" sourceRef="Activity_1rv5vxk" targetRef="Event_0ea41rf" />
    <bpmn:sequenceFlow id="Flow_1aebetc" sourceRef="Gateway_1uxdjtt" targetRef="Activity_022cdbo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_SI_PDF == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_18p28f0" sourceRef="Activity_022cdbo" targetRef="Activity_0vd3ese" />
    <bpmn:sequenceFlow id="Flow_0f7ga3i" sourceRef="Activity_08l6jvu" targetRef="Activity_0c1uma3" />
    <bpmn:sequenceFlow id="Flow_0q1rp61" sourceRef="Activity_0c1uma3" targetRef="Gateway_110l2fy" />
    <bpmn:sequenceFlow id="Flow_1nvo3iy" sourceRef="Gateway_1uxdjtt" targetRef="Activity_08l6jvu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_SI_PDF == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0lpwrp3" sourceRef="Activity_0sacem3" targetRef="Gateway_1uxdjtt" />
    <bpmn:sequenceFlow id="Flow_1a69l3s" sourceRef="Gateway_1szo20y" targetRef="Activity_0sacem3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_SO_Status_Valid==true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05mlnv4" sourceRef="Event_1xiu7ke" targetRef="Activity_15r9cd9" />
    <bpmn:exclusiveGateway id="Gateway_1bhgjta">
      <bpmn:incoming>Flow_0305q5x</bpmn:incoming>
      <bpmn:outgoing>Flow_0k5q6oz</bpmn:outgoing>
      <bpmn:outgoing>Flow_047eqxs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0305q5x" sourceRef="Activity_15r9cd9" targetRef="Gateway_1bhgjta" />
    <bpmn:sequenceFlow id="Flow_0k5q6oz" sourceRef="Gateway_1bhgjta" targetRef="Activity_0jkjfnl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Add_Instance_To_SI==true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_144pihl" sourceRef="Activity_0k5o5ui" targetRef="Activity_0c4to0p" />
    <bpmn:sequenceFlow id="Flow_1ssrpni" sourceRef="Activity_0c4to0p" targetRef="Event_0dyhwrq" />
    <bpmn:sequenceFlow id="Flow_047eqxs" sourceRef="Gateway_1bhgjta" targetRef="Activity_0568h44">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Add_Instance_To_SI==false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0jkjfnl" name="Create transaction" camunda:type="external" camunda:topic="Create_Transaction">
      <bpmn:incoming>Flow_0k5q6oz</bpmn:incoming>
      <bpmn:outgoing>Flow_0vtjv47</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0568h44" name="Status SI :&#10;Transaction Failed" camunda:type="external" camunda:topic="SI_Status_Transaction_Failed">
      <bpmn:incoming>Flow_047eqxs</bpmn:incoming>
      <bpmn:incoming>Flow_1c50y7x</bpmn:incoming>
      <bpmn:outgoing>Flow_15kiilt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0k5o5ui" name="Log-Error: Transaction Failed" camunda:type="external" camunda:topic="SI_Log_Error_Transaction_Failed">
      <bpmn:incoming>Flow_1g1vis9</bpmn:incoming>
      <bpmn:outgoing>Flow_144pihl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0c4to0p" name="Send Notification SA + Admin : Transaction Failed" camunda:type="external" camunda:topic="SI_Notification_Transaction_Failed">
      <bpmn:incoming>Flow_144pihl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ssrpni</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0g7xjo0">
      <bpmn:incoming>Flow_0vtjv47</bpmn:incoming>
      <bpmn:outgoing>Flow_1c50y7x</bpmn:outgoing>
      <bpmn:outgoing>Flow_03xy34e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vtjv47" sourceRef="Activity_0jkjfnl" targetRef="Gateway_0g7xjo0" />
    <bpmn:sequenceFlow id="Flow_1tyeohr" sourceRef="Activity_0f9rpuw" targetRef="Gateway_1szo20y" />
    <bpmn:sequenceFlow id="Flow_1c50y7x" sourceRef="Gateway_0g7xjo0" targetRef="Activity_0568h44">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_Transaction==false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_0f9rpuw" name="Status SO :Valid" camunda:type="external" camunda:topic="SO_Status_Valid">
      <bpmn:incoming>Flow_10qosdc</bpmn:incoming>
      <bpmn:outgoing>Flow_1tyeohr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ijh2vd" sourceRef="Gateway_1szo20y" targetRef="Activity_022cdbo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_SO_Status_Valid==false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0dyhwrq">
      <bpmn:incoming>Flow_1ssrpni</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_145s4kq" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0ea41rf">
      <bpmn:incoming>Flow_08z61ty</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_00ij9b8" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03xy34e" sourceRef="Gateway_0g7xjo0" targetRef="Activity_185mqey">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_Transaction==true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10qosdc" sourceRef="Activity_185mqey" targetRef="Activity_0f9rpuw" />
    <bpmn:serviceTask id="Activity_185mqey" name="Status SI : Transaction Succeeded&#10;" camunda:type="external" camunda:topic="SI_Status_Transaction_Succeeded">
      <bpmn:incoming>Flow_03xy34e</bpmn:incoming>
      <bpmn:outgoing>Flow_10qosdc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_15kiilt" sourceRef="Activity_0568h44" targetRef="Activity_0a2tyre" />
    <bpmn:sequenceFlow id="Flow_1g1vis9" sourceRef="Activity_0a2tyre" targetRef="Activity_0k5o5ui" />
    <bpmn:serviceTask id="Activity_0a2tyre" name="Status SO:&#10;Invalid" camunda:type="external" camunda:topic="SO_Status_Invalid">
      <bpmn:incoming>Flow_15kiilt</bpmn:incoming>
      <bpmn:outgoing>Flow_1g1vis9</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_SalesInvoice_Management">
      <bpmndi:BPMNShape id="Activity_07vqsvq_di" bpmnElement="Activity_1wast8y">
        <dc:Bounds x="2590" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13jdmgy_di" bpmnElement="Activity_1n5m1ax">
        <dc:Bounds x="2290" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sfxyxl_di" bpmnElement="Activity_1b0s858">
        <dc:Bounds x="2400" y="223" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rcdxpd_di" bpmnElement="Activity_1bqbpu7">
        <dc:Bounds x="2240" y="223" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yw1mwk_di" bpmnElement="Activity_1rv5vxk">
        <dc:Bounds x="2080" y="83" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15gd16f_di" bpmnElement="Activity_022cdbo">
        <dc:Bounds x="1780" y="83" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1t8wo6l_di" bpmnElement="Activity_0vd3ese">
        <dc:Bounds x="1930" y="83" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_138e665_di" bpmnElement="Activity_0c1uma3">
        <dc:Bounds x="1980" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_110l2fy_di" bpmnElement="Gateway_110l2fy" isMarkerVisible="true">
        <dc:Bounds x="2135" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ka4oe3_di" bpmnElement="Activity_08l6jvu">
        <dc:Bounds x="1810" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uxdjtt_di" bpmnElement="Gateway_1uxdjtt" isMarkerVisible="true">
        <dc:Bounds x="1705" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0apoqqh_di" bpmnElement="Activity_0sacem3">
        <dc:Bounds x="1440" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cu3sza" bpmnElement="Event_1xiu7ke">
        <dc:Bounds x="152" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1rgm8r9_di" bpmnElement="Event_0te5nqr">
        <dc:Bounds x="2822" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1szo20y_di" bpmnElement="Gateway_1szo20y" isMarkerVisible="true">
        <dc:Bounds x="1275" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mtsphg_di" bpmnElement="Activity_15r9cd9">
        <dc:Bounds x="260" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1bhgjta_di" bpmnElement="Gateway_1bhgjta" isMarkerVisible="true">
        <dc:Bounds x="415" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05v8a5z_di" bpmnElement="Activity_0jkjfnl">
        <dc:Bounds x="580" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17fhdtp_di" bpmnElement="Activity_0568h44">
        <dc:Bounds x="890" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ha4d6w_di" bpmnElement="Activity_0k5o5ui">
        <dc:Bounds x="1290" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dz5phh_di" bpmnElement="Activity_0c4to0p">
        <dc:Bounds x="1500" y="480" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g7xjo0_di" bpmnElement="Gateway_0g7xjo0" isMarkerVisible="true">
        <dc:Bounds x="775" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oh6ydp_di" bpmnElement="Activity_0f9rpuw">
        <dc:Bounds x="1070" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1446m99_di" bpmnElement="Event_0dyhwrq">
        <dc:Bounds x="1672" y="502" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0di51zn_di" bpmnElement="Event_0ea41rf">
        <dc:Bounds x="2232" y="105" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yj3dod_di" bpmnElement="Activity_185mqey">
        <dc:Bounds x="890" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01ldd14_di" bpmnElement="Activity_0a2tyre">
        <dc:Bounds x="1070" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1djmybl_di" bpmnElement="Flow_1djmybl">
        <di:waypoint x="2390" y="370" />
        <di:waypoint x="2590" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dpnstt_di" bpmnElement="Flow_0dpnstt">
        <di:waypoint x="2500" y="263" />
        <di:waypoint x="2545" y="263" />
        <di:waypoint x="2545" y="340" />
        <di:waypoint x="2590" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b3ua8z_di" bpmnElement="Flow_1b3ua8z">
        <di:waypoint x="2690" y="370" />
        <di:waypoint x="2822" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bncapp_di" bpmnElement="Flow_0bncapp">
        <di:waypoint x="2185" y="370" />
        <di:waypoint x="2290" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gpf44f_di" bpmnElement="Flow_0gpf44f">
        <di:waypoint x="2340" y="263" />
        <di:waypoint x="2400" y="263" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sw8bqf_di" bpmnElement="Flow_1sw8bqf">
        <di:waypoint x="2160" y="345" />
        <di:waypoint x="2160" y="263" />
        <di:waypoint x="2240" y="263" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03vxsi6_di" bpmnElement="Flow_03vxsi6">
        <di:waypoint x="2030" y="123" />
        <di:waypoint x="2080" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08z61ty_di" bpmnElement="Flow_08z61ty">
        <di:waypoint x="2180" y="123" />
        <di:waypoint x="2232" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aebetc_di" bpmnElement="Flow_1aebetc">
        <di:waypoint x="1730" y="345" />
        <di:waypoint x="1730" y="123" />
        <di:waypoint x="1780" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18p28f0_di" bpmnElement="Flow_18p28f0">
        <di:waypoint x="1880" y="123" />
        <di:waypoint x="1930" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f7ga3i_di" bpmnElement="Flow_0f7ga3i">
        <di:waypoint x="1910" y="370" />
        <di:waypoint x="1980" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q1rp61_di" bpmnElement="Flow_0q1rp61">
        <di:waypoint x="2080" y="370" />
        <di:waypoint x="2135" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nvo3iy_di" bpmnElement="Flow_1nvo3iy">
        <di:waypoint x="1755" y="370" />
        <di:waypoint x="1810" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lpwrp3_di" bpmnElement="Flow_0lpwrp3">
        <di:waypoint x="1540" y="370" />
        <di:waypoint x="1705" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a69l3s_di" bpmnElement="Flow_1a69l3s">
        <di:waypoint x="1325" y="370" />
        <di:waypoint x="1440" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05mlnv4_di" bpmnElement="Flow_05mlnv4">
        <di:waypoint x="188" y="370" />
        <di:waypoint x="260" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0305q5x_di" bpmnElement="Flow_0305q5x">
        <di:waypoint x="360" y="370" />
        <di:waypoint x="415" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k5q6oz_di" bpmnElement="Flow_0k5q6oz">
        <di:waypoint x="465" y="370" />
        <di:waypoint x="580" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_144pihl_di" bpmnElement="Flow_144pihl">
        <di:waypoint x="1390" y="520" />
        <di:waypoint x="1500" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ssrpni_di" bpmnElement="Flow_1ssrpni">
        <di:waypoint x="1600" y="520" />
        <di:waypoint x="1672" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_047eqxs_di" bpmnElement="Flow_047eqxs">
        <di:waypoint x="440" y="395" />
        <di:waypoint x="440" y="520" />
        <di:waypoint x="890" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vtjv47_di" bpmnElement="Flow_0vtjv47">
        <di:waypoint x="680" y="370" />
        <di:waypoint x="775" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tyeohr_di" bpmnElement="Flow_1tyeohr">
        <di:waypoint x="1170" y="370" />
        <di:waypoint x="1275" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c50y7x_di" bpmnElement="Flow_1c50y7x">
        <di:waypoint x="800" y="395" />
        <di:waypoint x="800" y="520" />
        <di:waypoint x="890" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ijh2vd_di" bpmnElement="Flow_1ijh2vd">
        <di:waypoint x="1300" y="345" />
        <di:waypoint x="1300" y="123" />
        <di:waypoint x="1780" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03xy34e_di" bpmnElement="Flow_03xy34e">
        <di:waypoint x="825" y="370" />
        <di:waypoint x="890" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10qosdc_di" bpmnElement="Flow_10qosdc">
        <di:waypoint x="990" y="370" />
        <di:waypoint x="1070" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15kiilt_di" bpmnElement="Flow_15kiilt">
        <di:waypoint x="990" y="520" />
        <di:waypoint x="1070" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g1vis9_di" bpmnElement="Flow_1g1vis9">
        <di:waypoint x="1170" y="520" />
        <di:waypoint x="1290" y="520" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
