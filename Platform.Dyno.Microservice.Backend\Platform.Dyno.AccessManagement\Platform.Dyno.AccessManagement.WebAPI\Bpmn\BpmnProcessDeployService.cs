﻿using Platform.Dyno.AccessManagement.Business.Bpmn;

namespace Platform.Dyno.AccessManagement.WebAPI.Bpmn
{
    public class BpmnProcessDeployService : IHostedService
    {
        private readonly BpmnService bpmnService;

        public BpmnProcessDeployService(BpmnService bpmnService)
        {
            this.bpmnService = bpmnService;
        }


        public async Task StartAsync(CancellationToken cancellationToken)
        {
          /* await bpmnService.CleanupProcessInstances("Process_SalesOrder_Management");
            await bpmnService.CleanupProcessInstances("Process_SalesInvoice_Management");

            await bpmnService.DeployProcessDefinition();*/
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
