﻿using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Image;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace Platform.Dyno.AccessManagement.Business.Service.Reporting
{
    public class InvoiceDocument : IDocument
    {
        public InvoiceModel Model { get; }
        public byte[] _image;

        public InvoiceDocument(InvoiceModel model, byte[] image)
        {
            Model = model;
            _image = image;
        }

        public DocumentMetadata GetMetadata() => DocumentMetadata.Default;
        public void Compose(IDocumentContainer container)
        {
            container
                .Page(page =>
                {
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);

                    page.Footer().Background(Colors.Blue.Darken4).Height(30).Element(ComposeFooter);
                });
        }

        void ComposeFooter(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().Row(row =>
                {
                    row.RelativeItem().AlignRight().Column(column =>
                    {
                        column.Item().Text(Model.SellerAddress.Phone);
                    });

                    row.RelativeItem().AlignCenter().Column(column =>
                    {
                        column.Item().Text(text =>
                        {
                            text.CurrentPageNumber();
                            text.Span(" / ");
                            text.TotalPages();
                        });
                    });

                    row.RelativeItem().AlignLeft().Column(column =>
                    {
                        column.Item().Text(Model.SellerAddress.Email);
                    });
                });
            });
        }

        void ComposeHeader(IContainer container)
        {
            container.Column(column =>
            {
                column.Item().LineHorizontal(30).LineColor(Colors.Blue.Darken4);
                column.Spacing(10);
                
                column.Item().Row(row =>
                {
                    row.RelativeItem().PaddingLeft(50).Column(column =>
                    {
                        column.Item().Width(100).Image(_image);
                        column.Item().Text(text =>
                        {
                            text.Span($"Facture").FontSize(40).SemiBold();
                        });
                    });
                    row.Spacing(40);
                    row.RelativeItem().Component(new AddressComponent("VENDEUR :", Model.SellerAddress));
                    

                });

            });
        }

        void ComposeContent(IContainer container)
        {
            container.Padding(20).Column(column =>
            {
                column.Item().LineHorizontal(1);

                column.Spacing(10);

                column.Item().Row(row =>
                {
                    row.RelativeItem().Component(new AddressComponent("ACHETEUR :", Model.CustomerAddress));
                    row.ConstantItem(50);
                    row.Spacing(40);
                    row.RelativeItem().AlignRight().Column(column =>
                    {
                        column.Item().Text(text =>
                        {
                            text.Span($"FACTURE ").SemiBold();
                            text.EmptyLine();
                            text.Span($"# {Model.InvoiceNumber}");
                        });

                        column.Item().Text(text =>
                        {
                            text.Span("DATE D'EMISSION ").SemiBold();
                            text.EmptyLine();
                            text.Span($"{Model.IssueDate:d}");
                        });

                        column.Item().Text(text =>
                        {
                            text.Span("DATE D’ÉCHÉANCE ").SemiBold();
                            text.EmptyLine();
                            text.Span($"{Model.DueDate:d}");
                        });
                    });

                });

                column.Item().LineHorizontal(1);
                column.Item().AlignCenter().Text($"{Model.Title}").FontSize(40).SemiBold();

                column.Item().Element(ComposeTable);

                column.Item().PaddingRight(5).AlignRight().Text($"Sous-Total: {Model.TotalPrice}").SemiBold();
                column.Item().PaddingRight(5).AlignRight().Text($"Timbre: {1}").SemiBold();
                column.Item().PaddingRight(5).AlignRight().Text($"Total: {Model.TotalPrice + 1}").SemiBold();



                column.Item().PaddingTop(5).Text($"La facture est arrêtée à la somme : {Model.TotalPriceText}").SemiBold();
                column.Item().PaddingTop(10).Element(ComposeComments);

                column.Item().Row(row =>
                {
                    row.RelativeItem().Column(column =>
                    {
                        column.Item().Text(text =>
                        {
                            text.Span($"DETAILS DE PAIEMENT :").SemiBold();
                            text.EmptyLine();
                            text.Span($"Bank : {Model.paiementDetails.Bank}");
                            text.EmptyLine();
                            text.Span($"BIC : {Model.paiementDetails.BIC}");
                            text.EmptyLine();
                            text.Span($"IBAN : {Model.paiementDetails.IBAN}");
                        });
                    });

                });
            });
        }

        void ComposeTable(IContainer container)
        {
            var headerStyle = TextStyle.Default.SemiBold();

            container.Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(25);
                    columns.RelativeColumn(3);
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                    columns.RelativeColumn();
                });

                table.Header(header =>
                {
                    header.Cell().Text("#");
                    header.Cell().Text("Produit").Style(headerStyle);
                    header.Cell().Text("Quantité").Style(headerStyle);
                    header.Cell().AlignRight().Text("NET").Style(headerStyle);
                    header.Cell().AlignRight().Text("TVA").Style(headerStyle);
                    header.Cell().AlignRight().Text("Sous-Total").Style(headerStyle);

                    header.Cell().ColumnSpan(6).PaddingTop(5).BorderBottom(1).BorderColor(Colors.Black);
                });

                foreach (var item in Model.Items)
                {
                    var index = Model.Items.IndexOf(item) + 1;

                    table.Cell().Element(CellStyle).Text($"{index}");
                    table.Cell().Element(CellStyle).Text($"Abonnement Saas: {item.ProductType}");
                    table.Cell().Element(CellStyle).Text($"{item.Quantity}");
                    table.Cell().Element(CellStyle).AlignRight().Text($"{item.NET}");
                    table.Cell().Element(CellStyle).AlignRight().Text($"{item.VAT}");
                    table.Cell().Element(CellStyle).AlignRight().Text($"{item.Total}");

                    static IContainer CellStyle(IContainer container) => container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                }
            });
        }

        void ComposeComments(IContainer container)
        {
            container.ShowEntire().Background(Colors.Grey.Medium).Padding(10).Column(column =>
            {
                column.Spacing(5);
                column.Item().Text("Commentaires").FontSize(14).SemiBold();
                column.Item().Text(Model.Comments);
                
            });
        }
    }

    public class AddressComponent : IComponent
    {
        private string Title { get; }

        private CompanyDetails Company { get; }
        public AddressComponent(string title, CompanyDetails company)
        {
            Title = title;
            Company = company;
        }

        public void Compose(IContainer container)
        {
            container.ShowEntire().Column(column =>
            {
                column.Spacing(2);

                column.Item().Text(Title).SemiBold();

                column.Item().Text($"{Company.CompanyName} - {Company.RNE}");
                column.Item().Text(Company.Address);
                column.Item().Text(Company.Email);
                column.Item().Text(Company.Phone);
            });
        }
    }
}
