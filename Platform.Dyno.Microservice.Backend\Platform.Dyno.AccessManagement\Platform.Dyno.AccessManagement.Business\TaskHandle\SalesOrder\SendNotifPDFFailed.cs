﻿using Camunda.Worker;
using Camunda.Worker.Variables;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("Notification_PDF_Failed")]
    public class SendNotifPDFFailed : IExternalTaskHandler
    {
        private readonly ISignalRNotificationService _signalRNotificationService;
        private readonly ICompanyService _companyService;

        public SendNotifPDFFailed(ISignalRNotificationService signalRNotificationService,
            ICompanyService companyService)
        {
            _signalRNotificationService= signalRNotificationService;
            _companyService= companyService;
        }
        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

            var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
            SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
            //SalesOrderDTO salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

            if (salesOrderDTO != null) {
                CompanyDTO? company = _companyService.Get(salesOrderDTO.CompanyId).ObjectValue;
                if(company != null)
                {
                    SignalRNotificationDTO notificationDTO = new SignalRNotificationDTO
                    {
                        Title = "PDF Failed",
                        Message = "PDF Failed To generate in Sales order !",
                        SendToId = new List<CompanyDTO> { company }

                    };
                    ResponseAPI<SignalRNotificationDTO> notif = _signalRNotificationService.Create(notificationDTO, companyId: salesOrderDTO.CompanyId);
                }
                
            }

            var completeResult = new CompleteResult();
            return completeResult;
        }
    }
}
