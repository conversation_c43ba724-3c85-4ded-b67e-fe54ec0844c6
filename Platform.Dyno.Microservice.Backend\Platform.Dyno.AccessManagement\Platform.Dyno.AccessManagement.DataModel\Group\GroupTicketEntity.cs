﻿using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Group;
[Table("GroupTicket")]
public class GroupTicketEntity
{
    [ForeignKey("GroupId")]
    public Guid? GroupId { get; set; }
    public GroupEntity? Group { get; set; } 



    [ForeignKey("TicketId")]
    public Guid TicketId { get; set; }
    public TicketEntity? Ticket { get; set; }
}
