﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.Ticket;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.BusinessModel.Ticket;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Ticket
{
    public class TicketService: ITicketService
    {
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _ticketCacheKey = RedisCacheKey.TicketCacheKey;
        private readonly IUnitOfWork<TicketEntity> _ticketRepository;
        private readonly ICompanyService _companyService;

        public TicketService(IUnitOfWork<TicketEntity> ticketRepository, 
            ICompanyService companyService,
            IMapper mapper,
            IRedisCacheService cache)
        {
            _ticketRepository = ticketRepository;  
            _companyService = companyService;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<TicketDTO>> GetAll(Guid companyId)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<TicketDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<List<TicketDTO>> response = new ResponseAPI<List<TicketDTO>>();
            var ticketsDTO = _cache.GetData<List<TicketDTO>>(_ticketCacheKey+companyId.ToString());
            try
            {
                if (ticketsDTO == null || ticketsDTO.Count() == 0)
                {
                    var ticketsEntity = _ticketRepository.Repository.GetAll(tickets=>tickets.CompanyId==companyId);

                    var ticketsBM = _mapper.Map<List<TicketBM>>(ticketsEntity);
                    ticketsDTO = _mapper.Map<List<TicketDTO>>(ticketsBM);
                    response.StatusCode = ticketsDTO == null || ticketsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = ticketsDTO.OrderByDescending(c => c.Name).ToList();
                    _cache.SetData(_ticketCacheKey+companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<TicketDTO>> GetAll(Guid companyId,PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<TicketDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<TicketDTO>> response = new ResponseAPI<PagedList<TicketDTO>>();
            var ticketsDTO = _cache.GetData<List<TicketDTO>>(_ticketCacheKey + companyId.ToString());
            try
            {
                if (ticketsDTO == null || ticketsDTO.Count() == 0)
                {
                    ticketsDTO = GetAll(companyId).ObjectValue;
                    response.StatusCode = ticketsDTO == null || ticketsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = PagedList<TicketDTO>.ToGenericPagedList(ticketsDTO, pagedParameters);
                    _cache.SetData(_ticketCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<TicketDTO>> GetAllActive(Guid companyId,PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<TicketDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<TicketDTO>> response = new ResponseAPI<PagedList<TicketDTO>>();
            var ticketsDTO = _cache.GetData<List<TicketDTO>>(_ticketCacheKey + companyId.ToString());
            try
            {
                if (ticketsDTO == null || ticketsDTO.Count() == 0)
                {
                    ticketsDTO = GetAll(companyId)?.ObjectValue?.Where(ticket => ticket.Status == Shared.Enum.Status.Active).ToList();
                    if (ticketsDTO == null)
                    {
                        response.StatusCode = HttpStatusCode.NotFound;
                        response.ExceptionMessage = "No active ticket found ";
                        return response;
                    }
                    response.StatusCode = ticketsDTO == null || ticketsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = PagedList<TicketDTO>.ToGenericPagedList(ticketsDTO, pagedParameters);
                    _cache.SetData(_ticketCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<List<TicketDTO>> GetTicketsByType(Guid companyId, WalletType ticketType)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<TicketDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<List<TicketDTO>> response = new ResponseAPI<List<TicketDTO>>();
            var ticketsDTO = _cache.GetData<List<TicketDTO>>(_ticketCacheKey + companyId.ToString());
            try
            {
                if (ticketsDTO == null || ticketsDTO.Count() == 0)
                {
                    var ticketsEntity = _ticketRepository.Repository.GetAll(ticket => ticket.CompanyId == companyId && ticket.Type== ticketType);

                    var ticketsBM = _mapper.Map<List<TicketBM>>(ticketsEntity);
                    ticketsDTO = _mapper.Map<List<TicketDTO>>(ticketsBM);
                    response.StatusCode = ticketsDTO == null || ticketsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = ticketsDTO.OrderByDescending(c => c.Name).ToList();
                    _cache.SetData(_ticketCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<TicketDTO> Get(Guid companyId, Guid id)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<TicketDTO> response = new ResponseAPI<TicketDTO>();
            try
            {
                var ticketDTO = GetAll(companyId).ObjectValue?.Where(ticket => ticket.TicketId == id).FirstOrDefault();
                response.StatusCode = ticketDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = ticketDTO;
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<List<TicketDTO>> Get(Guid companyId, Func<TicketDTO, bool> expression)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<TicketDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var ticketsDTO = GetAll(companyId).ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<TicketDTO>>
            {
                StatusCode = ticketsDTO == null || ticketsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK,
                ExceptionMessage = ticketsDTO == null || ticketsDTO.Count() == 0 ? $"tickets Not Found !" : null,
                ObjectValue = ticketsDTO
            };
        }
        #endregion

        #region Create
        public ResponseAPI<TicketDTO> Create(Guid companyId, TicketDTO ticketDTO, Guid? creatorUserId = null, bool updateCache = true)
        {
            if (ticketDTO.IsAutomatic==true && ticketDTO.StartDate == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"Start Date should have a value."
                };
            }
            else if (ticketDTO.IsAutomatic==true && ticketDTO.StartDate != null)
            {
                var ValidDate = checkDateValidity(ticketDTO.StartDate, ticketDTO.EndDate);
                if (!ValidDate)
                {
                    return new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = $"Start Date or End Date is incorrect."
                    };
                }
            }
            if (!checkTotalAmount(ticketDTO.Amount, ticketDTO.Quantity, ticketDTO.TotalAmount))
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"Total amount is incorrect."
                };
            }
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            #region Refdata
            ticketDTO.CreationTime = DateTime.Now.ToUniversalTime();
            ticketDTO.CreatorUserId = creatorUserId;
            ticketDTO.Status = ticketDTO.Status;
            #endregion
            ticketDTO.TicketId = Guid.NewGuid();
            ticketDTO.CompanyId = companyId;
            
            TicketBM ticketBM = _mapper.Map<TicketBM>(ticketDTO);
            TicketEntity ticketEntity = _mapper.Map<TicketEntity>(ticketBM);
            _ticketRepository.Repository.Insert(ticketEntity);
            _ticketRepository.Save();
            ticketDTO.TicketId = ticketEntity.TicketId;
            if (updateCache)
                _cache.RemoveData(_ticketCacheKey + companyId.ToString());
            return new ResponseAPI<TicketDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = ticketDTO
            };
        }

        #endregion

        #region Update
        public ResponseAPI<TicketDTO> Update(Guid companyId, TicketDTO ticketDTO, Guid? updateUserId = null, bool updateCache = true)
        {
            if (ticketDTO.IsAutomatic == true && ticketDTO.StartDate == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"Start Date should have a value."
                };
            }
            else if (ticketDTO.IsAutomatic == true && ticketDTO.StartDate != null)
            {
                var ValidDate = checkDateValidity(ticketDTO.StartDate, ticketDTO.EndDate);
                if (!ValidDate)
                {
                    return new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = $"Start Date or End Date is incorrect."
                    };
                }
            }
            if (!checkTotalAmount(ticketDTO.Amount, ticketDTO.Quantity, ticketDTO.TotalAmount))
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"Total amount is incorrect."
                };
            }
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var ticketExistDTO = Get(companyId, ticketDTO.TicketId).ObjectValue;
            if (ticketExistDTO != null)
            {
                #region Refdata
                RefDataService<TicketDTO>.UpdateRefData(ticketDTO, updateUserId);
                ticketDTO.CreatorUserId = ticketExistDTO.CreatorUserId;
                ticketDTO.CreationTime = ticketExistDTO.CreationTime;
                #endregion
                ticketDTO.CompanyId = companyId;
                TicketBM ticketBM = _mapper.Map<TicketBM>(ticketDTO);
                TicketEntity ticketEntity = _mapper.Map<TicketEntity>(ticketBM);
                _ticketRepository.Repository.Update(ticketEntity);
                _ticketRepository.Save();

                if (updateCache)
                    _cache.RemoveData(_ticketCacheKey + companyId.ToString());
                return new ResponseAPI<TicketDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = ticketDTO
                };
            }

            return new ResponseAPI<TicketDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {ticketDTO.TicketId} Not Found !"
            };
        }

        #endregion

        #region Delete
        public ResponseAPI<TicketDTO> Delete(Guid companyId, Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<TicketDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            TicketDTO? ticketDTO = Get(companyId,id)?.ObjectValue;
            if (ticketDTO != null)
            {
                TicketBM ticketBM = _mapper.Map<TicketBM>(ticketDTO);
                TicketEntity ticketEntity = _mapper.Map<TicketEntity>(ticketBM);
                _ticketRepository.Repository.Delete(ticketEntity.TicketId);
                _ticketRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_ticketCacheKey + companyId.ToString());
                }
                return new ResponseAPI<TicketDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = ticketDTO
                };
            }
            return new ResponseAPI<TicketDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {ticketDTO?.TicketId} Not Found !"
            };
        }
        #endregion

       private bool checkDateValidity(DateTime? StartDate,DateTime? EndDate)
        {
            if (EndDate == null)
            {
                return true;
            }
            else if (EndDate > StartDate)
            {
                return true;    
            }
            return false;
        }
        private bool checkTotalAmount(double amount,int quantity,double totalAmount)
        {
            var calculatedTotalAmount = amount * quantity;
            if (Math.Abs(calculatedTotalAmount-totalAmount)>0.01)
            {
                return false;
            }
            return true;
        }


    }
}
