﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Group;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.WebAPI.Controllers.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;

using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Group;
[Route("Api/[controller]")]
[ApiController]
public class GroupController : ControllerBase
{
    private readonly IGroupService _groupService;
    private readonly ILogger<CompanyController> _logger;
    private readonly ISortingUtility _sortingUtility;
    public GroupController(IGroupService groupService, ILogger<CompanyController> logger, ISortingUtility sortingUtility)
    {
        _groupService = groupService;   
        _logger = logger;
        _sortingUtility = sortingUtility;
    }

    #region Get
    [Route("GetAll")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult GetAll()
    {
        try
        {
            string? companyId = HttpContext.User.FindFirstValue("Company");
            if (companyId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            string? userType = HttpContext.User.FindFirstValue("UserType");
            if (userType == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            if (Enum.TryParse(userType, out UserType enumUserType))
            {
                ResponseAPI<List<GroupDTO>> groups = _groupService.GetAll(new Guid(companyId), enumUserType);
                return Ok(groups);
            }

            return Unauthorized(new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized !"
            });
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
            ResponseAPI<List<GroupDTO>> response = new ResponseAPI<List<GroupDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
            };
            return StatusCode(500, response);
        }
    }
    [Route("GetAllPaged")]
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult GetAllPaged([FromBody] PagedParameters pagedParameters)
    {
        try
        {
            string? companyId = HttpContext.User.FindFirstValue("Company");
            if (companyId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            string? userType = HttpContext.User.FindFirstValue("UserType");
            if (userType == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            if (Enum.TryParse(userType, out UserType enumUserType))
            {
                ResponseAPI<PagedList<GroupDTO>> groupsDTO = _groupService.GetAll(pagedParameters, new Guid(companyId), enumUserType);
                if(groupsDTO.ObjectValue != null)
                {


                    //groupsDTO.ObjectValue = _groupService.filterData( pagedParameters, groupsDTO);
                    //groupsDTO.ObjectValue = _groupService.sortData( pagedParameters, groupsDTO);
                    groupsDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, groupsDTO);
                    groupsDTO.ObjectValue = _sortingUtility.FilterData(pagedParameters, groupsDTO);


                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = groupsDTO.ObjectValue.TotalCount,
                        PageSize = groupsDTO.ObjectValue.PageSize,
                        CurrentPage = groupsDTO.ObjectValue.CurrentPage,
                        TotalPages = groupsDTO.ObjectValue.TotalPages,
                        HasNext = groupsDTO.ObjectValue.HasNext,
                        HasPrevious = groupsDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(groupsDTO);
            }

            return Unauthorized(new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized !"
            });
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
            return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

        }
    }
    [Route("GetAllActive")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult GetAllActive([FromQuery] PagedParameters pagedParameters)
    {
        try
        {
            string? companyId = HttpContext.User.FindFirstValue("Company");
            if (companyId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            string? userType = HttpContext.User.FindFirstValue("UserType");
            if (userType == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            if (Enum.TryParse(userType, out UserType enumUserType))
            {
                ResponseAPI<PagedList<GroupDTO>> groupsDTO = _groupService.GetAllActive(pagedParameters, new Guid(companyId), enumUserType);
                if (groupsDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = groupsDTO.ObjectValue.TotalCount,
                        PageSize = groupsDTO.ObjectValue.PageSize,
                        CurrentPage = groupsDTO.ObjectValue.CurrentPage,
                        TotalPages = groupsDTO.ObjectValue.TotalPages,
                        HasNext = groupsDTO.ObjectValue.HasNext,
                        HasPrevious = groupsDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(groupsDTO);
            }

            return Unauthorized(new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized !"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActive)}");
            return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

        }
    }
    [Route("Get/{id}")]
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult Get(Guid id)
    {
        try
        {
            ResponseAPI<GroupDTO> companyDTO = _groupService.Get(id);
            return Ok(companyDTO);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
            return BadRequest(new ResponseAPI<GroupDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = ex.InnerException?.Message
            });
        }

    }
    #endregion

    #region Create
    [Route("Create")]
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult Create([FromBody] GroupDTO groupDTO)
    {
        try
        {
            string? creatorUserId = HttpContext.User.FindFirstValue("Id");
            if (creatorUserId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            string? companyId = HttpContext.User.FindFirstValue("Company");
            if (companyId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            string? userType = HttpContext.User.FindFirstValue("UserType");
            if (userType == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            if (Enum.TryParse(userType, out UserType enumUserType))
            {
                ResponseAPI<GroupDTO> response = _groupService.Create(groupDTO, creatorUserId:new Guid(creatorUserId), companyId:new Guid(companyId), userType:enumUserType);
                return Ok(response);
            }

            return Unauthorized(new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized !"
            });
            
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
            return BadRequest(new ResponseAPI<GroupDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = ex.InnerException?.Message
            });
        }
    }
    #endregion

    #region Update
    [Route("Update")]
    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult Update([FromBody] GroupDTO groupDTO)
    {
        try
        {
            string? updateUserId = HttpContext.User.FindFirstValue("Id");
            if (updateUserId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            ResponseAPI<GroupDTO> response = _groupService.Update(groupDTO, updateUserId: new Guid(updateUserId));
            return Ok(response);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
            return BadRequest(new ResponseAPI<GroupDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = ex.InnerException?.Message
            });
        }
    }
    #endregion

    #region Delete
    [Route("Delete/{id}")]
    [HttpDelete]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public IActionResult Delete(Guid id)
    {
        try
        {
            string? deletedUserId = HttpContext.User.FindFirstValue("Id");
            if (deletedUserId == null)
            {
                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }

            ResponseAPI<GroupDTO> response = _groupService.Delete(id, deletorUserId:new Guid(deletedUserId));
            return Ok(response);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
            return BadRequest(new ResponseAPI<GroupDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = ex.InnerException?.Message
            });
        }
    }
    #endregion
}
