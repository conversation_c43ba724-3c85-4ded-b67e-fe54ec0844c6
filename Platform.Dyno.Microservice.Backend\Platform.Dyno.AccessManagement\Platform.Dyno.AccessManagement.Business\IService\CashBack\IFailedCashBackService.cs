﻿using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.CashBack
{
    public interface IFailedCashBackService
    {
        public ResponseAPI<List<FailedCashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin);
        public ResponseAPI<PagedList<FailedCashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters);
        public ResponseAPI<FailedCashBackDTO> Create(Guid companyId, bool isSuperAdmin, FailedCashBackDTO cashbackDTO, Guid creatorUserId, bool updateCache = true);

    }
}

