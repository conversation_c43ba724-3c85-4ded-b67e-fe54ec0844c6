﻿using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.SalesOrder
{
    public class FailedSalesOrderBM : ReferentialData
    {
        public Guid Id { get; set; }
        public Guid SalesOrderId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public SalesOrderBM? SalesOrder { get; set; }
        public new SalesOrderStatus Status { get; set; } = SalesOrderStatus.Cancelled;
        
    }
}
