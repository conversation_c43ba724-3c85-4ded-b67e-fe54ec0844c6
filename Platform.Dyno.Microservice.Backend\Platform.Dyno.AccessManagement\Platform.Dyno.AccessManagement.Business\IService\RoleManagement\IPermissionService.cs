﻿using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.RoleManagement
{
    public interface IPermissionService : IGenericService<PermissionDTO>
    {
        ResponseAPI<List<PermissionDTO>> GetList(Func<PermissionDTO, bool> expression);
        public ResponseAPI<List<PermissionDTO>> GetAllAvailablePermissionsByUser(UserDTO user, bool updateCache = true);
        public List<PermissionDTO>? CreatePermissionList(List<PermissionEntity> permissionEntities);
        public List<PermissionEntity>? CreatePermissionList(List<PermissionEntity>? permissionEntities, Guid? roleId, UserDTO user);
        public ResponseAPI<PermissionDTO> DeleteAllPermissionInRole(Guid id, Guid? deletorUserId = null, bool updateCache = true);
    }
}
