﻿using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.User
{
    public class UserOTPBM : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public int Code { get; set; }
        public bool IsConfirmed { get; set; }
        #endregion

        #region Structure
        public UserBM? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion


        public void GenerateCode()
        {
            Random random = new Random();
            this.Code = random.Next(1000, 10000);
        }
    }
}
