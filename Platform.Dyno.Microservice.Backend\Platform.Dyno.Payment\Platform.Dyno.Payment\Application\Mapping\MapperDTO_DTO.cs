﻿using Application.Models.BMs;
using AutoMapper;
using Platform.Dyno.Payment.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Mapping
{
    public class MapperDTO_DTO : Profile
    {
        public MapperDTO_DTO()
        {
            CreateMap<PreTransactionDTO, TransactionDTO>().ReverseMap();
        }
    }
}
