﻿using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.SalesOrder
{
    public class SalesOrderBM:ReferentialData
    {
        public Guid Id { get; set; }
        public string ProcessInstanceId { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double DynoAmount { get; set; }
        public Currency Currency { get; set; }
        public double NetAmount { get; set; }
        public double VATAmount { get; set; }
        public double TotalAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public ProductType ProductType { get; set; }
        public DateTime Date { get; set; }
        public new SalesOrderStatus Status { get; set; }
        #region Structure
        public Guid SalesOrderId { get; set; }
        public SalesOrderBM? SalesOrder { get; set; }
        public Guid? DocumentId { get; set; }
        public DocumentsBM? Document { get; set; }
        public Guid CompanyId { get; set; }
        public CompanyBM? Company { get; set; }
        #endregion
    }
}
