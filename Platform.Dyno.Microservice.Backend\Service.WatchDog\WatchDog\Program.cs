using Amazon.Runtime;
using Amazon.S3.Model;
using Amazon.S3;
using DynoTools;
using Newtonsoft.Json;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared.KafkaMessage;
using Serilog;
using Server.Kafka;
using WatchDog;
using Amazon;

var configurationKafka = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
    .Build();

LoggerConfiguration LogConfig = new LoggerConfiguration();
var builder = Host.CreateDefaultBuilder(args);
builder.UseSerilog();

#region Kafka 
var config = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
    .Build();

var s3Client = new AmazonS3Client(new BasicAWSCredentials(config["AWSAccessKey"], config["AWSPrivateKey"]), RegionEndpoint.USEast1);
var getObjectRequest = new GetObjectRequest
    {
        BucketName = config["AWSBucketName"] ?? "",
        Key = config["Key"]
    };
using (var response = s3Client.GetObjectAsync(getObjectRequest).Result)
{
    using (var streamReader = new StreamReader(response.ResponseStream))
    {
        var content = await streamReader.ReadToEndAsync();
        var jsonData = JsonConvert.DeserializeObject<Platform.Dyno.Shared.Configuration.Config>(content);

        builder.ConfigureServices((hostContext, services) =>
        {
            Log.Logger = LogConfig.LoggerConfig().CreateLogger();
            services.AddSingleton<IProducer<HeartBeatMessage>>(producer => new Producer<HeartBeatMessage>(Topic.TOPIC_WATCHDOG_SEND_MESSAGE, jsonData.KafkaServer));
            services.AddSingleton<IConsumer<HeartBeatMessage>>(consumer => new Consumer<HeartBeatMessage>(Topic.TOPIC_WATCHDOG_RECEIVE_MESSAGE, jsonData.KafkaServer, jsonData.KafkaGroupId));
            services.AddHostedService<WatchDogWorker>();

            services.AddSingleton<IProducer<WatchDogNotifDTO>>(producer => new Producer<WatchDogNotifDTO>(Topic.TOPIC_WATCHDOG_SEND_NOTIFICATION, jsonData.KafkaServer));
        });
    }
}
#endregion
      
using var host = builder.Build();
host.Run();


