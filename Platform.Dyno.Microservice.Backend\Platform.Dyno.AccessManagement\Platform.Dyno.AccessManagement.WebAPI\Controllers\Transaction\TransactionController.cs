﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.Transaction;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using System.Net;
using System.Security.Claims;
using static Camunda.Api.Client.Filter.FilterInfo;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Transaction
{
    [Route("Api/[controller]")]
    [ApiController]
    public class TransactionController : ControllerBase
    {
        private readonly ITransactionService _transactionService;
        private readonly IUserService _userService;
        private readonly ILogger<TransactionController> _logger;
        private readonly IHelper<TransactionDTO> _helper;
        private readonly IHelper<double> _helperForDouble;
        private readonly IMapper _mapper;
        private readonly Platform.Dyno.Shared.Configuration _configuration;
        private readonly IHelper<TransactionUserDTO> _transactionUserHelper;
        public TransactionController(ITransactionService transactionService, ILogger<TransactionController> logger,
            IHelper<TransactionDTO> helper,
            IHelper<double> helperForDouble,
            Platform.Dyno.Shared.Configuration configuration,
            IMapper mapper,
            IHelper<TransactionUserDTO> transactionUserHelper,
            IUserService userService)
        {
            _transactionService = transactionService;
            _mapper = mapper;
            _helper = helper;
            _helperForDouble = helperForDouble;
            _logger = logger;
            _configuration = configuration;
            _transactionUserHelper = transactionUserHelper;
            _userService = userService;
        }

        #region Get 
        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetAllPaged";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "order", "DESC" },
                        {"limit",pagedParameters.PageSize.ToString() },
                        {"skip",pagedParameters.PageNumber.ToString() }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    Ok(response);

                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/Get";

                ResponseAPI<TransactionDTO>? response = _helper.Get(Url, id);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else
                {
                    if (response?.StatusCode == HttpStatusCode.OK)
                    {
                        return Ok(response);
                    }
                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<RoleDTO> response = new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetCashiersTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCashiersTransactions([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<List<CashierTransactionsDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<List<CashierTransactionsDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if(enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<List<CashierTransactionsDTO>>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<List<CashierTransactionsDTO>> transactions = _transactionService.GetCashiersTransactions(new Guid(userId), pagedParameters);
                    if (transactions?.StatusCode == HttpStatusCode.OK)
                    {
                        return Ok(transactions);
                    }
                    return BadRequest(transactions);
                }

                return Unauthorized(new ResponseAPI<List<CashierTransactionsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<CashierTransactionsDTO>> response = new ResponseAPI<List<CashierTransactionsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        #region get company transactions
        [Route("GetCompanyReceivedTransactionsTotalAmountPerMonth")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCompanyReceivedTransactionsTotalAmountPerMonth()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<double>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }

                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetCompanyReceivedTransactionsTotalAmountPerMonth/{companyId}";

                ResponseAPI<double>? response = _helperForDouble.Get(Url);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<double>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else
                {
                    if (response?.StatusCode == HttpStatusCode.OK)
                    {
                        return Ok(response);
                    }
                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<double> response = new ResponseAPI<double>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("GetCompanySendedTransactionsTotalAmountPerMonth")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCompanySendedTransactionsTotalAmountPerMonth()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }

                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetCompanySendedTransactionsTotalAmountPerMonth/{companyId}";
                ResponseAPI<double>? response = _helperForDouble.Get(Url);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<double>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else
                {
                    if (response?.StatusCode == HttpStatusCode.OK)
                    {
                        return Ok(response);
                    }
                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<double> response = new ResponseAPI<double>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Get By Wallet
        [Route("GetWalletReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletReceivedTransactions(Guid walletId)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetWalletReceivedTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"walletId", walletId.ToString()},
                        {"sort","TransactionDate" },
                        { "order", "DESC" }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetWalletReceivedTransactions)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        [Route("GetWalletSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetSentTransactionsByWallet(Guid walletId)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetWalletSentTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"walletId", walletId.ToString()},
                        {"sort","TransactionDate" },
                        { "order", "DESC" }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetSentTransactionsByWallet)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        [Route("GetWalletTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletTransactions(Guid walletId)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetWalletTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"walletId", walletId.ToString()},
                        {"sort","TransactionDate" },
                        { "order", "DESC" }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetSentTransactionsByWallet)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Get By User
        [Route("GetUserReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserReceivedTransactions([FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserReceivedTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "userId", userId }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        
        [Route("GetUserSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserSentTransactions([FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserSentTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "userId", userId }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        
        [Route("GetUserTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserTransactions([FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "userId", userId }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<TransactionDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetHistoryReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetHistoryReceivedTransactions([FromQuery] PagedParameters pagedParameters, [FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = _transactionService.GetHistoriqueReceiverTransactions(new Guid(userId), pagedParameters.PageSize, pagedParameters.PageNumber);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetHistoryReceivedTransactions)}");
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetHistorySendedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetHistorySendedTransactions([FromQuery] PagedParameters pagedParameters, [FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = _transactionService.GetHistoriqueSenderTransactions(new Guid(userId), pagedParameters.PageSize, pagedParameters.PageNumber);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetHistorySendedTransactions)}");
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetHistoryTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetHistoryTransactions([FromQuery] PagedParameters pagedParameters, [FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = _transactionService.GetHistoriqueTransactions(new Guid(userId), pagedParameters.PageSize, pagedParameters.PageNumber);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetHistoryTransactions)}");
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetHistoryTransactionsByWalletType/{walletType}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetHistoryTransactionsByWalletType([FromHeader] string authorization, [FromQuery] PagedParameters pagedParameters, WalletType walletType)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = _transactionService.GetHistoriqueTransactionsByWalletType(new Guid(userId), walletType, pagedParameters.PageSize, pagedParameters.PageNumber);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetHistoryTransactionsByWalletType)}");
                ResponseAPI<List<HistoriqueTransactionDetailsDTO>> response = new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromHeader] string authorization,[FromBody] TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/Create";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"pinCode", pinCode}
                    };
                ResponseAPI<TransactionDTO>? response = _helper.Post(Url, transactionDTO, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("TransactionClientToCashier")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult TransactionClientToCashier([FromHeader] string authorization, [FromBody] TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<bool>? response = _transactionService.TransactionClientToCashier(transactionDTO, pinCode, new Guid(userId));
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<bool>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("CreatePreTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreatePreTransaction([FromHeader] string authorization, [FromBody] PreTransactionDTO preTransactionDTO, string pinCode)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                var checkPinCode = _userService.CheckUserPinCode(new Guid (userId),pinCode);
                if (checkPinCode == null || checkPinCode.ObjectValue == false)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "Wrong pin code !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/CreatePreTransaction";

                var transactionDTO = _mapper.Map<TransactionDTO>(preTransactionDTO);
                ResponseAPI<TransactionDTO>? response = _helper.Post(Url, transactionDTO);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("CreateUniqueQrCodeTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateUniqueQrCodeTransaction([FromHeader] string authorization, [FromBody] UniqueQRCodeTransactionDTO uniqueQRCodeTransactionDTO, string pinCode)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<TransactionDTO> transaction = _transactionService.CreateUniqueQrCodeTransaction(uniqueQRCodeTransactionDTO, pinCode, new Guid(userId));
                if (transaction.StatusCode != HttpStatusCode.Created)
                {
                    return BadRequest(transaction);
                }
                return Ok(transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion


        #region Send Ticket

        [Route("SendTicketToGroup")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult SendTicketToGroup([FromHeader] string authorization, Guid groupId,[FromBody] List<TicketDTO> ticketsId)
        {
            try
            {
                string companyId = HttpContext.User.FindFirstValue("Company");
                if(companyId == null)
                {
                    return Unauthorized(
                        new ResponseAPI<TransactionDTO>()
                        {
                            StatusCode = System.Net.HttpStatusCode.NotFound,
                            ExceptionMessage = "user not authorized"
                        });

                }
                string userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    var Response = new ResponseAPI<TransactionDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return Unauthorized(Response);
                }
                var transactions = _transactionService.CreateTransactionForGroup(new Guid(companyId), new Guid(userId), groupId, ticketsId);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("SendMultipleTicketToGroups")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult SendMultipleTicketToGroups([FromHeader] string authorization, List<GroupTicketDTO> groupTickets)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TransactionDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var transactions = _transactionService.SendMultipleTicketsToGroups(companyId, userId, groupTickets);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("SendTicketsToEmployees")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult SendTicketsToEmployees([FromHeader] string authorization, List<EmployeeTicketDTO> employeeTickets)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TransactionDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var transactions = _transactionService.SendTicketsToEmployees(companyId, userId, employeeTickets);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

    }
}