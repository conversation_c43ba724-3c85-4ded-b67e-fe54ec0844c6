# 🧪 SCRIPT DE TEST AUTOMATISÉ - SERVICE DE TRANSACTION DISTRIBUÉE
# Platform Dyno - Tests de validation

Write-Host "🚀 DÉMARRAGE DES TESTS DE TRANSACTION DISTRIBUÉE" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Configuration des URLs
$AuthUrl = "http://localhost:7274/api"
$PaymentUrl = "http://localhost:7018/api"

# Variables globales
$Token = ""
$TestResults = @()

# Fonction pour afficher les résultats
function Write-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Details = ""
    )
    
    $Color = if ($Status -eq "PASS") { "Green" } elseif ($Status -eq "FAIL") { "Red" } else { "Yellow" }
    Write-Host "[$Status] $TestName" -ForegroundColor $Color
    if ($Details) {
        Write-Host "    $Details" -ForegroundColor Gray
    }
    
    $TestResults += @{
        Test = $TestName
        Status = $Status
        Details = $Details
        Timestamp = Get-Date
    }
}

# Fonction pour faire un appel API
function Invoke-ApiCall {
    param(
        [string]$Method,
        [string]$Url,
        [hashtable]$Headers = @{},
        [object]$Body = $null
    )
    
    try {
        $params = @{
            Method = $Method
            Uri = $Url
            Headers = $Headers
            ContentType = "application/json"
        }
        
        if ($Body) {
            $params.Body = $Body | ConvertTo-Json -Depth 10
        }
        
        $response = Invoke-RestMethod @params
        return @{
            Success = $true
            Data = $response
            StatusCode = 200
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = $_.Exception.Response.StatusCode.value__
        }
    }
}

Write-Host "`n🔐 ÉTAPE 1: AUTHENTIFICATION" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Test d'authentification
$loginData = @{
    phoneNumber = "+33123456789"
    password = "TestPassword123!"
    countryCode = "+33"
}

$authResult = Invoke-ApiCall -Method "POST" -Url "$AuthUrl/AuthClient/login" -Body $loginData

if ($authResult.Success) {
    $Token = $authResult.Data.objectValue.token
    Write-TestResult -TestName "Authentification utilisateur" -Status "PASS" -Details "Token obtenu avec succès"
} else {
    Write-TestResult -TestName "Authentification utilisateur" -Status "FAIL" -Details $authResult.Error
    Write-Host "❌ Impossible de continuer sans authentification" -ForegroundColor Red
    exit 1
}

$authHeaders = @{
    "Authorization" = "Bearer $Token"
}

Write-Host "`n💰 ÉTAPE 2: VÉRIFICATION DES WALLETS" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Récupérer les wallets
$walletsResult = Invoke-ApiCall -Method "GET" -Url "$PaymentUrl/Wallet/GetMyWallets" -Headers $authHeaders

if ($walletsResult.Success) {
    $wallets = $walletsResult.Data.objectValue
    Write-TestResult -TestName "Récupération des wallets" -Status "PASS" -Details "$(($wallets | Measure-Object).Count) wallet(s) trouvé(s)"
    
    if ($wallets.Count -ge 2) {
        $SenderWalletId = $wallets[0].id
        $ReceiverWalletId = $wallets[1].id
        Write-Host "    Sender Wallet: $SenderWalletId" -ForegroundColor Gray
        Write-Host "    Receiver Wallet: $ReceiverWalletId" -ForegroundColor Gray
    } else {
        Write-TestResult -TestName "Wallets suffisants pour test" -Status "FAIL" -Details "Au moins 2 wallets requis"
        exit 1
    }
} else {
    Write-TestResult -TestName "Récupération des wallets" -Status "FAIL" -Details $walletsResult.Error
    exit 1
}

# Vérifier le solde total
$balanceResult = Invoke-ApiCall -Method "GET" -Url "$PaymentUrl/Wallet/GetTotalBalance" -Headers $authHeaders

if ($balanceResult.Success) {
    $initialBalance = $balanceResult.Data.objectValue
    Write-TestResult -TestName "Vérification solde initial" -Status "PASS" -Details "Solde: $initialBalance"
} else {
    Write-TestResult -TestName "Vérification solde initial" -Status "FAIL" -Details $balanceResult.Error
}

Write-Host "`n🔄 ÉTAPE 3: TESTS DE TRANSACTION DISTRIBUÉE" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan

# Test 1: Transaction normale
Write-Host "`n📝 Test 1: Transaction normale (50.00)" -ForegroundColor Yellow

$transactionData = @{
    senderWalletId = $SenderWalletId
    receiverWalletId = $ReceiverWalletId
    amount = 50.00
    description = "Test transaction distribuée normale"
    transactionDate = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
}

$transactionResult = Invoke-ApiCall -Method "POST" -Url "$PaymentUrl/DistributedTransaction/ExecuteSecure?pinCode=1234" -Headers $authHeaders -Body $transactionData

if ($transactionResult.Success) {
    Write-TestResult -TestName "Transaction distribuée normale" -Status "PASS" -Details "Montant: 50.00"
} else {
    Write-TestResult -TestName "Transaction distribuée normale" -Status "FAIL" -Details $transactionResult.Error
}

# Test 2: Fonds insuffisants
Write-Host "`n📝 Test 2: Fonds insuffisants (999999.00)" -ForegroundColor Yellow

$transactionData.amount = 999999.00
$transactionData.description = "Test fonds insuffisants"

$transactionResult = Invoke-ApiCall -Method "POST" -Url "$PaymentUrl/DistributedTransaction/ExecuteSecure?pinCode=1234" -Headers $authHeaders -Body $transactionData

if (!$transactionResult.Success -and $transactionResult.StatusCode -eq 400) {
    Write-TestResult -TestName "Gestion fonds insuffisants" -Status "PASS" -Details "Erreur 400 attendue"
} else {
    Write-TestResult -TestName "Gestion fonds insuffisants" -Status "FAIL" -Details "Devrait retourner une erreur 400"
}

# Test 3: Wallet inexistant
Write-Host "`n📝 Test 3: Wallet inexistant" -ForegroundColor Yellow

$transactionData.senderWalletId = "00000000-0000-0000-0000-000000000000"
$transactionData.amount = 25.00
$transactionData.description = "Test wallet inexistant"

$transactionResult = Invoke-ApiCall -Method "POST" -Url "$PaymentUrl/DistributedTransaction/ExecuteSecure?pinCode=1234" -Headers $authHeaders -Body $transactionData

if (!$transactionResult.Success -and $transactionResult.StatusCode -eq 404) {
    Write-TestResult -TestName "Gestion wallet inexistant" -Status "PASS" -Details "Erreur 404 attendue"
} else {
    Write-TestResult -TestName "Gestion wallet inexistant" -Status "FAIL" -Details "Devrait retourner une erreur 404"
}

# Test 4: PIN incorrect
Write-Host "`n📝 Test 4: PIN incorrect" -ForegroundColor Yellow

$transactionData.senderWalletId = $SenderWalletId
$transactionData.amount = 25.00
$transactionData.description = "Test PIN incorrect"

$transactionResult = Invoke-ApiCall -Method "POST" -Url "$PaymentUrl/DistributedTransaction/ExecuteSecure?pinCode=0000" -Headers $authHeaders -Body $transactionData

if (!$transactionResult.Success -and $transactionResult.StatusCode -eq 400) {
    Write-TestResult -TestName "Gestion PIN incorrect" -Status "PASS" -Details "Erreur 400 attendue"
} else {
    Write-TestResult -TestName "Gestion PIN incorrect" -Status "FAIL" -Details "Devrait retourner une erreur 400"
}

Write-Host "`n📊 ÉTAPE 4: VÉRIFICATION POST-TESTS" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Vérifier le solde final
$finalBalanceResult = Invoke-ApiCall -Method "GET" -Url "$PaymentUrl/Wallet/GetTotalBalance" -Headers $authHeaders

if ($finalBalanceResult.Success) {
    $finalBalance = $finalBalanceResult.Data.objectValue
    $difference = $initialBalance - $finalBalance
    Write-TestResult -TestName "Vérification solde final" -Status "PASS" -Details "Solde final: $finalBalance (Différence: $difference)"
} else {
    Write-TestResult -TestName "Vérification solde final" -Status "FAIL" -Details $finalBalanceResult.Error
}

# Vérifier les transactions récentes
$transactionsResult = Invoke-ApiCall -Method "GET" -Url "$PaymentUrl/Transaction/GetAllPaged?pageSize=5&pageNumber=1" -Headers $authHeaders

if ($transactionsResult.Success) {
    $transactions = $transactionsResult.Data.objectValue
    Write-TestResult -TestName "Récupération transactions récentes" -Status "PASS" -Details "$(($transactions | Measure-Object).Count) transaction(s) récente(s)"
} else {
    Write-TestResult -TestName "Récupération transactions récentes" -Status "FAIL" -Details $transactionsResult.Error
}

Write-Host "`n📋 RÉSUMÉ DES TESTS" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

$passCount = ($TestResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalCount = $TestResults.Count

Write-Host "✅ Tests réussis: $passCount" -ForegroundColor Green
Write-Host "❌ Tests échoués: $failCount" -ForegroundColor Red
Write-Host "📊 Total: $totalCount" -ForegroundColor White

if ($failCount -eq 0) {
    Write-Host "`n🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!" -ForegroundColor Green
    Write-Host "Le service de transaction distribuée fonctionne correctement." -ForegroundColor Green
} else {
    Write-Host "`n⚠️  CERTAINS TESTS ONT ÉCHOUÉ" -ForegroundColor Yellow
    Write-Host "Vérifiez les détails ci-dessus et corrigez les problèmes identifiés." -ForegroundColor Yellow
}

Write-Host "`n🔍 PROCHAINES ÉTAPES RECOMMANDÉES:" -ForegroundColor Cyan
Write-Host "- Tester avec de vrais wallets et transactions" -ForegroundColor White
Write-Host "- Effectuer des tests de charge" -ForegroundColor White
Write-Host "- Simuler des pannes blockchain pour tester la compensation" -ForegroundColor White
Write-Host "- Vérifier les logs de transaction dans Neo4j" -ForegroundColor White
Write-Host "- Tester les timeouts de réservation (5 minutes)" -ForegroundColor White

Write-Host "`n✅ Tests terminés à $(Get-Date)" -ForegroundColor Green
