﻿using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Role
{
    public class PermissionEntity : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        [Required]
        public string Name { get; set; } = string.Empty;
        public bool IsLeaf { get; set; } = false;
        public bool IsRoot { get; set; } = false;
        public bool? PartialSelected { get; set; }
        #endregion

        #region Structure
        [ForeignKey("RoleId")]
        public RoleEntity? Role { get; set; }
        public Guid? RoleId { get; set; }
        public ICollection<PermissionEntity>? Permissions { get; set;}

        [ForeignKey("PermissionEntityId")]
        public Guid? PermissionEntityId { get; set; }
        #endregion

    }
}
