﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="EnyimMemcachedCore" />
    <PackageReference Include="EPPlus" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="libphonenumber-csharp" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" />
    <PackageReference Include="Microsoft.AspNetCore.Http" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" />
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="Microsoft.Extensions.WebEncoders" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Npgsql" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="protobuf-net.Core" />
    <PackageReference Include="QRCoder" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="System.Net.Http" />
    <PackageReference Include="System.Security.Cryptography.Xml" />
    <PackageReference Include="System.Text.Json" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Cache\MemCache\" />
    <Folder Include="SharedClass\Security\" />
  </ItemGroup>
</Project>