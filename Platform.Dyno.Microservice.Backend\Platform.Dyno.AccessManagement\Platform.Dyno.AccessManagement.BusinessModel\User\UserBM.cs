﻿using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.BusinessModel.User
{
    public class UserBM : ReferentialData
    {
        #region User Identity Data
        public Guid Id { get; set; }
        public string? FullName { get; set; }
        public string Email { get; set; } = string.Empty;
        public string? NormalizedEmail { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string NormalizedUserName { get; set; } = string.Empty;
        public bool EmailConfirmed { get; set; } = false;
        public string PhoneNumber { get; set; } = string.Empty;
        public bool PhoneNumberConfirmed { get; set; }
        public string Password { get; set; } = string.Empty;
        public string PinCode { get; set; } = string.Empty;
        public UserType UserType { get; set; }
        #endregion

        #region User Data
        public string? Picture { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string CountryCode { get; set; }

        #endregion

        #region User Security
        public string? FCMToken { get; set; }
        public AuthentificationSource AuthentificationSource { get; set; }
        public string SecurityStamp { get; set; } = string.Empty;
        public string ConcurrencyStamp { get; set; } = string.Empty;

        public DateTimeOffset? LockoutEnd { get; set; }

        public bool LockoutEnabled { get; set; }
        public int AccessFailedCount { get; set; }
        #endregion

        #region Structure
        public IList<RoleBM>? Roles { get; set; }
        public IList<AddressBM>? Addresses { get; set; }
        public ICollection<MacAddressBM>? MacAddresses { get; set; }
        public Guid CompanyId { get; set; }
        #endregion
    }
}
