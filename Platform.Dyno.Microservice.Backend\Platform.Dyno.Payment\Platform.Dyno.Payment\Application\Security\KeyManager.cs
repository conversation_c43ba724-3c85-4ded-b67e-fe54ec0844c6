﻿using Platform.Dyno.Shared.SharedClass.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Application.Security
{
    public static class KeyManager
    {
        public static Keys GenerateSecuredKeys(string pinCode)
        {

            Tuple<byte[], byte[]> generatedKeys =KeyGenerator.GenerateKeyPair();
            var hashedPinCode = SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(pinCode));
            var iv = hashedPinCode.Take(16).ToArray();
            var key = hashedPinCode.TakeLast(16).ToArray();
            var hashedKey = SecurityProvider.HashKeyTo256Bits(key);
            var privateKey = generatedKeys.Item1;
            var publicKey = generatedKeys.Item2;
            var encryptedPrivateKey = SecurityProvider.EncryptKey(privateKey, hashedKey, iv);
            var keys = new Keys()
            {
                EncryptedPrivateKey = encryptedPrivateKey,
                HashedPrivateKey = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(privateKey)),
                PublicKey = Convert.ToBase64String(publicKey),
            };
            return keys;
        }
        public static string DecryptAndHashPrivateKey(string encryptedPrivateKey,string pinCode)
        {
            var hashedPinCode = SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(pinCode));
            var iv = hashedPinCode.Take(16).ToArray();
            var key = hashedPinCode.TakeLast(16).ToArray();
            var hashedKey = SecurityProvider.HashKeyTo256Bits(key);
            var decryptedPrivateKey= SecurityProvider.DecryptKey(Convert.FromBase64String(encryptedPrivateKey), hashedKey, iv);
            return Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Convert.FromBase64String(decryptedPrivateKey)));
        }

    }
}
