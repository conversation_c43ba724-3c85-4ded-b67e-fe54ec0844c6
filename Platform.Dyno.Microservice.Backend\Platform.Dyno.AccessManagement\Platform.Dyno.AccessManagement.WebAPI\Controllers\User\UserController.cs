﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.Security;
using System.Security.Claims;
using System.Text;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("Api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;
        private readonly ISortingUtility _sortingUtility;
        public UserController(IUserService userService, 
            ILogger<UserController> logger,
            ISortingUtility sortingUtility)
        {
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        //[Authorize("View All Users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<UserDTO>> users = _userService.GetAll(new Guid(companyId), enumUserType);
                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>> 
                { 
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500,  response);
            }

        }
        [Route("GetAllActive")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllActive([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<UserDTO>> users = _userService.GetAllActive(pagedParameters, new Guid(companyId), enumUserType);

                    if (users.ObjectValue != null)
                    {
                        users.ObjectValue = _sortingUtility.SortData(pagedParameters, users);
                        users.ObjectValue = _sortingUtility.FilterData(pagedParameters, users);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = users.ObjectValue.TotalCount,
                            PageSize = users.ObjectValue.PageSize,
                            CurrentPage = users.ObjectValue.CurrentPage,
                            TotalPages = users.ObjectValue.TotalPages,
                            HasNext = users.ObjectValue.HasNext,
                            HasPrevious = users.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActive)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPagedUsers")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<UserDTO>> users = _userService.GetAll(pagedParameters, new Guid(companyId), enumUserType);

                    if (users.ObjectValue != null)
                    {
                        users.ObjectValue = _sortingUtility.SortData(pagedParameters, users);
                        users.ObjectValue = _sortingUtility.FilterData(pagedParameters, users);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = users.ObjectValue.TotalCount,
                            PageSize = users.ObjectValue.PageSize,
                            CurrentPage = users.ObjectValue.CurrentPage,
                            TotalPages = users.ObjectValue.TotalPages,
                            HasNext = users.ObjectValue.HasNext,
                            HasPrevious = users.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<UserDTO> user = _userService.Get(id);
                return Ok(user);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetUserProfile")]
        [HttpGet]
        /*[Authorize("Role." + DefaultRoles.Client + "," + "Permission." + "View All Users")]*/
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserProfile()
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }
                ResponseAPI<UserProfileDTO> userProfile = _userService.GetUserProfile(new Guid(userId));
                return Ok(userProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the GetUserProfile");
                ResponseAPI<UserProfileDTO> response = new ResponseAPI<UserProfileDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred: " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        [Route("GetUsersCreatedByAdmin")]
        [HttpGet]
        /*[Authorize("Role." + DefaultRoles.Client + "," + "Permission." + "View All Users")]*/
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUsersCreatedByAdmin([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<UserDTO>> userDTO = _userService.GetUsersCreatedByAdmin(pagedParameters, new Guid(userId), new Guid(companyId), enumUserType);
                    if(userDTO.ObjectValue != null)
                    {
                        userDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, userDTO);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = userDTO.ObjectValue.TotalCount,
                            PageSize = userDTO.ObjectValue.PageSize,
                            CurrentPage = userDTO.ObjectValue.CurrentPage,
                            TotalPages = userDTO.ObjectValue.TotalPages,
                            HasNext = userDTO.ObjectValue.HasNext,
                            HasPrevious = userDTO.ObjectValue.HasPrevious,

                        };
                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }
                    
                    return Ok(userDTO);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUsersCreatedByAdmin)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);
            }

        }
        [Route("CheckUserPinCode")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CheckUserPinCode(string pinCode)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }
                ResponseAPI<bool> response = _userService.CheckUserPinCode(new Guid (userId),pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CheckUserPinCode)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);
            }

        }

        [Route("GetAllUsersByRole/{roleId}")]
        [HttpGet]
        //[Authorize("View All Users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllUsersByRole(Guid roleId)
        {
            try
            {
                ResponseAPI<List<UserDTO>> users = _userService.GetAllByRole(roleId);
                return Ok(users);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        [Route("GetAllUsersByDefaultRole/{defaultRole}")]
        [HttpGet]
        //[Authorize("View All Users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllUsersByDefaultRole(string defaultRole)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<UserDTO>> users = _userService.GetAllByDefaultRole(defaultRole, new Guid(companyId));
                return Ok(users);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllUsersByDefaultRole)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }


        [Route("GetCashiers")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCashiers([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if(enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<MacAddressDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<List<UserDTO>> users = _userService.Get(user => user.UserType == UserType.Cashier &&
                                                                        user.Status == Status.Active &&
                                                                        user.CompanyId == new Guid(companyId));
                    ResponseAPI<PagedList<UserDTO>> response = new ResponseAPI<PagedList<UserDTO>>();
                    if (users.ObjectValue != null)
                    {
                        response.ObjectValue = PagedList<UserDTO>.ToGenericPagedList(users.ObjectValue, pagedParameters);
                        response.ObjectValue = _sortingUtility.SortData(pagedParameters, response);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = response.ObjectValue.TotalCount,
                            PageSize = response.ObjectValue.PageSize,
                            CurrentPage = response.ObjectValue.CurrentPage,
                            TotalPages = response.ObjectValue.TotalPages,
                            HasNext = response.ObjectValue.HasNext,
                            HasPrevious = response.ObjectValue.HasPrevious,

                        };
                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }
                    response.StatusCode = users.StatusCode;
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetUserByPhoneNumber/{phoneNumber}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserIdByPhoneNumber(string phoneNumber)
        {
            try
            {
                ResponseAPI<UserDTO> user = _userService.GetUserByPhoneNumber(phoneNumber);
                ResponseAPI<UserDTO>response = new ResponseAPI<UserDTO>();
                if (user.ObjectValue != null)
                {
                    response.ObjectValue = user.ObjectValue;
                    response.StatusCode = user.StatusCode;
                      return Ok(response);
                }
                else
                {
                    return NotFound("User not found for the given phone number.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUserIdByPhoneNumber)}");
                ResponseAPI<UserDTO> response = new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        #endregion

        #region Create 
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] UserDTO user)
        {
            try
            {
                if(!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? createUserId = HttpContext.User.FindFirstValue("Id");

                if (createUserId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<UserDTO> response = _userService.Create(user, creatorUserId:new Guid(createUserId), companyId:new Guid(companyId), userType:enumUserType);
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Update
        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] UserDTO user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? updateUserId = HttpContext.User.FindFirstValue("Id");

               if (updateUserId == null)
               {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
               }

                ResponseAPI<UserDTO> response = _userService.Update(user, new Guid(updateUserId));
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("UpdateUserProfile")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateUserProfile([FromBody] UserProfileDTO userProfile)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }

                userProfile.Id = new Guid(userId);

                ResponseAPI<UserProfileDTO> updatedProfile = _userService.UpdateUserProfile(userProfile);
                return Ok(updatedProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the UpdateUserProfile");
                ResponseAPI<UserProfileDTO> response = new ResponseAPI<UserProfileDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred: " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                if (deleteUserId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<UserDTO> response = _userService.Delete(id, new Guid(deleteUserId));
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("HardDelete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> HardDelete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                if (deleteUserId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<UserDTO> response = await  _userService.Delete(id);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(HardDelete)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Hash pin code
        [Route("HashPinCode")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult HashPinCode(Guid pinCode)
        {
            try
            {
                var hashedPinCode = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(pinCode.ToString())));
                return Ok(hashedPinCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the GetUserProfile");
                ResponseAPI<UserProfileDTO> response = new ResponseAPI<UserProfileDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred: " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
        #endregion
    }
}
    