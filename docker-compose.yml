##version: '3.9'

services:
  payment-webapi:
    image: ${DOCKER_REGISTRY-}platformdynopaymentimage
    build:
      context: .
      dockerfile: Dockerfile-Payment
    container_name: paymentmanagementimage
    ports:
      - "7018:80"

  notification-webapi:
    image: ${DOCKER_REGISTRY-}platformdynonotificationimage
    build:
      context: .
      dockerfile: Dockerfile-Notif
    container_name: notificationmanagementimage
    ports:
      - "7038:80"

  accessmanagement-webapi:
    image: ${DOCKER_REGISTRY-}platformdynoaccessmanagementimage
    build:
      context: .
      dockerfile: ./Dockerfile-Access
    container_name: accessmanagementimage
    ports:
      - "7274:80"

  camunda:
    image: camunda/camunda-bpm-platform:latest
    container_name: camunda
    ports:
      - "8080:8080"