﻿using Domain.Enums.Query;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface IRefundTransactionService
    {
        #region Get
        public ResponseAPI<List<RefundTransactionDTO>> GetAll(string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);

        public ResponseAPI<RefundTransactionDTO> Get(Guid id);

        public ResponseAPI<List<RefundTransactionDTO>> GetUserSentTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);

        public ResponseAPI<List<RefundTransactionDTO>> GetUserReceivedTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        
        public ResponseAPI<List<RefundTransactionDTO>> GetUserTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        #endregion

        #region Create
        public ResponseAPI<RefundTransactionDTO> Create(RefundTransactionDTO transactionDTO);
        #endregion

        #region Update
        public ResponseAPI<RefundTransactionDTO> UpdateRefundStatus(Guid id, RefundStatus refundStatus);
        #endregion
    }
}
