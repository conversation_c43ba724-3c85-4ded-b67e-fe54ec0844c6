﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Platform.Dyno.AccessManagement.DataModel.Employee;

[Table("Employee")]
public class EmployeeEntity : ReferentialData
{
    public Guid EmployeeId { get; set; }

    [ForeignKey("UserId")]
    public Guid UserId { get; set; }
    public UserEntity? User { get; set; }

    [ForeignKey("CompanyId")]
    public Guid CompanyId { get; set; }
    [JsonIgnore]
    public CompanyEntity? Company { get; set; }
   
    [ForeignKey("GroupId")]
    public Guid GroupId { get; set; }
    public GroupEntity? Group { get; set; }
}
