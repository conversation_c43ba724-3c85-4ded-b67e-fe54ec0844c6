﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.Bpmn;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Linq;
using System.Net;

namespace Platform.Dyno.AccessManagement.Business.Service.SalesOrder
{
    public class SalesInvoiceService : ISalesInvoiceService
    {           
        private readonly IUnitOfWork<SalesInvoiceEntity> _salesInvoiceRepository;
        private readonly IMapper _mapper;
        private readonly ICompanyService _companyService;
        private readonly IRedisCacheService _cache;
        private readonly string _salesInvoiceCacheKey = RedisCacheKey.SalesInvoiceCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("sales invoice");
        private readonly BpmnService _bpmnService;

        public SalesInvoiceService(IUnitOfWork<SalesInvoiceEntity> salesInvoiceRepository,
            IMapper mapper,
            ICompanyService companyService,
            IRedisCacheService cache,
            BpmnService bpmnService)
        {
            _salesInvoiceRepository  = salesInvoiceRepository;
            _mapper = mapper;
            _companyService = companyService;
            _cache = cache;
            _bpmnService = bpmnService;
        }

        #region Get
        public ResponseAPI<List<SalesInvoiceDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if(userType == UserType.Cashier || userType == UserType.Client)
            {
                return new ResponseAPI<List<SalesInvoiceDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }
            ResponseAPI<List<SalesInvoiceDTO>> response = new ResponseAPI<List<SalesInvoiceDTO>>();
            var salesInvoicesDTO = _cache.GetData<List<SalesInvoiceDTO>>(_salesInvoiceCacheKey);
            try
            {
                if (salesInvoicesDTO == null || salesInvoicesDTO.Count() == 0)
                {
                    List<SalesInvoiceEntity> salesInvoicesEntity = (List<SalesInvoiceEntity>)_salesInvoiceRepository.Repository.GetAll(includes: new List<string> { "Document" });

                    var salesInvoicesBM = _mapper.Map<List<SalesInvoiceBM>>(salesInvoicesEntity);
                    salesInvoicesDTO = _mapper.Map<List<SalesInvoiceDTO>>(salesInvoicesBM);
                    
                    _cache.SetData(_salesInvoiceCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
                if(userType == UserType.Company || userType == UserType.ShopOwner)
                {
                    salesInvoicesDTO = salesInvoicesDTO.Where(o => o.CompanyId == companyId).ToList();
                }
                response.StatusCode = salesInvoicesDTO == null || salesInvoicesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = salesInvoicesDTO?.OrderByDescending(c => c.Code).ToList();
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }

        public ResponseAPI<PagedList<SalesInvoiceDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SalesInvoiceDTO>> invoicesDTO = GetAll(companyId, userType);
            PagedList<SalesInvoiceDTO>? pagedList = null;
            if (invoicesDTO.ObjectValue != null)
            {
                pagedList = PagedList<SalesInvoiceDTO>.ToGenericPagedList(invoicesDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<SalesInvoiceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<SalesInvoiceDTO> Get(Guid id)
        {
            SalesInvoiceDTO? salesInvoiceDTO = GetAll().ObjectValue?.Where(invoice => invoice.Id == id).FirstOrDefault();
            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = salesInvoiceDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = salesInvoiceDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = salesInvoiceDTO
            };
        }

        public ResponseAPI<List<SalesInvoiceDTO>> Get(Func<SalesInvoiceDTO, bool> expression)
        {
            List<SalesInvoiceDTO>? salesInvoicesDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<SalesInvoiceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = salesInvoicesDTO
            };
        }
        

        public ResponseAPI<PagedList<SalesInvoiceDTO>> GetByStatus(Guid companyId, bool isSuperAdmin, List<InvoiceStatus> invoiceStatus, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<SalesInvoiceDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<SalesInvoiceDTO>> response = new ResponseAPI<PagedList<SalesInvoiceDTO>>();
            var invoicesDTO = _cache.GetData<List<CashBackDTO>>(_salesInvoiceCacheKey + companyId.ToString());
            try
            {
                if (invoicesDTO == null || invoicesDTO.Count() == 0)
                {
                    IList<SalesInvoiceEntity> invoicesEntity;
                    if (isSuperAdmin == true)
                    {
                        invoicesEntity = _salesInvoiceRepository.Repository.GetAll(invoice =>  invoiceStatus.Contains(invoice.Status), includes: new List<string> { "Company", "Document" });
                    }
                    else
                    {
                        invoicesEntity = _salesInvoiceRepository.Repository.GetAll(invoice => invoice.CompanyId == companyId && invoiceStatus.Contains(invoice.Status), includes: new List<string> { "Company", "Document" });
                    }
                    var invoicesBM = _mapper.Map<List<SalesInvoiceBM>>(invoicesEntity);
                    var invoicessDTOList = _mapper.Map<List<SalesInvoiceDTO>>(invoicesBM);
                    PagedList<SalesInvoiceDTO> invoicesDTOPagedList = PagedList<SalesInvoiceDTO>.ToGenericPagedList(invoicessDTOList, pagedParameters);
                    response.StatusCode = HttpStatusCode.OK;
                    response.ObjectValue = invoicesDTOPagedList;
                    _cache.SetData(_salesInvoiceCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }

        public SalesInvoiceDTO? GetSalesInvoiceBySalesOrderId(Guid salesOrderId)
        {
            var response = Get(x=>x.SalesOrderId == salesOrderId)?.ObjectValue?.FirstOrDefault();
            return response;
        }
        #endregion

        #region Create
        public ResponseAPI<SalesInvoiceDTO> Create(SalesInvoiceDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            var lastSalesInvoice = _salesInvoiceRepository.Repository.Get(x=>x.Date.Date==DateTime.UtcNow.Date,orderBy: x => x.OrderByDescending(y => y.Date));
            if (lastSalesInvoice == null)
            {
                dtoObject.Code = "IN" + DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString("00") + DateTime.UtcNow.Day.ToString("00") + "-" + 1.ToString();
            }
            else
            {
                int lastSalesOrdernumber = int.Parse(lastSalesInvoice.Code.Split("-")[1]);
                dtoObject.Code = "IN" + DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString("00") + DateTime.UtcNow.Day.ToString("00") + "-" + (lastSalesOrdernumber + 1).ToString();
            }
            SalesInvoiceBM salesInvoice = _mapper.Map<SalesInvoiceBM>(dtoObject);

            #region changed data
            salesInvoice.Id = Guid.NewGuid();
            RefDataService<SalesInvoiceBM>.CreateRefData(salesInvoice, salesInvoice.Id);

            #endregion

            SalesInvoiceEntity salesInvoiceEntity = _mapper.Map<SalesInvoiceEntity>(salesInvoice);
            _salesInvoiceRepository.Repository.Insert(salesInvoiceEntity);
            _salesInvoiceRepository.Save();

            dtoObject.Id = salesInvoiceEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_salesInvoiceCacheKey);
            }

            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }
        #endregion

        #region Update
        public ResponseAPI<SalesInvoiceDTO> Update(SalesInvoiceDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                SalesInvoiceBM salesInvoice = _mapper.Map<SalesInvoiceBM>(dtoObject);

                #region RefData
                RefDataService<SalesInvoiceBM>.UpdateRefData(salesInvoice, salesInvoice.Id);
                #endregion

                SalesInvoiceEntity salesInvoiceEntity = _mapper.Map<SalesInvoiceEntity>(salesInvoice);
                _salesInvoiceRepository.Repository.Update(salesInvoiceEntity);
                _salesInvoiceRepository.Save();

                dtoObject.Id = salesInvoiceEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_salesInvoiceCacheKey);
                }

                return new ResponseAPI<SalesInvoiceDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }
        public ResponseAPI<SalesInvoiceDTO> UpdateStatus(Guid salesInvoiceId, InvoiceStatus salesInvoiceStatus, bool isSuperAdmin, Guid? companyId = null, Guid? updateUserId = null, bool updateCache = true, Guid? documentId = null)
        {
            if (companyId == null && isSuperAdmin == false)
            {
                return new ResponseAPI<SalesInvoiceDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }

            var salesInvoiceExistDTO = Get(salesInvoiceId).ObjectValue;
            if (salesInvoiceExistDTO != null)
            {
                if (documentId != null)
                {
                    salesInvoiceExistDTO.DocumentId = (Guid)documentId;
                }

                if (salesInvoiceExistDTO.Status != InvoiceStatus.Valid)
                {
                    #region Refdata
                    salesInvoiceExistDTO.Status = salesInvoiceStatus;
                    RefDataService<SalesInvoiceDTO>.UpdateRefData(salesInvoiceExistDTO, null);
                    #endregion
                    SalesInvoiceBM salesInvoiceBM = _mapper.Map<SalesInvoiceBM>(salesInvoiceExistDTO);
                    SalesInvoiceEntity salesInvoiceEntity = _mapper.Map<SalesInvoiceEntity>(salesInvoiceBM);
                    _salesInvoiceRepository.Repository.Update(salesInvoiceEntity);
                    _salesInvoiceRepository.Save();
                }

                if (updateCache)
                    _cache.RemoveData(_salesInvoiceCacheKey + salesInvoiceExistDTO.CompanyId.ToString());
                return new ResponseAPI<SalesInvoiceDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesInvoiceExistDTO
                };
            }

            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {salesInvoiceId} Not Found !"
            };

        }
        #endregion

        #region Delete
        public ResponseAPI<SalesInvoiceDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            SalesInvoiceDTO? salesInvoiceDto = Get(id).ObjectValue;
            if (salesInvoiceDto != null)
            {

                SalesInvoiceBM salesInvoice = _mapper.Map<SalesInvoiceBM>(salesInvoiceDto);

                #region RefData
                RefDataService<SalesInvoiceBM>.UpdateRefData(salesInvoice, salesInvoice.Id);
                #endregion

                SalesInvoiceEntity salesInvoiceEntity = _mapper.Map<SalesInvoiceEntity>(salesInvoice);
                _salesInvoiceRepository.Repository.Update(salesInvoiceEntity);
                _salesInvoiceRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_salesInvoiceCacheKey);
                }

                return new ResponseAPI<SalesInvoiceDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = salesInvoiceDto
                };
            }

            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.DeleteError
            };
        }

        public ResponseAPI<PagedList<SalesInvoiceDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            throw new NotImplementedException();
        }
        #endregion
        #region start process 
        public async Task<ResponseAPI<string>> StartProcess(SalesInvoiceDTO salesInvoiceDTO)
        {
            try
            {
                var processInstanceId = await _bpmnService.StartProcessForSalesInvoice(salesInvoiceDTO);
                salesInvoiceDTO.AssociateWithProcessInstance(processInstanceId);
                return new ResponseAPI<string>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesInvoiceDTO.ProcessInstanceId
                };

            }
            catch (Exception ex)
            {
                return new ResponseAPI<string>
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = ex.Message
                };

            }

        }
        #endregion
        #region add instance to si
        public ResponseAPI<SalesInvoiceDTO> AddInstanceToSI(Guid salesInvoiceId, string instantanceId)
        {
            var salesInvoiceExistDTO = Get(salesInvoiceId).ObjectValue;
            if (salesInvoiceExistDTO == null)
            {
                return new ResponseAPI<SalesInvoiceDTO>
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $" sales order with id {salesInvoiceId} is not found"

                };
            }
            salesInvoiceExistDTO.AssociateWithProcessInstance(instantanceId);
            SalesInvoiceBM salesInvoiceBM = _mapper.Map<SalesInvoiceBM>(salesInvoiceExistDTO);
            SalesInvoiceEntity salesInvoiceEntity = _mapper.Map<SalesInvoiceEntity>(salesInvoiceBM);
            _salesInvoiceRepository.Repository.Update(salesInvoiceEntity);
            _salesInvoiceRepository.Save();
            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = salesInvoiceExistDTO
            };
        }
        #endregion
    }
}
