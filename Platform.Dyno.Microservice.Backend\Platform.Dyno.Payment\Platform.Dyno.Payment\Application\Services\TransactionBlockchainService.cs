﻿using Application.Interfaces;
using Application.IServices;
using Application.Models.BMs;
using Application.Security;
using AutoMapper;
using Domain.Entities;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Z.BulkOperations;
using Z.Expressions;

namespace Application.Services
{
    public class TransactionBlockchainService : ITransactionBlockchainService
    {
        private readonly IGenericRepository _Repository;
        private readonly IMapper _mapper;
        private readonly ITransactionService _transactionService;
        private readonly IWalletService _walletService;
        private readonly IWalletBlockchainService _walletBlockchainService;
        private readonly Configuration _configuration;
        private readonly IHelper<TransactionDTO> _transactionHelper;
        private readonly IHelper<TransactionBlockchain> _transactionBlockchainHelper;
        private readonly IHelper<TransactionGroupBlockchain> _transactionGroupBlockchainHelper;
        private readonly ExceptionMessages exceptionMessage = new ExceptionMessages("transaction");

        public TransactionBlockchainService(IGenericRepository Repository, 
            IMapper mapper,
            IWalletService walletService,
            Configuration configuration,
            IHelper<TransactionDTO> transactionHelper,
            IHelper<TransactionGroupBlockchain> transactionGroupBlockchainHelper,
            IHelper<TransactionBlockchain> transactionBlockchainHelper,
            IWalletBlockchainService walletBlockchainService,
            ITransactionService transactionService)
        {
            _Repository = Repository;
            _mapper = mapper;
            _walletService = walletService;
            _configuration = configuration;
            _transactionHelper = transactionHelper;
            _transactionBlockchainHelper = transactionBlockchainHelper;
            _transactionGroupBlockchainHelper = transactionGroupBlockchainHelper;
            _walletBlockchainService = walletBlockchainService;
            _transactionService = transactionService;
        }

        #region Get

        public ResponseAPI<List<TransactionDTO>> GetAll()
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetAllTransactions";
            Console.WriteLine("endpoint : " + endPoint);
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<TransactionDTO> Get(Guid id)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetTransaction";
            ResponseAPI<TransactionDTO>? blockchainResponse = _transactionHelper.Get(endPoint, id);
            if (blockchainResponse != null)
                return blockchainResponse;

            return new ResponseAPI<TransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Wallet id : {id} not found !"
            };
        }

        #region Get By User
        public ResponseAPI<List<TransactionDTO>> GetUserReceivedTransactions(Guid userId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetUserReceivedTransactions/{userId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<List<TransactionDTO>> GetUserSentTransactions(Guid userId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetUserSentTransactions/{userId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<List<TransactionDTO>> GetUserTransactions(Guid userId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetUserTransactions/{userId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }
        #endregion


        #region Get By Wallet
        public ResponseAPI<List<TransactionDTO>> GetWalletReceivedTransactions(Guid walletId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetWalletReceivedTransactions/{walletId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<List<TransactionDTO>> GetWalletSentTransactions(Guid walletId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetWalletSentTransactions/{walletId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<List<TransactionDTO>> GetWalletTransactions(Guid walletId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetWalletTransactions/{walletId}";
            ResponseAPI<List<TransactionDTO>>? blockchainResponse = _transactionHelper.GetAll(endPoint);
            List<TransactionDTO> walletsDtO = new List<TransactionDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }
        #endregion

        #endregion

        #region Create

        public ResponseAPI<TransactionDTO> Create(TransactionDTO transactionDTO, Guid userId, string pinCode)
        {
            
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            var senderWalletBlockchain = _walletBlockchainService.Get(transactionBM.SenderWalletId).ObjectValue;
            
            if (senderWallet == null && senderWalletBlockchain == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }

            if (senderWallet.Status != WalletStatus.Active)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }

            if (senderWalletBlockchain?.Balance < transactionDTO.Amount)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }


            var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }
            
            var senderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWallet.PrivateKey, pinCode.ToString());
            var receiverPublicKey = receiverWallet.PublicKey;

            if (transactionBM != null)
            {
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $ReceiverId})
                                CREATE (sender)-[t:Transaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus
                                    } 
                                ]->(receiver)
                                SET 
                                sender.Balance = sender.Balance - $Amount,
                                receiver.Balance = receiver.Balance + $Amount
                                RETURN t as t;";
                transactionBM.Id = Guid.NewGuid();
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.SenderWalletId.ToString()  },
                    { "ReceiverId",transactionBM.ReceiverWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() },
                    { "RefundStatus", RefundStatus.None.ToString() }
                };
           
                var transaction = new TransactionBlockchain
                {
                    Id = transactionBM.Id,
                    SenderPrivateKey = senderPrivateKey,
                    ReceiverPublicKey = receiverPublicKey,
                    Amount = transactionBM.Amount,
                    QrCodeId = transactionBM.QrCodeId,
                    TransactionDate = transactionBM.TransactionDate,
                    Status = TransactionStatus.succeeded
                };
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/Transaction";
                ResponseAPI<TransactionBlockchain>? blockchainResponse = _transactionBlockchainHelper.Post(endPoint, transaction);
                
                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK )
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = exceptionMessage.CreateError
                    };
                }

                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessage.CreateError
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessage.CreateError
                };
            }
        }

        public ResponseAPI<TransactionDTO> TransactionClientShop(TransactionDTO transactionDTO, Guid userId, string pinCode)
        {
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            var senderWalletBlockchain = _walletBlockchainService.Get(transactionBM.SenderWalletId).ObjectValue;


            if (senderWallet == null || 
                senderWalletBlockchain == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }

            if (senderWallet.Status != WalletStatus.Active)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }

            if (senderWalletBlockchain?.Balance < transactionDTO.Amount)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = exceptionMessage.CreateError,
                };
            }


            var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }

            var senderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWallet.PrivateKey, pinCode.ToString());
            var receiverPublicKey = receiverWallet.PublicKey;

            if (transactionBM != null)
            {
                transactionBM.Id = Guid.NewGuid();
                transactionBM.TransactionDate = DateTime.UtcNow;
                var transaction = new TransactionBlockchain
                {
                    Id = transactionBM.Id,
                    SenderPrivateKey = senderPrivateKey,
                    ReceiverPublicKey = receiverPublicKey,
                    Amount = transactionBM.Amount,
                    QrCodeId= transactionBM.QrCodeId,
                    TransactionDate= transactionBM.TransactionDate,
                    Status = TransactionStatus.succeeded                   
                };
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/Transaction";
                ResponseAPI<TransactionBlockchain>? blockchainResponse = _transactionBlockchainHelper.Post(endPoint, transaction);

                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = exceptionMessage.CreateError
                    };
                }
                
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created
                }; 
            }
            else
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessage.CreateError
                };
            }
        }

        public ResponseAPI<TransactionDTO> CreateTransactionToCompany(TransactionToCompanyDTO transactionToCompanyDTO, Guid userId, string pinCode)
        {
            var senderWalletId = _walletService.GetUserWallets(transactionToCompanyDTO.SenderCompanyId).ObjectValue?.FirstOrDefault();
            var receiverWalletId = _walletService.GetUserWallets(transactionToCompanyDTO.ReceiverCompanyId).ObjectValue?.FirstOrDefault();
            if (senderWalletId != null && receiverWalletId != null)
            {
                TransactionDTO transactionDTO = new TransactionDTO()
                {
                    SenderWalletId = senderWalletId.Id,
                    ReceiverWalletId = receiverWalletId.Id,
                    Amount = transactionToCompanyDTO.Amount,
                };
                ResponseAPI<TransactionDTO> responseTransaction = Create(transactionDTO, senderWalletId.AssignedToId, senderWalletId.AssignedToId.ToString());
                return responseTransaction;


            }
            return new ResponseAPI<TransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "wallet not found"
            };
            

        }
        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionsForGroup(TransactionGroupDTO transactionGroup , string pinCode)
        {
            ExceptionMessages exceptionMessages = new ExceptionMessages("transaction");
            var senderWalletResponse = _walletService.GetUserWallets(transactionGroup.SenderUserId).ObjectValue;
            if (senderWalletResponse == null || senderWalletResponse.Count <= 0)
            {
                return new ResponseAPI<List<TransactionUserDTO>>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"sender wallet not found !"
                };
            }

            if (!senderWalletResponse.Any(wallet => wallet.Balance > transactionGroup.TotalAmount)) {
                return new ResponseAPI<List<TransactionUserDTO>>()
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = $"The sender's wallet amount is insufficient !"
                };
            }

            var senderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWalletResponse.FirstOrDefault()?.PrivateKey ?? string.Empty, pinCode.ToString());

            
            if(transactionGroup.ReceiverList != null && transactionGroup.ReceiverList.Count > 0) 
            {
                var transactionDTOList = new List<TransactionDTO>();
                TransactionGroupBlockchain transactionGroupBlockchain = new TransactionGroupBlockchain
                {
                    Id = transactionGroup.Id,
                    QrCodeId = transactionGroup.QrCodeId,
                    SenderPrivateKey = senderPrivateKey,
                    Status = transactionGroup.Status,
                    TransactionDate = transactionGroup.TransactionDate,
                    TotalAmount = transactionGroup.TotalAmount,
                };
                List<ReceiverListBlockchain> receiverListDTOs= new List<ReceiverListBlockchain>();
                foreach (var receiver in transactionGroup.ReceiverList)
                {
                    var receiverWalletResponse = _walletService.GetUserWalletByType(receiver.ReceiverUserId, receiver.ReceiverWalletType).ObjectValue;
                    if(receiverWalletResponse == null )
                    {
                        return new ResponseAPI<List<TransactionUserDTO>>
                        {
                            StatusCode = System.Net.HttpStatusCode.BadRequest,
                            ExceptionMessage = "Receiver wallet not found !",
                        };
                    }

                    var receiverPublicKey = receiverWalletResponse.PublicKey;

                    receiverListDTOs.Add(new ReceiverListBlockchain
                    {
                        ReceiverPublicKey = receiverPublicKey,
                        Amount = receiver.Amount
                    });

                    transactionDTOList.Add(new TransactionDTO()
                    {
                        Id = Guid.NewGuid(),
                        SenderWalletId = senderWalletResponse.FirstOrDefault()?.Id ?? Guid.Empty,
                        ReceiverWalletId = receiverWalletResponse.Id,
                        TransactionDate = DateTime.UtcNow,
                        Amount = receiver.Amount,
                        Status = TransactionStatus.succeeded
                    });

                }
                transactionGroupBlockchain.ReceiverTransactions = receiverListDTOs;
                ResponseAPI<TransactionGroupBlockchain>? blockchainResponse = _transactionGroupBlockchainHelper.Post($"{_configuration.PaymentBlockchainAddress}/ListTransaction",
                                                                                                            transactionGroupBlockchain);

                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = exceptionMessage.CreateError
                    };
                }

                var query = @"UNWIND $transactions AS transactionData
                MATCH(sender: Wallet { Id: transactionData.SenderWalletId}), (receiver: Wallet { Id: transactionData.ReceiverWalletId})
                                CREATE(sender) -[t: Transaction
                                {
                                    Id: transactionData.Id,
                                    Amount: transactionData.Amount,
                                    SenderWalletId:transactionData.SenderWalletId,
                                    ReceiverWalletId:transactionData.ReceiverWalletId,
                                    TransactionDate:transactionData.TransactionDate,
                                    Status:transactionData.Status,
                                    RefundStatus:transactionData.RefundStatus
                                    } 
                                ]->(receiver)
                                SET 
                                sender.Balance = sender.Balance - transactionData.Amount,
                                receiver.Balance = receiver.Balance + transactionData.Amount
                                RETURN t as t; ";

                var parameters = new Dictionary<string, object>
                {
                    { "transactions", transactionDTOList.Select(transaction =>  new  Dictionary<string, object> {
                                { "Id",transaction.Id.ToString()  },
                                { "Amount",transaction.Amount  },
                                { "SenderWalletId",transaction.SenderWalletId.ToString()  },
                                { "ReceiverWalletId", transaction.ReceiverWalletId.ToString() },
                                { "TransactionDate", transaction.TransactionDate},
                                { "Status", transaction.Status.ToString() },
                                { "RefundStatus", RefundStatus.None.ToString() }
                            })
                    }
                };
                var result = _Repository.ExecuteWriteRangeTransactionAsync<IRelationship>(query, "t", parameters).Result;
                
                if (result != null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created
                    };
                }
                return new ResponseAPI<List<TransactionUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessages.CreateError,
                };
            }
            


            return new ResponseAPI<List<TransactionUserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "List of receiver is empty !",
            };

        }

        public ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO, string pinCode)
        {
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            if (senderWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Sender wallet is not found",
                };
            }
            var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }
            var SenderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWallet.PrivateKey, pinCode.ToString());
            var receiverPublicKey = receiverWallet.PublicKey;
            
            if (transactionBM != null)
            {
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $ReceiverId})
                                CREATE (sender)-[t:Transaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus
                                    } 
                                ]->(receiver)
                                SET 
                                sender.Balance = sender.Balance - $Amount,
                                receiver.Balance = receiver.Balance + $Amount
                                RETURN t as t;";
                transactionBM.Id = Guid.NewGuid();
                transactionBM.TransactionDate = DateTime.UtcNow;
                transactionBM.Status = TransactionStatus.succeeded;
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.SenderWalletId.ToString()  },
                    { "ReceiverId",transactionBM.ReceiverWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() },
                    { "RefundStatus", RefundStatus.None.ToString() }
                };

                var transaction = new TransactionBlockchain
                {
                    Id = transactionBM.Id,
                    QrCodeId = transactionBM.QrCodeId,
                    SenderPrivateKey = SenderPrivateKey,
                    ReceiverPublicKey = receiverPublicKey,
                    Amount = transactionBM.Amount,
                    TransactionDate = transactionBM.TransactionDate,
                    Status = transactionBM.Status
                };
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/Transaction";
                ResponseAPI<TransactionBlockchain>? blockchainResponse = _transactionBlockchainHelper.Post(endPoint, transaction);

                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = exceptionMessage.CreateError
                    };
                }


                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction is not created",
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(TransactionDTO), "Transaction must not be null");
            }
        }

        #endregion

        #region Cancled

        public ResponseAPI<TransactionDTO> UpdateRefundStatus(Guid transactionId, RefundStatus refundStatus)
        {
            ResponseAPI<TransactionDTO> transaction = _transactionService.Get(transactionId);
            if (transaction == null || transaction.ObjectValue == null) 
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = "Transaction not found !"
                }; 
            }
            var query = @" MATCH (:Wallet)-[transaction:Transaction {Id: $id}]->(:Wallet)
                           SET transaction.RefundStatus = $refundStatus
                           RETURN transaction;";

            IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "id", transactionId.ToString()  },
                    { "refundStatus", refundStatus.ToString()  },
                };
            var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;

            if (result != null)
            {
                TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                TransactionBM transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = transactionDTO
                };
            }

            return new ResponseAPI<TransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest
            };
        }
        public ResponseAPI<TransactionDTO> CancelTransaction(Guid transactionId, string pinCode)
        {
            throw new NotImplementedException();
        }

        

        #endregion
    }
}
