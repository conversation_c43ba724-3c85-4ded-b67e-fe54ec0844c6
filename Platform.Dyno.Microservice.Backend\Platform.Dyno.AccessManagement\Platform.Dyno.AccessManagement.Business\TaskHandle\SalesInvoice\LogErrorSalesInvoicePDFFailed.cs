﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.Business.Service.Logger;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Logger;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SI_Log_Error_PDF_Failed")]
    public class LogErrorSalesInvoicePDFFailed : IExternalTaskHandler
    {
        private readonly ILogger<LogErrorSalesInvoicePDFFailed> _logger;
        private readonly ILogErrorService _logErrorService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public LogErrorSalesInvoicePDFFailed(
        ILogger<LogErrorSalesInvoicePDFFailed> logger,
        ILogErrorService logErrorService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId)

        {
            _logger = logger;
            _logErrorService = logErrorService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
                SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
                //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());
                string? error = externalTask.Variables["Error_Create_SI_PDF"].ToString();

                if (salesInvoiceDTO == null)
                {
                    outputVariables.Add("Result_SI_Log_Error_PDF_Failed", new BooleanVariable(false));
                    outputVariables.Add("Error_SI_Log_Error_PDF_Failed", new StringVariable("json conversion failed"));

                }
                else
                {
                    LogErrorDTO logErrorDTO = new LogErrorDTO()
                    {
                        Id = Guid.NewGuid(),
                        Microservice = MicroserviceName.AccessManagement,
                        API = $"invoiceService/CreatePDF/{salesInvoiceDTO.Id}",
                        Error = error,
                        Type = ErrorType.SI_PDF_Failed,
                        CreationDate = DateTime.UtcNow
                    };

                    var response = _logErrorService.Create(logErrorDTO);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
                    {
                        outputVariables.Add("Result_SI_Log_Error_PDF_Failed", new BooleanVariable(true));
                    }
                    else
                    {
                        outputVariables.Add("Result_SI_Log_Error_PDF_Failed", new BooleanVariable(false));
                        outputVariables.Add("Error_SI_Log_Error_PDF_Failed", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SI_Log_Error_PDF_Failed", new BooleanVariable(false));
                outputVariables.Add("Error_SI_Log_Error_PDF_Failed", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
