﻿using Platform.Dyno.Shared.Enum;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.RefData
{
    [ProtoContract()]
    public class ReferentialData
    {
        [ProtoMember(1)]
        public Status Status { get; set; } = Status.Active;

        [ProtoMember(2)]
        #region Creation
        public Guid? CreatorUserId { get; set; }
        [ProtoMember(3)]
        public string? CreatorUserEmail { get; set; }
        [ProtoMember(4)]
        public DateTime? CreationTime { get; set; }
        #endregion

        #region Modification
        [ProtoMember(5)]
        public Guid? LastModifierUserId { get; set; }
        [ProtoMember(6)]
        public string? LastModifierUserEmail { get; set; }
        [ProtoMember(7)]
        public DateTime? LastModificationTime { get; set; }
        #endregion

        #region Deletion
        [ProtoMember(8)]
        public Guid? DeleterUserId { get; set; }
        [ProtoMember(9)]
        public string? DeleterUserEmail { get; set; }
        [ProtoMember(10)]
        public DateTime? DeletionTime { get; set; }
        #endregion

    }
}
