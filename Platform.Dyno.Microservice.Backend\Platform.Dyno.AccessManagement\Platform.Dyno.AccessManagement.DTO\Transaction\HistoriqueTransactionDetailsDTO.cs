﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Transaction
{
    public class HistoriqueTransactionDetailsDTO
    {
        public Guid Id { get; set; }

        public Guid SenderWalletId { get; set; }
        public Guid SenderId { get; set; }
        public string? SenderName { get; set; }  

        public Guid ReceiverWalletId { get; set; }
        public Guid ReceiverId { get; set; }
        public string? ReceiverName { get; set;}

        public DateTime TransactionDate { get; set; }
        public double Amount { get; set; }

        public RefundStatus RefundStatus { get; set; }

        public bool IsCredit { get; set; }
    }
}
