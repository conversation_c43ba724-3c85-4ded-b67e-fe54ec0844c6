﻿using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.Extensions.Configuration;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Image
{
    public static class ImageHelper
    {
        public static async Task<string> SaveImageAsync(string imageBase64)
        {
            string createdFileName = string.Empty;
            if (!string.IsNullOrEmpty(imageBase64))
            {
                string[] imageArray = imageBase64.Split(',');

                if (imageArray != null && imageArray.Length > 1)
                {
                    if (IsBase64String(imageArray[1]))
                    {
                        byte[] fileBytes = Convert.FromBase64String(imageArray[1]);
                        Guid fileId = Guid.NewGuid();
                        string fileType = getFileType(imageBase64.Split(',')[0]);
                        string fileName = fileId + fileType;
                        createdFileName = await UploadFromAWSAsync(fileBytes, fileName);

                    }
                }
            }
            return createdFileName;
        }

        private static async Task<string> UploadFromAWSAsync(byte[] fileBytes, string fileName)
        {
            string createdFileName;
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
                .Build();
            var client = new AmazonS3Client(configuration["AWSAccessKey"], configuration["AWSPrivateKey"], Amazon.RegionEndpoint.USEast1);
            try
            {
                using (var stream = new MemoryStream(fileBytes))
                {

                    PutObjectRequest putRequest = new PutObjectRequest
                    {
                        BucketName = configuration["AWSBucketName"],
                        Key = $"Images/{fileName}",
                        InputStream = stream,
                        ContentType = "image/jpg"
                    };

                    PutObjectResponse response = await client.PutObjectAsync(putRequest);
                }
                createdFileName = fileName;
            }
            catch (AmazonS3Exception amazonS3Exception)
            {
                if (amazonS3Exception.ErrorCode != null &&
                    (amazonS3Exception.ErrorCode.Equals("InvalidAccessKeyId")
                    ||
                    amazonS3Exception.ErrorCode.Equals("InvalidSecurity")))
                {
                    throw new Exception("Check the provided AWS Credentials.");
                }
                else
                {
                    throw new Exception("Error occurred: " + amazonS3Exception.Message);
                }
            }
            return createdFileName;
        }

        public static async Task UploadPDFInAWSAsync(byte[] fileBytes, string fileName)
        {
            string createdFileName;
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
                .Build();
            var client = new AmazonS3Client(configuration["AWSAccessKey"], configuration["AWSPrivateKey"], Amazon.RegionEndpoint.USEast1);
            try
            {
                using (var stream = new MemoryStream(fileBytes))
                {

                    PutObjectRequest putRequest = new PutObjectRequest
                    {
                        BucketName = configuration["AWSBucketName"],
                        Key = $"PDFs/{fileName}",
                        InputStream = stream,
                        ContentType = "application/pdf"
                    };

                    PutObjectResponse response = await client.PutObjectAsync(putRequest);
                }
                createdFileName = fileName;
            }
            catch (AmazonS3Exception amazonS3Exception)
            {
                if (amazonS3Exception.ErrorCode != null &&
                    (amazonS3Exception.ErrorCode.Equals("InvalidAccessKeyId")
                    ||
                    amazonS3Exception.ErrorCode.Equals("InvalidSecurity")))
                {
                    throw new Exception("Check the provided AWS Credentials.");
                }
                else
                {
                    throw new Exception("Error occurred: " + amazonS3Exception.Message);
                }
            }
        }

        private static async Task<string> UploadImage(string webRootPath, byte[] fileBytes, string fileName)
        {
            try
            {
                string imagePath = Path.Combine(webRootPath, "Images", fileName);

                using (FileStream fs = new FileStream(imagePath, FileMode.Create))
                {
                    await fs.WriteAsync(fileBytes, 0, fileBytes.Length);
                }

                return $"Images/{fileName}";
            }
            catch (Exception ex)
            {
                // Handle the exception (log, throw, etc.)
                Console.WriteLine($"Error uploading image: {ex.Message}");
                return string.Empty;
            }
        }
        
        private static string getFileType(string fileHeader)
        {
            string fileType = "";
            if (fileHeader.ToLower().Contains("png"))
                fileType = ".png";
            else if (fileHeader.ToLower().Contains("jpeg"))
                fileType = ".jpeg";
            else if (fileHeader.ToLower().Contains("jpg"))
                fileType = ".jpg";
            return fileType;
        }

        private static bool IsBase64String(string base64)
        {
            Span<byte> buffer = new Span<byte>(new byte[base64.Length]);
            return Convert.TryFromBase64String(base64, buffer, out int bytesParsed);
        }

        public static async Task<byte[]> DownloadImage(string imageUrl)
        {
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    // Download the image bytes asynchronously
                    byte[] imageBytes = await client.GetByteArrayAsync(imageUrl);
                    return imageBytes;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error downloading image: {ex.Message}");
                    return null;
                }
            }
        }

        
    }
}
