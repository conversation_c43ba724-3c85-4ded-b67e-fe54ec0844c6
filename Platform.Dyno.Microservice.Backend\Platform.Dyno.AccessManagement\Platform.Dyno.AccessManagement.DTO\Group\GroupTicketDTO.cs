﻿using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Group;

 public class GroupTicketDTO
{
    public Guid? GroupId { get; set; }
    [JsonIgnore]
    public GroupDTO? Group { get; set; } 
    public Guid TicketId { get; set; }
    public TicketDTO? Ticket { get; set; }
}
