﻿using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;

namespace Platform.Dyno.AccessManagement.Business.IService.Company;

public interface ICompanyService : IGenericService<CompanyDTO>
{
    ResponseAPI<PagedList<CompanyDTO>> GetRecents(PagedParameters pagedParameters);
    ResponseAPI<PagedList<CompanyDTO>> GetAllActiveByUserType(PagedParameters pagedParameters, UserType filterType, Guid? companyId = null, UserType? userType = null);
    ResponseAPI<List<CompanyDTO>> GetAllShopByCategory(CategoryType category);
    ResponseAPI<List<CompanyDTO>> GetNearestShopByCategory(CategoryType category, double longitude, double latitude, double distance);
    ResponseAPI<PagedList<UserDTO>> GetAllEmployees( PagedParameters pagedParameters, Guid companyId);
    ResponseAPI<PagedList<UserDTO>> GetAllActiveEmployees(PagedParameters pagedParameters, Guid companyId);
    ResponseAPI<CompanyDTO> GetCompanyByEmployee(Guid employeeId);
    ResponseAPI<EmployeeDTO> AssociateEmployeeToCompany(Guid companyId, Guid userId, Guid groupId, Status status, bool updateCache = true);
    ResponseAPI<EmployeeDTO> UpdateEmployeeAssociation(Guid companyId,Guid employeeId, Guid userId, Guid groupId, Status status, bool updateCache = true);
    ResponseAPI<EmployeeDTO> DeleteEmployee(Guid employeeId, Guid companyId, Guid? deletorUserId = null, bool updateCache = true);

    ResponseAPI<CompanyDTO> Reactivate(Guid id, Guid? reactivateUserId = null, bool updateCache = true);



}
