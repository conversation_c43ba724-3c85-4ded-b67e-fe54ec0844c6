﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.WebAPI.Controllers.Cashback;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.Business.IService.CashBack;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Cashback
{
    [Route("Api/[controller]")]
    [ApiController]
    public class FailedCashbackController : Controller
    {
        private readonly IFailedCashBackService _failedCashbackService;
        private readonly IUserService _userService;
        private readonly ILogger<FailedCashbackController> _logger;

        private readonly ISortingUtility _sortingUtility;
        public FailedCashbackController(IFailedCashBackService failedCashbackService,
            IUserService userService,
            ILogger<FailedCashbackController> logger,
            ISortingUtility sortingUtility)
        {
            _failedCashbackService = failedCashbackService;
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<FailedCashBackDTO>> failedCashbacks = _failedCashbackService.GetAll(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(failedCashbacks);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<FailedCashBackDTO>> response = new ResponseAPI<List<FailedCashBackDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<PagedList<FailedCashBackDTO>> cashBacksDTO = _failedCashbackService.GetAll(companyId, isSuperAdminResponse.ObjectValue, pagedParameters);
                if (cashBacksDTO.ObjectValue != null)
                {
                    cashBacksDTO.ObjectValue= _sortingUtility.SortData(pagedParameters, cashBacksDTO);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = cashBacksDTO.ObjectValue.TotalCount,
                        PageSize = cashBacksDTO.ObjectValue.PageSize,
                        CurrentPage = cashBacksDTO.ObjectValue.CurrentPage,
                        TotalPages = cashBacksDTO.ObjectValue.TotalPages,
                        HasNext = cashBacksDTO.ObjectValue.HasNext,
                        HasPrevious = cashBacksDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(cashBacksDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] FailedCashBackDTO failedCashbackDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<FailedCashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<FailedCashBackDTO> failedCashbackDTOResponse = _failedCashbackService.Create(companyId, isSuperAdminResponse.ObjectValue, failedCashbackDTO, userId);
                if (failedCashbackDTOResponse.ObjectValue == null)
                {
                    return BadRequest(Response);
                }
                return Ok(failedCashbackDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<FailedCashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

    }
}
