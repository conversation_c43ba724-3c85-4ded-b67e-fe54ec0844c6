﻿using AutoMapper;
using Domain.Entities;
using Platform.Dyno.Shared.Enum;
using Neo4j.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Mapping
{
    public class MapperINode_Entity : Profile
    {
        public MapperINode_Entity() {
            CreateMap<INode, WalletEntity>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.Parse(src["Id"].As<string>())))
            .ForMember(dest => dest.PrivateKey, opt => opt.MapFrom(src => src["PrivateKey"].As<string>()))
            .ForMember(dest => dest.PublicKey, opt => opt.MapFrom(src =>src["PublicKey"].As<string>()))
            .ForMember(dest => dest.AssignedToId, opt => opt.MapFrom(src => Guid.Parse(src["AssignedToId"].As<string>())))
            .ForMember(dest => dest.WalletType, opt => opt.MapFrom(src => Enum.Parse(typeof(WalletType), src["WalletType"].As<string>(), true)))
            .ForMember(dest => dest.AssignedToType, opt => opt.MapFrom(src => Enum.Parse(typeof(UserType), src["AssignedToType"].As<string>(), true)))
            .ForMember(dest => dest.Balance, opt => opt.MapFrom(src => src["Balance"].As<double>()))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse(typeof(WalletStatus), src["Status"].As<string>(), true)))
            .ReverseMap();
        }
    }
}
