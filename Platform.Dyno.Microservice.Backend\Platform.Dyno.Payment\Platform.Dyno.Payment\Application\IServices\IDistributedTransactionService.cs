using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;

namespace Application.IServices
{
    /// <summary>
    /// Interface pour la gestion des transactions distribuées entre Neo4j et Blockchain
    /// </summary>
    public interface IDistributedTransactionService
    {
        /// <summary>
        /// Exécute une transaction distribuée avec garantie ACID entre Neo4j et Blockchain
        /// </summary>
        /// <param name="transactionDTO">Données de la transaction</param>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <param name="pinCode">Code PIN pour la validation</param>
        /// <returns>Résultat de la transaction distribuée</returns>
        Task<ResponseAPI<TransactionDTO>> ExecuteDistributedTransaction(
            TransactionDTO transactionDTO, 
            Guid userId, 
            string pinCode);
    }
}
