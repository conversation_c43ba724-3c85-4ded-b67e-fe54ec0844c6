﻿using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface IQrCodeService
    {
        public ResponseAPI<string> GenerateTransactionQrCode(TransactionQrCodeDTO transactionQrCodeDTO );
        public ResponseAPI<string> GenerateWalletQrCode(WalletQrCodeDTO transactionQrCodeDTO);

    }
}
