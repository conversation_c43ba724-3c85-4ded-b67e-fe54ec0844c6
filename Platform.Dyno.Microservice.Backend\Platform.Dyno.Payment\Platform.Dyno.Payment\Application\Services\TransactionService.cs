﻿using Application.Interfaces;
using Application.IServices;
using Application.Models.BMs;
using Application.Security;
using AutoMapper;
using Domain.Entities;
using Domain.Enums;
using Domain.Enums.Query;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using StackExchange.Redis;
using System.Collections.Generic;

namespace Application.Services
{
    public class TransactionService : ITransactionService
    {
        private readonly IGenericRepository _Repository;
        private readonly IMapper _mapper;
        private readonly IWalletService _walletService;
        private readonly ILogger<TransactionService> _logger;

        public TransactionService(IGenericRepository Repository, ILogger<TransactionService> logger, IMapper mapper,
            IWalletService walletService)
        {
            _Repository = Repository;
            _logger = logger;
            _mapper = mapper;
            _walletService = walletService;
        }


        #region Get
        public ResponseAPI<List<TransactionDTO>> GetAll(string sort = "",
            Ordering order = Ordering.Asc, int limit = 6, int skip = 0)
        {

            var query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet{{userType:$userType}})
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit },
                { "userType", UserType.Company.ToString() }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                return _mapper.Map<TransactionDTO>(TransactionBM);
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }
        public ResponseAPI<List<TransactionDTO>> GetAllByUserType(UserType userType, string sort = "",
        Ordering order = Ordering.Asc, int limit = 6, int skip = 0)
        {
            var res = UserType.Company.ToString();
            var query = "";
            IDictionary<string, object> parameters = null;
            if (userType == UserType.Company)
            {
                query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet{{AssignedToType:$userType}})
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";
                parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit },
                { "userType", UserType.Company.ToString() }
            };
            }
            else if (userType == UserType.ShopOwner)
            {
                query = @$" MATCH (sender:Wallet{{AssignedToType:$userType}})-[t:Transaction]->(receiver:Wallet)
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";
                parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit },
                { "userType", UserType.ShopOwner.ToString() }
            };
            }
        



            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                return _mapper.Map<TransactionDTO>(TransactionBM);
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }
        public ResponseAPI<TransactionDTO> Get(Guid id)
        {
            try
            {
                var query = @$"MATCH (sender:Wallet)-[t:Transaction{{Id:$TransactionId}}]->(receiver:Wallet)
                            RETURN t{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                    { "TransactionId",id.ToString() }
                };

                var Transaction = _Repository.ExecuteReadScalarAsync<Dictionary<string, object>>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Transaction with id {id} is not found",
                };
                if (Transaction != null)
                {
                    Transaction["TransactionDate"] = ((ZonedDateTime)Transaction["TransactionDate"]).UtcDateTime;
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(Transaction);
                    TransactionBM transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.OK,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;

            }
            catch (Exception ex)
            {
                _logger.LogError("Error with get transaction by id", ex);
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Transaction with id {id} is not found",
                };
                return response;
            }

        }

        public ResponseAPI<bool> CheckQRcodeExist(Guid qrcode)
        {
            var query = @$"MATCH (sender:Wallet)-[t:Transaction{{QrCodeId:$QrCodeId}}]->(receiver:Wallet)
               RETURN t{{.* }}";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                    { "QrCodeId",qrcode.ToString()}
               };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            if (Transactions != null && Transactions.Count() > 0)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = true,
                }; 
            }

            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = false
            };
        }
        public ResponseAPI<TransactionDTO> GetPreTransaction(Guid SenderWalletId,Guid QrCodeId)
        {
            try
            {
                var query = @$"MATCH (sender:Wallet{{Id:$SenderWalletId}})-[t:Transaction{{QrCodeId:$QrCodeId}}]->(receiver:Wallet{{Id:$SenderWalletId}})
                            RETURN t{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                    { "QrCodeId",QrCodeId.ToString()},
                    { "SenderWalletId",SenderWalletId.ToString()}
                };

                var Transaction = _Repository.ExecuteReadScalarAsync<Dictionary<string, object>>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Transaction with QrCodeId {QrCodeId} is not found",
                };
                if (Transaction != null)
                {
                    Transaction["TransactionDate"] = ((ZonedDateTime)Transaction["TransactionDate"]).UtcDateTime;
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(Transaction);
                    TransactionBM transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.OK,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;

            }

            catch (Exception ex)
            {
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Transaction with QrCodeId {QrCodeId} is not found",
                };
                return response;
            }

        }
        
        public ResponseAPI<List<TransactionDTO>> GetUserReceivedTransactions(Guid userId, string sort = "",
        Ordering order = Ordering.Desc, int limit = 6, int skip = 0)
        {

            var query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet{{AssignedToId:$userId}})
                            WHERE sender.AssignedToId <> $userId
                            RETURN t{{.* }}
                            ORDER BY t.TransactionDate DESC
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"userId",userId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                transactionDTO.IsCredit = true;
                return transactionDTO;
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }

        
        public ResponseAPI<List<TransactionDTO>> GetUserSentTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var query = @$" MATCH (sender:Wallet{{AssignedToId:$userId}})-[t:Transaction]->(receiver:Wallet)
                            WHERE receiver.AssignedToId <> $userId
                            RETURN t{{.* }}
                            ORDER BY t.TransactionDate DESC
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"userId",userId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                transactionDTO.IsCredit = false;
                return transactionDTO;
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }
        
        
        public ResponseAPI<List<TransactionDTO>> GetUserTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            List<WalletDTO>? walletsDTO = _walletService.GetUserWallets(userId).ObjectValue;
            if(walletsDTO != null && walletsDTO.Count > 0)
            {
                var query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet)
                WHERE (sender.AssignedToId = $userId AND receiver.AssignedToId <> $userId) OR
                      (sender.AssignedToId <> $userId AND receiver.AssignedToId = $userId)
                    RETURN t{{.* }}
                    ORDER BY t.TransactionDate DESC
                    SKIP $skip
                    LIMIT $limit";
                
                IDictionary<string, object> parameters = new Dictionary<string, object> {
                    {"userId",userId.ToString() },
                    { "sort", sort },
                    { "order", order.ToString().ToUpper() },
                    { "skip", skip },
                    { "limit", limit }
                };

                var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
                List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
                {
                    t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                    var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                    var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                    TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                    if(walletsDTO.Any(wallet => wallet.Id == transactionDTO.SenderWalletId))
                        transactionDTO.IsCredit = false;
                    else transactionDTO.IsCredit = true;
                    return transactionDTO;
                }).ToList();

                return new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = TransactionsDtO
                };

            }

            
            return new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = $"User Unauthorized !"
            };
        }

        #region get company transaction
        public ResponseAPI<double> GetCompanyReceivedTransactionsTotalAmountPerMonth(Guid companyId)
        {
            var query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet{{AssignedToId:$companyId}})
                            where datetime(t.TransactionDate).epochSeconds >= datetime().epochSeconds - (30 * 24 * 60 * 60)
                            RETURN SUM(t.Amount) AS totalAmount";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"companyId",companyId.ToString() }
            };

            double TransactionTotalAmount = _Repository.ExecuteReadScalarAsync<double>(query, parameters).Result;
            
            ResponseAPI<double> response = new ResponseAPI<double>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionTotalAmount,
            };
            return response;
        }
        public ResponseAPI<double> GetCompanySendedTransactionsTotalAmountPerMonth(Guid companyId)
        {
            var query = @$" MATCH (sender:Wallet{{AssignedToId:$companyId}})-[t:Transaction]->(receiver:Wallet)
                            where datetime(t.TransactionDate).epochSeconds >= datetime().epochSeconds - (30 * 24 * 60 * 60)
                            RETURN SUM(t.Amount) AS totalAmount";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"companyId",companyId.ToString() }
            };

            double TransactionTotalAmount = _Repository.ExecuteReadScalarAsync<double>(query, parameters).Result;

            ResponseAPI<double> response = new ResponseAPI<double>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionTotalAmount,
            };
            return response;
        }
        #endregion
        public ResponseAPI<List<TransactionDTO>> GetWalletReceivedTransactions(Guid walletId, string sort = "",
        Ordering order = Ordering.Asc, int limit = 6, int skip = 0)
        {

            var query = @$" MATCH (sender:Wallet)-[t:Transaction{{ReceiverWalletId:$walletId}}]->(receiver:Wallet)
                            WHERE receiver.AssignedToId <> sender.AssignedToId
                            RETURN t{{.* }}
                            ORDER BY t.TransactionDate DESC
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"walletId",walletId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                transactionDTO.IsCredit = true;
                return transactionDTO;
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }

        
        public ResponseAPI<List<TransactionDTO>> GetWalletSentTransactions(Guid walletId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var query = @$" MATCH (sender:Wallet)-[t:Transaction{{SenderWalletId:$walletId}}]->(receiver:Wallet)
                            WHERE receiver.AssignedToId <> sender.AssignedToId
                            RETURN t{{.* }}
                            ORDER BY t.TransactionDate DESC
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"walletId",walletId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                transactionDTO.IsCredit = false;
                return transactionDTO;
            }).ToList();
            ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO,
            };
            return response;
        }


        public ResponseAPI<List<TransactionDTO>> GetWalletTransactions(Guid walletId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var query = @$" MATCH (sender:Wallet)-[t:Transaction]->(receiver:Wallet)
                WHERE (t.ReceiverWalletId = $walletId AND receiver.AssignedToId <> sender.AssignedToId) OR
                      (t.SenderWalletId = $walletId AND receiver.AssignedToId <> sender.AssignedToId)
                RETURN t{{.* }}
                ORDER BY t.TransactionDate DESC
                SKIP $skip
                LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"walletId",walletId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var Transactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            List<TransactionDTO> TransactionsDtO = Transactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                var TransactionEntity = _mapper.Map<TransactionEntity>(t);
                var TransactionBM = _mapper.Map<TransactionBM>(TransactionEntity);
                TransactionDTO transactionDTO = _mapper.Map<TransactionDTO>(TransactionBM);
                if (transactionDTO.SenderWalletId == walletId)
                    transactionDTO.IsCredit = false;
                else transactionDTO.IsCredit = true;
                return transactionDTO;
            }).ToList();

            return new ResponseAPI<List<TransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = TransactionsDtO
            };
        }
        
        
        #endregion


        #region create
        public ResponseAPI<TransactionDTO> Create(TransactionDTO transactionDTO, Guid UserId,string pinCode)
        {
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            if (senderWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Sender wallet is not found",
                };
            }
            var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }
            var SenderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWallet.PrivateKey,pinCode.ToString());
            var recriverPublicKey = receiverWallet.PublicKey;
            if (transactionBM != null)
            {
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $ReceiverId})
                                CREATE (sender)-[t:Transaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus
                                    } 
                                ]->(receiver)
                                SET 
                                sender.Balance = sender.Balance - $Amount,
                                receiver.Balance = receiver.Balance + $Amount
                                RETURN t as t;";
                transactionBM.Id = Guid.NewGuid();
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.SenderWalletId.ToString()  },
                    { "ReceiverId",transactionBM.ReceiverWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() },
                    { "RefundStatus", transactionBM.RefundStatus.ToString() },
                };
                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction is not created",
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(TransactionDTO), "Transaction must not be null");
            }
        }
        public ResponseAPI<TransactionDTO> CreatePreTransaction(TransactionDTO transactionDTO)
        {
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            if (senderWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Sender wallet is not found",
                };
            }

            transactionBM.Id = Guid.NewGuid();
            if (transactionBM != null)
            {
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $SenderId})
                                CREATE (sender)-[t:Transaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$SenderId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus
                                    } 
                                ]->(receiver)
                                RETURN t as t;";
                transactionBM.Id = Guid.NewGuid();
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.SenderWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() },
                    { "RefundStatus", RefundStatus.None.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction is not created",
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(TransactionDTO), "Transaction must not be null");
            }
        }
        public ResponseAPI<TransactionDTO> CancelTransaction(Guid transactionId, string pinCode)
        {
            var transactionDTO = Get(transactionId).ObjectValue;
            var transactionBM = _mapper.Map<TransactionBM>(transactionDTO);
            if (transactionBM != null)
            {
                var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
                if (senderWallet == null)
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "Sender wallet is not found",
                    };
                }
                var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
                if (receiverWallet == null)
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "Receiver wallet is not found",
                    };
                }
                var SenderPrivateKey = KeyManager.DecryptAndHashPrivateKey(senderWallet.PrivateKey, pinCode.ToString());
                var recriverPublicKey = receiverWallet.PublicKey;
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $ReceiverId})
                                CREATE (sender)-[t:Transaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status} 
                                ]->(receiver)
                                SET 
                                sender.Balance = sender.Balance - $Amount,
                                receiver.Balance = receiver.Balance + $Amount
                                RETURN t as t;";
                transactionBM.Id = Guid.NewGuid();
                transactionBM.TransactionDate= DateTime.UtcNow;
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.ReceiverWalletId.ToString()  },
                    { "ReceiverId",transactionBM.SenderWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction is not canceled",
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);    
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Transaction to cancel is not found",
                };
                return response;
            }
        }
        public ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO,string pinCode)
        {
            var senderWallet = _walletService.Get(transactionDTO.SenderWalletId).ObjectValue;
            if (senderWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Sender wallet is not found",
                };
            }
            var receiverWallet = _walletService.Get(transactionDTO.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }
            var preTransactionResponse = GetPreTransaction(transactionDTO.SenderWalletId, transactionDTO.QrCodeId);
            if (preTransactionResponse.StatusCode == System.Net.HttpStatusCode.OK && preTransactionResponse.ObjectValue!=null)
            {
                var preTransaction = preTransactionResponse.ObjectValue;
                var preTransactionBM = _mapper.Map<TransactionBM>(preTransaction);
                var newTransaction = new TransactionBM()
                {
                    Id=preTransactionBM.Id,
                    SenderWalletId=preTransactionBM.SenderWalletId,
                    ReceiverWalletId= transactionDTO.ReceiverWalletId,
                    Amount=preTransactionBM.Amount,
                    QrCodeId=preTransactionBM.QrCodeId,
                    TransactionDate=DateTime.UtcNow,
                    Status=preTransactionBM.Status,


                };
                var query = @$"MATCH (sender:Wallet {{Id :$SenderId}})-[oldTransaction:Transaction{{QrCodeId:$QrCodeId}}]->(oldReceiver:Wallet{{Id:$SenderId}})
                               WITH sender, oldTransaction, oldReceiver
                               MATCH (newReceiver:Wallet {{Id: $ReceiverId}})
                                CREATE (sender)-[newTransaction:Transaction{{Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus}}]->(newReceiver)
                                SET 
                                sender.Balance = sender.Balance - $Amount,
                                newReceiver.Balance = newReceiver.Balance + $Amount
                              DELETE oldTransaction
                              RETURN newTransaction as t";

                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",newTransaction.Id.ToString()  },
                    { "SenderId",newTransaction.SenderWalletId.ToString()  },
                    { "ReceiverId",newTransaction.ReceiverWalletId.ToString()  },
                    { "QrCodeId",newTransaction.QrCodeId.ToString()  },
                    { "Amount",newTransaction.Amount  },
                    { "TransactionDate", newTransaction.TransactionDate },
                    { "Status", newTransaction.Status.ToString() },
                    { "RefundStatus", RefundStatus.None.ToString() }
                };

                //Create Transaction with new Transaction
                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                ResponseAPI<TransactionDTO> response = new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction is not created",
                };
                if (result != null)
                {
                    TransactionEntity transactionEntity = _mapper.Map<TransactionEntity>(result);
                    var transactionBM = _mapper.Map<TransactionBM>(transactionEntity);
                    transactionDTO = _mapper.Map<TransactionDTO>(transactionBM);
                    response = new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = transactionDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                return new ResponseAPI<TransactionDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage =("Ticket already scanned")
                };
            }
        }
        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionsForGroup(List<TransactionUserDTO> transactionUserDTOList, string pinCode)
        {
            var transactionDTOList = new List<TransactionDTO>();
            foreach (var transactionUser in transactionUserDTOList)
            {
                var senderWalletResponse = _walletService.GetUserWalletByType(transactionUser.SenderUserId, transactionUser.SenderWalletType);
                if (senderWalletResponse.ObjectValue == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"wallet {transactionUser.SenderWalletType.ToString()} not found for user by id {transactionUser.SenderUserId} "
                    };
                }
                var receiverWalletResponse = _walletService.GetUserWalletByType(transactionUser.ReceiverUserId, transactionUser.ReceiverWalletType);
                if (receiverWalletResponse.ObjectValue == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"wallet {transactionUser.ReceiverWalletType.ToString()} not found for user by id {transactionUser.ReceiverUserId} "
                    };
                }
                var transactionDTO = new TransactionDTO()
                {
                    Id = Guid.NewGuid(),
                    SenderWalletId = senderWalletResponse.ObjectValue.Id,
                    ReceiverWalletId = receiverWalletResponse.ObjectValue.Id,
                    TransactionDate = DateTime.UtcNow,
                    Amount = transactionUser.Amount,
                    Status = TransactionStatus.succeeded
                };
                transactionDTOList.Add(transactionDTO);
            }

            var query = @"UNWIND $transactions AS transactionData
                MATCH(sender: Wallet { Id: transactionData.SenderWalletId}), (receiver: Wallet { Id: transactionData.ReceiverWalletId})
                                CREATE(sender) -[t: Transaction
                                {
                                    Id: transactionData.Id,
                                    Amount: transactionData.Amount,
                                    SenderWalletId:transactionData.SenderWalletId,
                                    ReceiverWalletId:transactionData.ReceiverWalletId,
                                    TransactionDate:transactionData.TransactionDate,
                                    Status:transactionData.Status,
                                    RefundStatus:transactionData.RefundStatus
                                    } 
                                ]->(receiver)
                                 SET 
                                 sender.Balance = sender.Balance - $Amount,
                                 receiver.Balance = receiver.Balance + $Amount
                                RETURN t as t; ";

            
            var parameters = new Dictionary<string, object>
            {
                { "transactions", transactionDTOList.Select(transaction =>  new  Dictionary<string, object> {
                                { "Id",transaction.Id.ToString()  },
                                { "Amount",transaction.Amount  },
                                { "SenderWalletId",transaction.SenderWalletId.ToString()  },
                                { "ReceiverWalletId", transaction.ReceiverWalletId.ToString() },
                                { "TransactionDate", transaction.TransactionDate},
                                { "Status", transaction.Status.ToString() },
                                { "RefundStatus", RefundStatus.None.ToString() }
                            })
                }
            };
            var result = _Repository.ExecuteWriteRangeTransactionAsync<IRelationship>(query, "t", parameters).Result;
            ResponseAPI<List<TransactionUserDTO>> response = new ResponseAPI<List<TransactionUserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Transaction is not created",
            };
            if (result != null)
            {
                response = new ResponseAPI<List<TransactionUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = transactionUserDTOList,
                };
                return response;
            }
            return response;
            
        }   
        #endregion

    }
}
