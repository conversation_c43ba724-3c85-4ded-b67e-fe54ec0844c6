﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.User.Password
{
    public class ForgetPasswordDTO
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Client URI required")]
        public string ClientURI { get; set; } = string.Empty;
    }
}
