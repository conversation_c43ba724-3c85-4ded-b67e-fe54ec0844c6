﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.SalesOrder
{
    public class SalesInvoiceEntity : ReferentialData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ProcessInstanceId { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double DynoAmount { get; set; }
        public Currency Currency { get; set; }
        public double NetAmount { get; set; }
        public double VATAmount { get; set; }
        public double TotalAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public ProductType ProductType { get; set; }
        public DateTime Date { get; set; }
        public new InvoiceStatus Status { get; set; }


        #region Structure
        [ForeignKey("SalesOrderId")]
        public Guid SalesOrderId { get; set; }
        public SalesOrderEntity? SalesOrder { get; set; }
        [ForeignKey("DocumentId")]
        public Guid DocumentId { get; set; }
        public DocumentsEntity? Document { get; set; }

        [ForeignKey("CompanyId")]
        public Guid CompanyId { get; set; }
        [JsonIgnore]
        public CompanyEntity? Company { get; set; }
        #endregion
    }
}
