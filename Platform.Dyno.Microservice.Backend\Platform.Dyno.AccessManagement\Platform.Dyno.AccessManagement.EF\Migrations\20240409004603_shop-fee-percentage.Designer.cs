﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Platform.Dyno.AccessManagement.EF;

#nullable disable

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    [DbContext(typeof(ContextDB))]
    [Migration("20240409004603_shop-fee-percentage")]
    partial class shopfeepercentage
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("MessageNotificationsId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SubscribersId")
                        .HasColumnType("uuid");

                    b.HasKey("MessageNotificationsId", "SubscribersId");

                    b.HasIndex("SubscribersId");

                    b.ToTable("MessageNotificationEntitySubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FullAddress")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSaved")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("MacAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("MacAddress");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("FeeAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PaymentDetailsId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("ValidationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("WalletBallance")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PaymentDetailsId");

                    b.ToTable("CashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CashbackId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CashbackId");

                    b.ToTable("FailedCashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("CategoryType")
                        .HasColumnType("integer");

                    b.Property<double?>("ClientFeePercentage")
                        .HasColumnType("double precision");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EntrepriseType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("RNECode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ServiceType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TaxCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Company");

                    b.HasData(
                        new
                        {
                            Id = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            ClientFeePercentage = 11.0,
                            Email = "<EMAIL>",
                            EntrepriseType = 0,
                            Name = "Dyno & Motiva Systems",
                            PhoneNumber = "+21699001000",
                            RNECode = "1803838P",
                            ServiceType = 0,
                            Status = 0,
                            TaxCode = "000 M A 1803838P",
                            WalletId = new Guid("00000000-0000-0000-0000-000000000000")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("PaymentDelay")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("RIB")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("ShopFeePercentage")
                        .HasColumnType("double precision");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.Property<Guid>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("EmployeeId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("GroupId");

                    b.HasIndex("UserId");

                    b.ToTable("Employee");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Property<Guid>("GroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("GroupId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Group");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.Property<Guid?>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uuid");

                    b.HasKey("GroupId", "TicketId");

                    b.HasIndex("TicketId");

                    b.ToTable("GroupTicket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Logger.LogErrorEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("API")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Error")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<int>("Microservice")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("LogError");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.HasKey("CompanyId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("CompanyNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MessageGroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Protocol")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Rule")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TopicArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MessageNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatorType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsSeen")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SignalRNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EndPoint")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EndPointArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TargetArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserEntityId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserEntityId");

                    b.ToTable("SubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsLeaf")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool?>("PartialSelected")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("PermissionEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PermissionEntityId");

                    b.HasIndex("RoleId");

                    b.ToTable("Permission");

                    b.HasData(
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea4"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b32f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f5252a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e0f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6c42303e-9466-40a3-9f04-59db7efd39c3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ee424456-2dd5-448b-a24a-a4ffa23db15f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("91ee99a2-9756-4fdd-acf8-dc6aceca52a7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d5cae7c2-e640-417b-98d5-d559aaa87539"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c697a4f3-c352-4279-8845-f7f0ca040b14"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c186861f-08c1-4463-8d63-5484c5652918"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f20b2983-6d9b-4fe0-8ee8-5a0255d5a2e0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a9ccc7d0-0765-4a94-9bd4-385631825d50"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b320"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f52520"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e00"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("25e5a90f-6287-4033-829a-ecc417e54343"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ceceb370-cb0d-49ab-a458-301078a9d126"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("77b55a22-850d-4177-9a00-974f6d7107e2"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0d2c1dc2-5405-4b5d-bb08-f04a90922e10"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("602aa4d2-2f12-40c2-bb07-1a22621d725d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("709b16ea-18c1-4a87-92bb-e9c9c88dbcee"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7dec5f33-050e-42fc-9643-08461b2f878d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("3c0e20f8-fd44-43d9-8ed8-b9ec25a7add3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("cf66edaa-cb00-4524-a0a1-bac0a4d2dae2"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fa58580f-446b-4b2f-80c7-117cd5e1256f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fc05ec08-2bff-4e84-9b83-aa77f3399b9a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("9d1d7b60-446c-4e14-9a25-fbed8bc96dad"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1db3f662-80aa-4373-83b3-1a040b312965"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b5018c2-e58b-46d7-aa86-c23a299e2ea1"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ded1362c-7406-410e-a8ad-1f1c106d7cfa"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("50f3274f-c7ed-48fb-88c8-e6f1c642de89"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6b735ebc-a774-4da1-8ff0-6de78332c746"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("e4b7bc84-bbf2-423a-8614-59e7e5b3e3d8"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b5c9b1a2-7fc7-4566-b85a-4c4a7707375b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("5ddd7d9c-5a4c-4c2a-92bf-a24119b0ce7e"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("608608e2-7edf-4599-8b11-ef9c2f65e841"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f78763e2-5d85-41cd-aa61-fa6d34add3dd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7bbee4f9-1a50-4a07-a14a-37861c129811"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("2bcaaff9-1c46-4d36-9363-5dded74fda55"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fcae0b52-9667-4abb-b561-c8b39ac1cc90"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("3666fb12-a9cf-46e4-9282-55f01a0e3ee4"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b7223cc0-774b-4ba5-9fe4-117f1de8ba43"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("2f9facb1-2291-4acd-a753-dc1fbeca5a12"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a890dfaf-cd23-4f56-9b8d-049cb6636be3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("37bbff10-04c0-41e9-b3e7-1c8949e64df6"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("57189e50-82c8-4eac-a604-8ffcbd0d7142"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("58bef5e1-79ce-45c6-9575-be103795d67c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            ConcurrencyStamp = "a9aa4b2a-4664-4f4e-a5c5-54717044811b",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9364),
                            Name = "Default",
                            NormalizedName = "DEFAULT",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            ConcurrencyStamp = "25f0821f-2bfa-43e3-ac83-c96eee0444bd",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9372),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "SuperAdmin",
                            NormalizedName = "SUPERADMIN",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            ConcurrencyStamp = "099c10ca-9442-4dc6-ad9d-74091b78e997",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9376),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Admin",
                            NormalizedName = "ADMIN",
                            Status = 0,
                            UserType = 1
                        },
                        new
                        {
                            Id = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            ConcurrencyStamp = "d56c2296-2579-4015-b787-923dbe943402",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9380),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Shop Admin",
                            NormalizedName = "SHOP ADMIN",
                            Status = 0,
                            UserType = 0
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                            ConcurrencyStamp = "fc33819a-cf6b-4aac-992c-1338ea50fd38",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9383),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Client",
                            NormalizedName = "CLIENT",
                            Status = 0,
                            UserType = 4
                        },
                        new
                        {
                            Id = new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                            ConcurrencyStamp = "323713be-f909-4f95-b60c-46cf2ce9b7bc",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9391),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Cashier",
                            NormalizedName = "CASHIER",
                            Status = 0,
                            UserType = 2
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("RoleId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("RoleUser");

                    b.HasData(
                        new
                        {
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            UserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SalesOrderId");

                    b.ToTable("FailedSalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.HasIndex("SalesOrderId")
                        .IsUnique();

                    b.ToTable("SalesInvoice");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.ToTable("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Property<Guid>("TicketId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<Guid?>("CompanyEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutomatic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("PeriodType")
                        .HasColumnType("integer");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("TicketId");

                    b.HasIndex("CompanyEntityId");

                    b.ToTable("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<int>("AuthentificationSource")
                        .HasColumnType("integer");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<int?>("Gender")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("text");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("PinCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            AccessFailedCount = 0,
                            AuthentificationSource = 0,
                            CompanyId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            ConcurrencyStamp = "93f5f46e-0cbd-4b07-a97d-24f606028e41",
                            CountryCode = "",
                            CreationTime = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            DateOfBirth = new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669),
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            FullName = "Dyno Root",
                            Gender = 2,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "CFBC54C0-97CE-40FF-9281-79D09713CB71",
                            PasswordHash = "AQAAAAEAACcQAAAAEHqL+5v6qGBES5d8090am7wSb/dCyRynxjXBvY03Pr/EDGlf6IhcpIMrh/G8GnlqUQ==",
                            PhoneNumberConfirmed = true,
                            PinCode = "ZFxn5dSGlaMFoTfAET4RbyhYkvs39oCKFHsXjahuXSg=",
                            Status = 0,
                            TwoFactorEnabled = false,
                            UserName = "cfbc54c0-97ce-40ff-9281-79d09713cb71",
                            UserType = 3
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Code")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserOTP");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiredDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("RefreshToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserToken");
                });

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", null)
                        .WithMany()
                        .HasForeignKey("MessageNotificationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", null)
                        .WithMany()
                        .HasForeignKey("SubscribersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Addresses")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("Addresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("MacAddresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", "PaymentDetails")
                        .WithMany("CashBacks")
                        .HasForeignKey("PaymentDetailsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", "Cashback")
                        .WithMany()
                        .HasForeignKey("CashbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cashback");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("PaymentDetails")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Employees")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("Employees")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("GroupTickets")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", "Ticket")
                        .WithMany("GroupTickets")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", "Notification")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("SubscriberDevices")
                        .HasForeignKey("UserEntityId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", null)
                        .WithMany("Permissions")
                        .HasForeignKey("PermissionEntityId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("Permissions")
                        .HasForeignKey("RoleId");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("RoleUsers")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("RoleUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesInvoices")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "DocumentId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Document");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "DocumentId");

                    b.Navigation("Company");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", null)
                        .WithMany("Tickets")
                        .HasForeignKey("CompanyEntityId");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("UserOTPs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("UserTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CompanyNotifs");

                    b.Navigation("Employees");

                    b.Navigation("PaymentDetails");

                    b.Navigation("SalesInvoices");

                    b.Navigation("SalesOrders");

                    b.Navigation("Tickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Navigation("CashBacks");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Navigation("CompanyNotifs");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Navigation("Permissions");

                    b.Navigation("RoleUsers");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("MacAddresses");

                    b.Navigation("RoleUsers");

                    b.Navigation("SubscriberDevices");

                    b.Navigation("UserOTPs");

                    b.Navigation("UserTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
