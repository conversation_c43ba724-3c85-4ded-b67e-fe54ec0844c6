{"AWSAccessKey": "********************", "AWS_SECRET_ACCESS_KEY": "WtR1F0vx1ashO4Kl3ggCaOOAJmlJUHKX1iBml3zQ", "AccessAddress": "https://localhost:7274", "NotificationAddress": "https://localhost:7038", "PaymentAddress": "https://localhost:7018", "PaymentBlockchainAddress": "https://ab0c-196-203-166-66.ngrok-free.app", "PlatformAdminAddress": "http://localhost:4200", "RedisAddress": "", "CamundaAddress": "http://localhost:8080/engine-rest", "KafkaServer": "b-2.dynokafkacluster.l184ii.c19.kafka.us-east-1.amazonaws.com:9092,b-1.dynokafkacluster.l184ii.c19.kafka.us-east-1.amazonaws.com:9092,b-3.dynokafkacluster.l184ii.c19.kafka.us-east-1.amazonaws.com:9092", "KafkaGroupId": "watchDog", "AWSS3URL": "https://dynofiles.s3.amazonaws.com", "LogoURL": "https://dynofiles.s3.amazonaws.com/Images/Logo.png", "IPServer": "database-1.c9y0a2c0g03h.us-east-1.rds.amazonaws.com", "Port": 5432, "User": "postgres", "Password": "Dyno123++", "AccessManagement": "AccessManagement", "Key": "7e3344ab-22b2-497d-a294-a998f9950723", "TokenLifeTime": 120, "Issuer": "SecureApi", "RefreshTokenLifeTime": 30, "ValidIssuer": "SecureApi", "NEO4J_URI": "neo4j+s://fff50857.databases.neo4j.io", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "kaVIdZtw8n_SKgaMbeG_ipudkBlcEYAuV2kzEUBQIDQ", "AURA_INSTANCEID": "fff50857", "AURA_INSTANCENAME": "Instance01", "EmailFrom": "<EMAIL>", "SmtpServer": "ssl0.ovh.net", "EmailPort": 465, "Email": "<EMAIL>", "EmailPassword": "DynoInfo123"}