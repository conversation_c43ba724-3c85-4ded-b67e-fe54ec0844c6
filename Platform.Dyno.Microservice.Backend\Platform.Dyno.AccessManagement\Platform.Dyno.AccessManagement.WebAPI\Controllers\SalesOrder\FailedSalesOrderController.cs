﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.WebAPI.Controllers.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.SalesOrder
{
    [Route("Api/[controller]")]
    [ApiController]
    public class FailedSalesOrderController : Controller
    {
        private readonly IFailedSalesOrderService _failedSalesOrderService;
        private readonly IUserService _userService;
        private readonly ILogger<FailedSalesOrderController> _logger;
        private readonly ISortingUtility _sortingUtility;
        public FailedSalesOrderController(IFailedSalesOrderService failedSalesOrderService,
            IUserService userService,
            ILogger<FailedSalesOrderController> logger,
            ISortingUtility sortingUtility)
        {
            _failedSalesOrderService = failedSalesOrderService;
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<FailedSalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<FailedSalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<FailedSalesOrderDTO>> failedSalesOrders = _failedSalesOrderService.GetAll(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(failedSalesOrders);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<FailedSalesOrderDTO>> response = new ResponseAPI<List<FailedSalesOrderDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<PagedList<FailedSalesOrderDTO>> salesOrdersDTO = _failedSalesOrderService.GetAll(companyId, isSuperAdminResponse.ObjectValue, pagedParameters);
                if (salesOrdersDTO.ObjectValue != null)
                {
                    salesOrdersDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, salesOrdersDTO);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = salesOrdersDTO.ObjectValue.TotalCount,
                        PageSize = salesOrdersDTO.ObjectValue.PageSize,
                        CurrentPage = salesOrdersDTO.ObjectValue.CurrentPage,
                        TotalPages = salesOrdersDTO.ObjectValue.TotalPages,
                        HasNext = salesOrdersDTO.ObjectValue.HasNext,
                        HasPrevious = salesOrdersDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(salesOrdersDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

       
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] FailedSalesOrderDTO failedSalesOrderDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<FailedSalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<FailedSalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<FailedSalesOrderDTO> failedSalesOrderDTOResponse = _failedSalesOrderService.Create(companyId, isSuperAdminResponse.ObjectValue, failedSalesOrderDTO, userId);
                if (failedSalesOrderDTOResponse.ObjectValue == null)
                {
                    return BadRequest(Response);
                }
                return Ok(failedSalesOrderDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<FailedSalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

    }
}
