﻿using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.Business.IService
{
    public interface ISnsService
    {
        Task<ResponseAPI<string>> SendNotification(string message, string topicArn, string targetArn,
            string messageGroupId, string subject);
        Task<ResponseAPI<string>> CreateTopic(string topicName);
        Task<ResponseAPI<string>> DeleteTopic(string topicArn);
        Task<ResponseAPI<string>> CreateEndPoint(string endpoint, string platformApplicationArn);
        Task<ResponseAPI<string>> Subscribe(string protocol, string topicArn, string endPoint);
        Task<ResponseAPI<string>> Unsubscribe(string subscriptionArn);
    }
}
