﻿using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.Shared.RefData;
using System.Text.Json.Serialization;

namespace Platform.Dyno.AccessManagement.BusinessModel.Address
{
    public class MacAddressBM : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public string MacAddress { get; set; } = string.Empty;
        public bool IsConfirmed { get; set; } = false;
        public bool IsSaved { get; set; } = false;
        #endregion

        #region Structure
        public UserBM? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
