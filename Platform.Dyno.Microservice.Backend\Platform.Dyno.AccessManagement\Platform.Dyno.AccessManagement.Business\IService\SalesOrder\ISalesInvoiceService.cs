﻿using Platform.Dyno.AccessManagement.Business.Bpmn;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using Platform.Dyno.Shared.SharedClass.Generic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.SalesOrder
{
    public interface ISalesInvoiceService : IGenericService<SalesInvoiceDTO>
    {
        ResponseAPI<PagedList<SalesInvoiceDTO>> GetByStatus(Guid companyId, bool isSuperAdmin, List<InvoiceStatus> invoiceStatus, PagedParameters pagedParameters);
        SalesInvoiceDTO? GetSalesInvoiceBySalesOrderId(Guid salesOrderId);
        ResponseAPI<SalesInvoiceDTO> UpdateStatus(Guid salesInvoiceId, InvoiceStatus salesInvoiceStatus, bool isSuperAdmin, Guid? companyId = null, Guid? updateUserId = null, bool updateCache = true, Guid? documentId = null);
        Task<ResponseAPI<string>> StartProcess(SalesInvoiceDTO salesInvoiceDTO);
        ResponseAPI<SalesInvoiceDTO> AddInstanceToSI(Guid salesInvoiceId, string instantanceId);
    }
}
