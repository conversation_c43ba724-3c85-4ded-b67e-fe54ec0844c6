﻿Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessMangement\Platform.Dyno.AccessManagement.DataModel\Platform.Dyno.AccessManagement.DataModel.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessMangement\Platform.Dyno.AccessManagement.EF\Platform.Dyno.AccessManagement.EF.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.AccessMangement\Platform.Dyno.AccessManagement.WebAPI\Platform.Dyno.AccessManagement.WebAPI.csproj...
  CACHE https://api.nuget.org/v3/vulnerabilities/index.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/vulnerability.base.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/2025.01.29.10.10.52/vulnerability.update.json
  CACHE https://api.nuget.org/v3/vulnerabilities/index.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/vulnerability.base.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/2025.01.29.10.10.52/vulnerability.update.json
  CACHE https://api.nuget.org/v3/vulnerabilities/index.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/vulnerability.base.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/2025.01.29.10.10.52/vulnerability.update.json
NU1701: Le package 'Microsoft.Owin 4.2.2' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1701: Le package 'Owin 1.0.0' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1605: Avertissement comme erreur : Passage à une version antérieure du package détecté : Microsoft.Extensions.Configuration de 8.0.0 à 7.0.0. Référencez le package directement à partir du projet pour sélectionner une version différente. 
 Platform.Dyno.AccessManagement.DataModel -> Platform.Dyno.Shared -> EPPlus 7.1.0 -> Microsoft.Extensions.Configuration.Json 8.0.0 -> Microsoft.Extensions.Configuration (>= 8.0.0) 
 Platform.Dyno.AccessManagement.DataModel -> Platform.Dyno.Shared -> Microsoft.Extensions.Configuration (>= 7.0.0)
NU1202: Le package Microsoft.AspNetCore.Identity.EntityFrameworkCore 9.0.1 n'est pas compatible avec net8.0 (.NETCoreApp,Version=v8.0). Le package Microsoft.AspNetCore.Identity.EntityFrameworkCore 9.0.1 prend en charge : net9.0 (.NETCoreApp,Version=v9.0)
  CACHE https://api.nuget.org/v3/vulnerabilities/index.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/vulnerability.base.json
  CACHE https://api.nuget.org/v3-vulnerabilities/2025.01.28.05.41.07/2025.01.29.10.10.52/vulnerability.update.json
NU1701: Le package 'Microsoft.Owin 4.2.2' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net6.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1701: Le package 'Owin 1.0.0' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net6.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1202: Le package Microsoft.EntityFrameworkCore 9.0.1 n'est pas compatible avec net6.0 (.NETCoreApp,Version=v6.0). Le package Microsoft.EntityFrameworkCore 9.0.1 prend en charge : net8.0 (.NETCoreApp,Version=v8.0)
NU1202: Le package Microsoft.AspNetCore.Authentication.JwtBearer 9.0.1 n'est pas compatible avec net6.0 (.NETCoreApp,Version=v6.0). Le package Microsoft.AspNetCore.Authentication.JwtBearer 9.0.1 prend en charge : net9.0 (.NETCoreApp,Version=v9.0)
NU1608: Version du package détectée en dehors de la contrainte de dépendance : Npgsql.EntityFrameworkCore.PostgreSQL 7.0.4 nécessite Microsoft.EntityFrameworkCore (>= 7.0.5 && < 8.0.0) alors que la version Microsoft.EntityFrameworkCore 9.0.1 a été résolue.
NU1608: Version du package détectée en dehors de la contrainte de dépendance : Npgsql.EntityFrameworkCore.PostgreSQL 7.0.4 nécessite Microsoft.EntityFrameworkCore.Abstractions (>= 7.0.5 && < 8.0.0) alors que la version Microsoft.EntityFrameworkCore.Abstractions 9.0.1 a été résolue.
NU1701: Le package 'Microsoft.Owin 4.2.2' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1701: Le package 'Owin 1.0.0' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1107: Conflit de version détecté pour Microsoft.EntityFrameworkCore.Relational. Installez/référencez Microsoft.EntityFrameworkCore.Relational 9.0.1 directement au projet Platform.Dyno.AccessManagement.EF pour résoudre ce problème. 
 Platform.Dyno.AccessManagement.EF -> Microsoft.EntityFrameworkCore.Design 9.0.1 -> Microsoft.EntityFrameworkCore.Relational (>= 9.0.1) 
 Platform.Dyno.AccessManagement.EF -> Npgsql.EntityFrameworkCore.PostgreSQL 7.0.4 -> Microsoft.EntityFrameworkCore.Relational (>= 7.0.5 && < 8.0.0).
NU1701: Le package 'Microsoft.Owin 4.2.2' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1701: Le package 'Owin 1.0.0' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net8.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.
NU1107: Conflit de version détecté pour Microsoft.EntityFrameworkCore.Relational. Installez/référencez Microsoft.EntityFrameworkCore.Relational 9.0.1 directement au projet Platform.Dyno.AccessManagement.WebAPI pour résoudre ce problème. 
 Platform.Dyno.AccessManagement.WebAPI -> Microsoft.EntityFrameworkCore.Design 9.0.1 -> Microsoft.EntityFrameworkCore.Relational (>= 9.0.1) 
 Platform.Dyno.AccessManagement.WebAPI -> Npgsql.EntityFrameworkCore.PostgreSQL 7.0.4 -> Microsoft.EntityFrameworkCore.Relational (>= 7.0.5 && < 8.0.0).
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.Notification\Platform.Dyno.Notification\Platform.Dyno.Notification.WebAPI.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Infrastructure\Infrastructure.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Server.Kafka\Server.Kafka\Server.Kafka.csproj...
Restauration des packages pour C:\Dyno\Dyno Code\BitBucket\Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.WebAPI\Platform.Dyno.Payment.WebAPI.csproj...
