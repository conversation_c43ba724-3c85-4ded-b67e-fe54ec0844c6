﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.EF.DefaultMigration
{
    public class DefaultPermission : IEntityTypeConfiguration<PermissionEntity>
    {
        private readonly ConfigurationDefaultId _defaultId;

        public DefaultPermission(ConfigurationDefaultId defaultId)
        {
            _defaultId = defaultId;
        }

        public void Configure(EntityTypeBuilder<PermissionEntity> builder)
        {
            builder.HasData(
            #region super admin role permissions
                new PermissionEntity
                {
                    Id = _defaultId.CompanyManagementPermissionId,
                    Name = Permissions.CompanyManagement,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyPermissionId,
                    Name = Permissions.Company,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyManagementPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.AddCompanyPermissionId,
                    Name = Permissions.AddCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ViewCompanyPermissionId,
                    Name = Permissions.ViewCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.UpdateCompanyPermissionId,
                    Name = Permissions.UpdateCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DeleteCompanyPermissionId,
                    Name = Permissions.DeleteCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.AuthentificationPermissionId,
                    Name = Permissions.Authentification,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.UserPermissionId,
                    Name = Permissions.User,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.AuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.AddUserPermissionId,
                    Name = Permissions.AddUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.UserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ViewUserPermissionId,
                    Name = Permissions.ViewUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.UserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.UpdateUserPermissionId,
                    Name = Permissions.UpdateUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.UserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DeleteUserPermissionId,
                    Name = Permissions.DeleteUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.UserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.RolePermissionId,
                    Name = Permissions.Role,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.AuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.AddRolePermissionId,
                    Name = Permissions.AddRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.RolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ViewRolePermissionId,
                    Name = Permissions.ViewRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.RolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.UpdateRolePermissionId,
                    Name = Permissions.UpdateRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.RolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DeleteRolePermissionId,
                    Name = Permissions.DeleteRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleSuperAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.RolePermissionId
                },

            #endregion
            #region default role permissions

                new PermissionEntity
                {
                    Id = _defaultId.DefaultCompanyManagementPermissionId,
                    Name = Permissions.CompanyManagement,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultCompanyPermissionId,
                    Name = Permissions.Company,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultCompanyManagementPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultAddCompanyPermissionId,
                    Name = Permissions.AddCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultViewCompanyPermissionId,
                    Name = Permissions.ViewCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultUpdateCompanyPermissionId,
                    Name = Permissions.UpdateCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultDeleteCompanyPermissionId,
                    Name = Permissions.DeleteCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultAuthentificationPermissionId,
                    Name = Permissions.Authentification,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultUserPermissionId,
                    Name = Permissions.User,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultAddUserPermissionId,
                    Name = Permissions.AddUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultViewUserPermissionId,
                    Name = Permissions.ViewUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultUpdateUserPermissionId,
                    Name = Permissions.UpdateUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultDeleteUserPermissionId,
                    Name = Permissions.DeleteUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultRolePermissionId,
                    Name = Permissions.Role,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultAddRolePermissionId,
                    Name = Permissions.AddRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultViewRolePermissionId,
                    Name = Permissions.ViewRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultUpdateRolePermissionId,
                    Name = Permissions.UpdateRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.DefaultDeleteRolePermissionId,
                    Name = Permissions.DeleteRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = null,
                    RoleId = _defaultId.RoleId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.DefaultRolePermissionId
                },

            #endregion
            #region Company role permissions

                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminCompanyManagementPermissionId,
                    Name = Permissions.CompanyManagement,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminCompanyPermissionId,
                    Name = Permissions.Company,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminCompanyManagementPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminAddCompanyPermissionId,
                    Name = Permissions.AddCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminViewCompanyPermissionId,
                    Name = Permissions.ViewCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminUpdateCompanyPermissionId,
                    Name = Permissions.UpdateCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminDeleteCompanyPermissionId,
                    Name = Permissions.DeleteCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminAuthentificationPermissionId,
                    Name = Permissions.Authentification,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminUserPermissionId,
                    Name = Permissions.User,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminAddUserPermissionId,
                    Name = Permissions.AddUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminViewUserPermissionId,
                    Name = Permissions.ViewUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminUpdateUserPermissionId,
                    Name = Permissions.UpdateUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminDeleteUserPermissionId,
                    Name = Permissions.DeleteUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminRolePermissionId,
                    Name = Permissions.Role,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminAddRolePermissionId,
                    Name = Permissions.AddRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminViewRolePermissionId,
                    Name = Permissions.ViewRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminUpdateRolePermissionId,
                    Name = Permissions.UpdateRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.CompanyAdminDeleteRolePermissionId,
                    Name = Permissions.DeleteRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleCompanyAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.CompanyAdminRolePermissionId
                },

            #endregion
            #region Shop role permissions

                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminCompanyManagementPermissionId,
                    Name = Permissions.CompanyManagement,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminCompanyPermissionId,
                    Name = Permissions.Company,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminCompanyManagementPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminAddCompanyPermissionId,
                    Name = Permissions.AddCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminViewCompanyPermissionId,
                    Name = Permissions.ViewCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminUpdateCompanyPermissionId,
                    Name = Permissions.UpdateCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminDeleteCompanyPermissionId,
                    Name = Permissions.DeleteCompanyManagement,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminCompanyPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminAuthentificationPermissionId,
                    Name = Permissions.Authentification,
                    IsRoot = true,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminUserPermissionId,
                    Name = Permissions.User,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminAddUserPermissionId,
                    Name = Permissions.AddUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminViewUserPermissionId,
                    Name = Permissions.ViewUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminUpdateUserPermissionId,
                    Name = Permissions.UpdateUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminDeleteUserPermissionId,
                    Name = Permissions.DeleteUser,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminUserPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminRolePermissionId,
                    Name = Permissions.Role,
                    IsRoot = false,
                    IsLeaf = false,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminAuthentificationPermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminAddRolePermissionId,
                    Name = Permissions.AddRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminViewRolePermissionId,
                    Name = Permissions.ViewRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminUpdateRolePermissionId,
                    Name = Permissions.UpdateRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminRolePermissionId
                },
                new PermissionEntity
                {
                    Id = _defaultId.ShopAdminDeleteRolePermissionId,
                    Name = Permissions.DeleteRole,
                    IsRoot = false,
                    IsLeaf = true,
                    PartialSelected = false,
                    RoleId = _defaultId.RoleShopAdminId,
                    Status = Status.Active,
                    CreatorUserId = _defaultId.UserId,
                    PermissionEntityId = _defaultId.ShopAdminRolePermissionId
                }

                #endregion

            );

        }
    }
}
