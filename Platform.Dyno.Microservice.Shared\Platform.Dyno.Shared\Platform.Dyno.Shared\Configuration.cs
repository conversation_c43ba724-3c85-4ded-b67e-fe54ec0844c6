﻿using Amazon.Runtime;
using Amazon.S3.Model;
using Amazon.S3;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Platform.Dyno.Shared
{
    public class Configuration
    {
        #region AWS_Key
        public string AWSAccessKey { get; } = string.Empty;
        public string AWSPrivateKey { get; } = string.Empty;
        public string AWSBucketName { get; } = string.Empty;
        public string AWSS3URL { get; } = string.Empty;
        public string AWSKey { get;} = string.Empty;
        
        #endregion

        #region Microservice

        #region AccessManagement
        public string AccessManagementAddress { get; } = string.Empty;
        #endregion

        #region Notification
        public string NotificationAddress { get; } = string.Empty;
        #endregion

        #region Payment
        public string PaymentAddress { get; } = string.Empty;
        #endregion

        #region PaymentBlockchain
        public string PaymentBlockchainAddress { get; } = string.Empty;
        #endregion

        #region PlatformAdminIpAddress
        public string PlatformAdminAddress { get; } = string.Empty;
        #endregion

        public string LogoURL { get; } = string.Empty;

        #endregion

        #region Redis
        public string RedisAddress { get; } = string.Empty;
        public string RedisUser { get; } = string.Empty;
        public string RedisPassword { get; }   = string.Empty;
        #endregion

        #region Camunda
        public string CamundaAddress { get; } = string.Empty;
        #endregion

        #region Kafka
        public string KafkaServer { get; } = string.Empty;
        public string KafkaGroupId { get; } = string.Empty;
        #endregion

        #region Connection Database
        public string IPServer { get;} = string.Empty;
        public string Port { get;} = string.Empty;
        public string User { get;} = string.Empty;
        public string Password { get;} = string.Empty;
        public string AccessManagement { get;} = string.Empty;
        #endregion

        #region Token Variable
        public string Key { get; } = string.Empty;
        public string TokenLifeTime { get; } = string.Empty;
        public string Issuer { get; } = string.Empty;
        public string RefreshTokenLifeTime { get; } = string.Empty;
        public string ValidIssuer { get; } = string.Empty;
        #endregion

        #region Neo4j
        public string NEO4J_URI { get; } = string.Empty;
        public string NEO4J_USERNAME { get; } = string.Empty;
        public string NEO4J_PASSWORD { get; } = string.Empty;
       // public string AURA_INSTANCEID { get; } = string.Empty;
       // public string AURA_INSTANCENAME { get; } = string.Empty;
        #endregion

        #region Email Config
        public string EmailFrom { get;} = string.Empty;
        public string SmtpServer { get;} = string.Empty;
        public string EmailPort { get;} = string.Empty;
        public string Email { get;} = string.Empty;
        public string EmailPassword { get;} = string.Empty;
        #endregion

       
        public Configuration()
        {
            string jsonFilePath = "Configuration/config.json";

            if (File.Exists(jsonFilePath))
            {
                var json = File.ReadAllText(jsonFilePath);
                var data = JsonConvert.DeserializeObject<AWSConfig>(json);

                #region AWS_Key
                AWSAccessKey = data.AWSAccessKey;
                AWSPrivateKey = data.AWSPrivateKey;
                AWSBucketName= data.AWSBucketName;
                AWSKey= data.AWSKey;
                #endregion

                var s3Client = new AmazonS3Client(new BasicAWSCredentials(AWSAccessKey, AWSPrivateKey), RegionEndpoint.USEast1);

                var getObjectRequest = new GetObjectRequest
                {
                    BucketName = AWSBucketName,
                    Key = AWSKey
                };

                using (var response = s3Client.GetObjectAsync(getObjectRequest).Result)
                {
                    using (var streamReader = new StreamReader(response.ResponseStream))
                    {
                        var content = streamReader.ReadToEndAsync().Result;
                        var jsonData = JsonConvert.DeserializeObject<Config>(content);

                        #region Microservice
                        AccessManagementAddress = jsonData.AccessManagementAddress;
                        NotificationAddress = jsonData.NotificationAddress;
                        PaymentAddress = jsonData.PaymentAddress;
                        PaymentBlockchainAddress = jsonData.PaymentBlockchainAddress;
                        PlatformAdminAddress = jsonData.PlatformAdminAddress;
                        AWSS3URL = jsonData.AWSS3URL;
                        LogoURL = jsonData.LogoURL;
                        #endregion

                        #region Redis
                        RedisAddress = jsonData.RedisAddress;
                        RedisUser = jsonData.RedisUser;
                        RedisPassword = jsonData.RedisPassword;
                        #endregion

                        #region Camunda
                        CamundaAddress = jsonData.CamundaAddress;
                        #endregion

                        #region Kafka
                        KafkaServer = jsonData.KafkaServer;
                        KafkaGroupId = jsonData.KafkaGroupId;
                        #endregion

                        #region Connection Database
                        IPServer= jsonData.IPServer;
                        Port= jsonData.Port;
                        User= jsonData.User;
                        Password= jsonData.Password;
                        AccessManagement = jsonData.AccessManagement;
                        #endregion

                        #region Token Variable
                        Key = jsonData.Key;
                        TokenLifeTime = jsonData.TokenLifeTime;
                        Issuer = jsonData.Issuer;
                        RefreshTokenLifeTime = jsonData.RefreshTokenLifeTime;
                        ValidIssuer = jsonData.ValidIssuer;
                        #endregion

                        #region Neo4j
                        NEO4J_URI = jsonData.NEO4J_URI;
                        NEO4J_USERNAME = jsonData.NEO4J_USERNAME;
                        NEO4J_PASSWORD = jsonData.NEO4J_PASSWORD;
                        //AURA_INSTANCEID = jsonData.AURA_INSTANCEID;
                        //AURA_INSTANCENAME = jsonData.AURA_INSTANCENAME;
                        #endregion

                        #region Email Config
                        EmailFrom = jsonData.EmailFrom;
                        SmtpServer= jsonData.SmtpServer;
                        EmailPort = jsonData.EmailPort;
                        Email = jsonData.Email;
                        EmailPassword= jsonData.EmailPassword;
                        #endregion
                    }
                }


            }
            else
            {
                // Handle the case when the JSON file doesn't exist.
                // You can set default values or throw an exception.
            }
        }

        private class AWSConfig
        {
            #region AWS_Key
            public string AWSAccessKey { get; set; } = string.Empty;
            public string AWSPrivateKey { get; set; } = string.Empty;
            public string AWSBucketName { get; set; } = string.Empty;
            public string AWSKey { get; set; } = string.Empty;
            #endregion
        }

        public class Config
        {
            

            #region Microservice

            #region Access Management
            public string AccessManagementAddress { get; set; } = string.Empty;
            #endregion

            #region Notification
            public string NotificationAddress { get; set; } = string.Empty;
            #endregion

            #region Payment
            public string PaymentAddress { get; set; } = string.Empty;
            #endregion

            #region PaymentBlockchain
            public string PaymentBlockchainAddress { get; set; } = string.Empty;

            #endregion

            #region PlatformAdminAddress
            public string PlatformAdminAddress { get; set; } = string.Empty;
            #endregion

            public string AWSS3URL { get; set; } = string.Empty;
            public string LogoURL { get; set; } = string.Empty;

            #endregion

            #region Redis
            public string RedisAddress { get; set; } = string.Empty;
            public string RedisHost { get; set; } = string.Empty;
            public string RedisPort { get; set; } = string.Empty;
            public string RedisUser { get; set; } = string.Empty;
            public string RedisPassword { get; set; } = string.Empty;
            #endregion

            #region Camunda
            public string CamundaAddress { get; set; } = string.Empty;
            #endregion

            #region Kafka
            public string KafkaServer { get; set; } = string.Empty;
            public string KafkaGroupId { get; set; } = string.Empty;
            #endregion

            #region Connection Database
            public string IPServer { get; set; } = string.Empty;
            public string Port { get; set; } = string.Empty;
            public string User { get; set; } = string.Empty;
            public string Password { get; set; } = string.Empty;
            public string AccessManagement { get; set; } = string.Empty;
            #endregion

            #region Token Variable
            public string Key { get; set; } = string.Empty;
            public string TokenLifeTime { get; set; } = string.Empty;
            public string Issuer { get; set; } = string.Empty;
            public string RefreshTokenLifeTime { get; set; } = string.Empty;
            public string ValidIssuer { get; set; } = string.Empty;
            #endregion

            #region Neo4j
            public string NEO4J_URI { get; set; } = string.Empty;
            public string NEO4J_USERNAME { get; set; } = string.Empty;
            public string NEO4J_PASSWORD { get; set; } = string.Empty;
            public string AURA_INSTANCEID { get; set; } = string.Empty;
            public string AURA_INSTANCENAME { get; set; } = string.Empty;
            #endregion

            #region Email Config
            public string EmailFrom { get; set; } = string.Empty;
            public string SmtpServer { get; set; } = string.Empty;
            public string EmailPort { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string EmailPassword { get; set; } = string.Empty;
            #endregion

        }
    }
}
