﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.DTO.Emailing
{
    public class EmailConfiguration
    {
        public string EmailFrom { get; set; } = string.Empty;
        public string SmtpServer { get; set; } = string.Empty;
        public int EmailPort { get; set; }
        public string Email { get; set; } = string.Empty;
        public string EmailPassword { get; set; } = string.Empty;
    }
}
