﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="IServices\**" />
    <Compile Remove="Services\**" />
    <EmbeddedResource Remove="IServices\**" />
    <EmbeddedResource Remove="Services\**" />
    <None Remove="IServices\**" />
    <None Remove="Services\**" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\number_to_word_converter\1.1.8\contentFiles\any\netstandard2.0\readme.txt" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\select.htmltopdf.netcore\23.2.0\contentFiles\any\any\Select.Html.dep" />
    <Content Remove="C:\Users\<USER>\.nuget\packages\select.htmltopdf\23.2.0\contentFiles\any\any\Select.Html.dep" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Bpmn\SalesInvoiceWorkflow.bpmn" />
    <None Remove="Bpmn\SalesOrderWorkflow.bpmn" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Bpmn\SalesOrderWorkflow.bpmn" />
    <EmbeddedResource Include="Bpmn\SalesInvoiceWorkflow.bpmn" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="Camunda.Api.Client" />
    <PackageReference Include="Camunda.Worker" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="QuestPDF" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj" />
    <ProjectReference Include="..\..\Platform.Dyno.Notification\Platform.Dyno.Notification.DTO\Platform.Dyno.Notification.DTO.csproj" />
    <ProjectReference Include="..\..\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.DTO\Platform.Dyno.Payment.DTO.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.DTO\Platform.Dyno.AccessManagement.DTO.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.EF\Platform.Dyno.AccessManagement.EF.csproj" />
  </ItemGroup>
</Project>