﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Security.Cryptography.Xml;


namespace Platform.Dyno.AccessManagement.Business.Service.RoleManagement
{
    public class RoleService : IRoleService
    {
        private readonly IUnitOfWork<RoleEntity> _roleRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _roleCacheKey = RedisCacheKey.RoleCacheKey;
        private readonly IPermissionService _permissionService;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("role");
        public RoleService(IUnitOfWork<RoleEntity> roleRepository,
            IMapper mapper,
            IRedisCacheService cache,
            IPermissionService permissionService)
        {
            _roleRepository = roleRepository;
            _mapper = mapper;
            _cache = cache;
            _permissionService = permissionService;
        }

        #region Get
        public ResponseAPI<List<RoleDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if(userType == UserType.Client || userType == UserType.Cashier )
            {
                return new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            List<RoleDTO>? rolesDTO = _cache.GetData<List<RoleDTO>>(_roleCacheKey);
            
            if (rolesDTO == null || rolesDTO.Count == 0)
            {
                List<RoleEntity> rolesEntities;
                if (companyId==null)
                {
                    rolesEntities = (List<RoleEntity>)_roleRepository.Repository.GetAll(includes: new List<string> { "RoleUsers" }, orderBy: role => role.OrderByDescending(role => role.LastModificationTime));
                }
                else if (userType == UserType.Company)
                {
                    List<string> includeNames = new List<string> { "admin"};
                    rolesEntities = (List<RoleEntity>)_roleRepository.Repository.GetAll(x => includeNames.Contains(x.Name.ToLower()) || x.CompanyId ==companyId , includes: new List<string> { "RoleUsers" }, orderBy: role => role.OrderByDescending(role => role.LastModificationTime));
                }
                else if (userType == UserType.ShopOwner)
                {
                    List<string> includeNames = new List<string> { "shop admin", "cashier" };
                    rolesEntities = (List<RoleEntity>)_roleRepository.Repository.GetAll(x => includeNames.Contains(x.Name.ToLower()) || x.CompanyId == companyId, includes: new List<string> { "RoleUsers" }, orderBy: role => role.OrderByDescending(role => role.LastModificationTime));
                }
                else
                {
                    List<string> includeNames = new List<string> { "shop admin", "admin" ,"superadmin"};
                    rolesEntities = (List<RoleEntity>)_roleRepository.Repository.GetAll(x => includeNames.Contains(x.Name.ToLower()) || x.CompanyId == companyId, includes: new List<string> { "RoleUsers" }, orderBy: role => role.OrderByDescending(role => role.LastModificationTime));
                };
                List<RoleBM> roles = _mapper.Map<List<RoleBM>>(rolesEntities);
                rolesDTO = _mapper.Map<List<RoleDTO>>(roles);
                foreach (var role in rolesDTO)
                {
                    ResponseAPI<List<PermissionDTO>> permissions = _permissionService.Get(permission => permission.RoleId == role.Id
                    && permission.Status == Status.Active);
                    role.Permissions = permissions.ObjectValue;
                }
                _cache.SetData(_roleCacheKey, rolesDTO, DateTimeOffset.UtcNow.AddDays(1));

            }
            ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = rolesDTO,
            };
            return response;
        }
        public ResponseAPI<PagedList<RoleDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<RoleDTO>> rolesDTO = GetAll(companyId, userType);
            PagedList<RoleDTO>? pagedList = null;
            if (rolesDTO.ObjectValue != null)
            {
                pagedList = PagedList<RoleDTO>.ToGenericPagedList(rolesDTO.ObjectValue.Where(role => role.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<RoleDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<RoleDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<RoleDTO>> rolesDTO = GetAll(companyId, userType);
            PagedList<RoleDTO>? pagedList = null;
            if (rolesDTO.ObjectValue != null)
            {
                pagedList = PagedList<RoleDTO>.ToGenericPagedList(rolesDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<RoleDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<RoleDTO> Get(Guid id)
        {
            RoleDTO? roleDTO = GetAll().ObjectValue?.Where(role => role.Id == id).FirstOrDefault();
            return new ResponseAPI<RoleDTO>()
            {
                StatusCode = roleDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = roleDTO == null ? $"Role with id {id} Not Found !" : null,
                ObjectValue = roleDTO
            };
        }

        public ResponseAPI<List<RoleDTO>> Get(Func<RoleDTO, bool> expression)
        {
            List<RoleDTO>? rolesDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<RoleDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = rolesDTO
            };
        }
        #endregion

        #region Create
        public ResponseAPI<RoleDTO> Create(RoleDTO dtoObject, Guid? creatorUserId, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            if (userType == UserType.Client || userType == UserType.Cashier)
            {
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            if (dtoObject.Name?.ToLower()=="client" || dtoObject.Name?.ToLower() == "superadmin" || dtoObject.Name?.ToLower() == "shop admin" || dtoObject.Name?.ToLower() == "admin" || dtoObject.Name?.ToLower() == "cashier" || dtoObject.Name?.ToLower() == "default")
            {
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "You can not create a role with the name Client or Superadmin, Admin or Shop Admin"

                };
            }

            List<RoleDTO>? roles = Get(role => role.Name?.ToUpper() == dtoObject.Name?.ToUpper() &&
            role.CompanyId == companyId && role.Status != Status.Deleted).ObjectValue;

            if(roles != null && roles.Count > 0 )
            {
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "You can not create a role with the same name !"

                };
            }



            RoleBM role = _mapper.Map<RoleBM>(dtoObject);

            #region changed data
            role.Id = Guid.NewGuid();
            role.CompanyId = companyId;
            RefDataService<RoleBM>.CreateRefData(role, companyId);
            #endregion

            RoleEntity roleEntity = _mapper.Map<RoleEntity>(role);

            List<PermissionEntity>? permissionEntities = (List<PermissionEntity>?)roleEntity.Permissions;


            roleEntity.Permissions = _permissionService.CreatePermissionList(permissionEntities, role.Id, dtoObject.Users.FirstOrDefault());

            _roleRepository.Repository.Insert(roleEntity);
            _roleRepository.Save();

            dtoObject.Id = roleEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_roleCacheKey);
            }

            return new ResponseAPI<RoleDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }
        #endregion

        #region Update
        public ResponseAPI<RoleDTO> Update(RoleDTO dtoObject, Guid? updateUserId, bool updateCache = true)
        {
            if (dtoObject.Name?.ToLower() == "client" || dtoObject.Name?.ToLower() == "superadmin" || dtoObject.Name?.ToLower() == "shop admin" || dtoObject.Name?.ToLower() == "admin" || dtoObject.Name?.ToLower() == "cashier" || dtoObject.Name?.ToLower() == "default")
            {
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "You can not update role CLIENT and SUPERADMIN"
                };
            }
            RoleDTO? roleDTO = Get(dtoObject.Id).ObjectValue;
            if (roleDTO != null)
            {
                _permissionService.DeleteAllPermissionInRole(roleDTO.Id);
                roleDTO = GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).FirstOrDefault();
                RoleBM role = _mapper.Map<RoleBM>(dtoObject);

                #region RefData
                RefDataService<RoleBM>.UpdateRefData(role, updateUserId);
                role.CreationTime = roleDTO?.CreationTime;
                role.CreatorUserId = roleDTO?.CreatorUserId;
                #endregion

                RoleEntity roleEntity = _mapper.Map<RoleEntity>(role);
                List<PermissionEntity>? permissionEntities = (List<PermissionEntity>?)roleEntity.Permissions;
                roleEntity.Permissions = _permissionService.CreatePermissionList(permissionEntities, role.Id, dtoObject.Users.FirstOrDefault());
                List<PermissionBM> permissionBM = _mapper.Map<List<PermissionBM>>((List<PermissionEntity>?)roleEntity.Permissions);
                List<PermissionDTO> permissionDTO = _mapper.Map<List<PermissionDTO>>(permissionBM);
                foreach (var permission in permissionDTO)
                {
                    _permissionService.Create(permission);
                }
                roleEntity.Permissions = null;
                _roleRepository.Repository.Update(roleEntity);
                _roleRepository.Save();
                dtoObject.Id = roleEntity.Id;
                if (updateCache)
                {
                    _cache.RemoveData(_roleCacheKey);
                }
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = dtoObject
                };
            }
            return new ResponseAPI<RoleDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Role with id {dtoObject.Id} Not Found !"
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<RoleDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            RoleDTO? roleDTO = Get(id).ObjectValue;
            if (roleDTO?.Name?.ToLower() == "client" || roleDTO?.Name?.ToLower() == "superadmin" || roleDTO?.Name?.ToLower() == "shop admin" || roleDTO?.Name?.ToLower() == "admin" || roleDTO?.Name?.ToLower() == "cashier" || roleDTO?.Name?.ToLower() == "default")
            {
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "You can not delete role CLIENT and SUPERADMIN"
                };
            }
            _permissionService.DeleteAllPermissionInRole(id);
            if (roleDTO != null)
            {
                RoleBM role = _mapper.Map<RoleBM>(roleDTO);

                #region Refdata
                RefDataService<RoleBM>.DeleteRefData(role, deletorUserId);
                #endregion

                RoleEntity roleEntity = _mapper.Map<RoleEntity>(role);
                _roleRepository.Repository.Delete(roleEntity.Id);
                _roleRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_roleCacheKey);
                }
                return new ResponseAPI<RoleDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = roleDTO
                };
            }

            return new ResponseAPI<RoleDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Role with id {id} Not Found !"
            };
        }
        #endregion

    }
}
