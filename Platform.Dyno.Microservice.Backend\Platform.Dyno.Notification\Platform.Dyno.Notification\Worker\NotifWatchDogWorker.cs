﻿using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Tls;
using Platform.Dyno.Notification.Business.Service;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Server.Kafka;

namespace Platform.Dyno.Notification.WebAPI.Worker
{
    public class NotifWatchDogWorker : BackgroundService
    {
        private readonly IConsumer<WatchDogNotifDTO> _consumer;
        private CancellationToken _token;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ConfigurationDefaultId _configuration;
        public NotifWatchDogWorker(IConsumer<WatchDogNotifDTO> consumer,
            IServiceScopeFactory serviceScopeFactory,
            ConfigurationDefaultId configuration)
        {
            _consumer = consumer;
            _consumer._receiveEvent += ManageReceivedHeartBeatMessage;

            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _configuration = configuration;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _token = stoppingToken;
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }

        private async void ManageReceivedHeartBeatMessage(WatchDogNotifDTO message)
        {
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var notificationService = scope.ServiceProvider.GetRequiredService<NotificationService<WatchDogNotifDTO>>();
                Guid companyId = _configuration.CompanyId;
                UserType userType = UserType.SuperAdmin;
                string groupName = $"{companyId} {userType}";
                await notificationService.SendNotification(message, groupName);
            }

        }
    }
}
