﻿using Application.Models.BMs;
using Domain.Enums.Query;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface ITransactionService
    {
        public ResponseAPI<List<TransactionDTO>> GetAll(string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<List<TransactionDTO>> GetAllByUserType(UserType userType, string sort = "",
      Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<TransactionDTO> Get(Guid id);

        public ResponseAPI<bool> CheckQRcodeExist(Guid qrcode);
        public ResponseAPI<List<TransactionDTO>> GetUserReceivedTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<List<TransactionDTO>> GetUserSentTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<List<TransactionDTO>> GetUserTransactions(Guid userId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<double> GetCompanyReceivedTransactionsTotalAmountPerMonth(Guid companyId);
        public ResponseAPI<double> GetCompanySendedTransactionsTotalAmountPerMonth(Guid companyId);
        public ResponseAPI<List<TransactionDTO>> GetWalletReceivedTransactions(Guid walletId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<List<TransactionDTO>> GetWalletSentTransactions(Guid walletId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<List<TransactionDTO>> GetWalletTransactions(Guid walletId, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<TransactionDTO> Create(TransactionDTO transactionDTO, Guid userId,string pinCode);
        public ResponseAPI<TransactionDTO> CreatePreTransaction(TransactionDTO transactionDTO);
        public ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO,string pinCode);
        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionsForGroup(List<TransactionUserDTO> transactionUserDTOList, string pinCode);
        public ResponseAPI<TransactionDTO> CancelTransaction(Guid transactionId, string pinCode);
    }
}
