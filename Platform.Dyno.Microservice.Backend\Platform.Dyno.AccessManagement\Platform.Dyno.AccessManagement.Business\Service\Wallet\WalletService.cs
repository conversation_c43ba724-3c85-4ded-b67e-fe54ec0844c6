﻿using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Wallet
{
    public class WalletService:IWalletService
    {
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;
        public WalletService(IUserService userService,
            ICompanyService companyService)
        {
            _userService = userService;
            _companyService = companyService;
        }

        public List<WalletDTO> GetWalletsAssignedToName(List<WalletDTO> walletDTOList)
        {
            foreach (WalletDTO walletDTO in walletDTOList)
            {
                if(walletDTO.AssignedToType == UserType.Client || walletDTO.AssignedToType == UserType.Cashier)
                {
                    var user = _userService.Get(walletDTO.AssignedToId).ObjectValue;
                    walletDTO.AssignedToName = user?.FullName ?? "";
                }else
                {
                    var company = _companyService.Get(walletDTO.AssignedToId).ObjectValue;
                    walletDTO.AssignedToName = company?.Name ?? "";
                }
                                            
            }
            return walletDTOList;
        }

        public WalletDTO GetWalletAssignedToName(WalletDTO walletDTO)
        {
            var user = _userService.Get(walletDTO.AssignedToId).ObjectValue;          
            return walletDTO;
        }
    }
}
