﻿using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Notification
{
    public interface IEmailingService 
    {
        ResponseAPI<bool> SendResetPasswordEmail(ForgetPasswordDTO forgetPassword, string code);
        ResponseAPI<bool> SendMacAddressCodeEmail(string email, int code,LanguageType language);
        ResponseAPI<bool> sendConfirmationEmail(string email, string code, string token);
        ResponseAPI<bool> sendAddCompanyEmail(string email, string code);
        ResponseAPI<bool> sendSalesOrderEmail(string email, string template);
        ResponseAPI<bool> sendSalesInvoiceEmail(string email, string template);
        ResponseAPI<bool> sendCashBackEmail(CompanyDTO company, CashBackDTO cashBackDTO);
    }
}
