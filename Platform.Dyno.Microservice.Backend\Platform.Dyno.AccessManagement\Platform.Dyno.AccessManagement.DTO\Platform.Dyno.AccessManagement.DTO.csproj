﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="protobuf-net.Core" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj" />
    <ProjectReference Include="..\..\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.DTO\Platform.Dyno.Payment.DTO.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.BusinessModel\Platform.Dyno.AccessManagement.BusinessModel.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.DataModel\Platform.Dyno.AccessManagement.DataModel.csproj" />
  </ItemGroup>
</Project>