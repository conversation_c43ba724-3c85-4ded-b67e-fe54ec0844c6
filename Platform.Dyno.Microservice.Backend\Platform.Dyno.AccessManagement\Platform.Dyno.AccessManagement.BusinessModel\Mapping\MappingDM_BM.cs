﻿using AutoMapper;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.CashBack;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Group;
using Platform.Dyno.AccessManagement.BusinessModel.Logger;
using Platform.Dyno.AccessManagement.BusinessModel.Notification;
using Platform.Dyno.AccessManagement.BusinessModel.Reporting;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.BusinessModel.Ticket;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.CashBack;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Logger;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;

namespace Platform.Dyno.AccessManagement.BusinessModel.Mapping
{
    public class MappingDM_BM : Profile
    {
        public MappingDM_BM() {


            #region Company
            CreateMap<CompanyEntity, CompanyBM>().ReverseMap();
            //  .ForMember(companyBM => companyBM.Users, opt => opt.MapFrom(companyEntity => companyEntity.Users))
            //  .ForMember(companyBM => companyBM.Employees, opt => opt.MapFrom(companyEntity => companyEntity.Employees));
            CreateMap<PaymentDetailsEntity, PaymentDetailsBM>().ReverseMap();
            #endregion

            #region CashBack
            CreateMap<CashBackEntity, CashBackBM>().ReverseMap();
            CreateMap<FailedCashBackEntity, FailedCashBackBM>().ReverseMap();
            #endregion

            #region Group
            CreateMap<GroupEntity, GroupBM>().ReverseMap();
            #endregion

            #region User
            CreateMap<UserEntity, UserBM>()
                .ForMember(userBM => userBM.Password, opt => opt.MapFrom(userEntity => userEntity.PasswordHash))
                .ReverseMap()
                .ForMember(userEntity => userEntity.PasswordHash, opt => opt.MapFrom(userBM => userBM.Password))
                /*.ForMember(userEntity => userEntity.UserName, opt => opt.MapFrom(userBM => userBM.Id))
                .ForMember(userEntity => userEntity.NormalizedUserName, opt => opt.MapFrom(userBM => userBM.Id))*/
                .ForMember(userEntity => userEntity.Roles, opt => opt.Ignore());

            CreateMap<UserTokenEntity, UserTokenBM>().ReverseMap();
            CreateMap<UserOTPEntity, UserOTPBM>().ReverseMap();
            CreateMap<BlackListedUserEntity, BlackListedUserBM>().ReverseMap();

            #endregion

            #region Role
            CreateMap<RoleEntity, RoleBM>()
                .ReverseMap()
                .ForMember(userEntity => userEntity.Users, opt => opt.Ignore());
            CreateMap<PermissionEntity, PermissionBM>().ReverseMap();
            #endregion

            #region Address
            CreateMap<AddressEntity, AddressBM>().ReverseMap();
            CreateMap<MacAddressEntity, MacAddressBM>().ReverseMap();
            #endregion

            #region Notification
            CreateMap<SubscriberDeviceEntity, SubscriberDeviceBM>().ReverseMap();
            CreateMap<SignalRNotificationEntity, SignalRNotificationBM>().ReverseMap();
            #endregion

            #region Sales Order
            CreateMap<SalesOrderEntity,SalesOrderBM>().ReverseMap();
            CreateMap<FailedSalesOrderEntity, FailedSalesOrderBM>().ReverseMap();
            CreateMap<SalesInvoiceEntity, SalesInvoiceBM>().ReverseMap();
            #endregion

            #region ticket
            CreateMap<TicketEntity, TicketBM>().ReverseMap();
            #endregion

            #region Reporting
            CreateMap<DocumentsEntity, DocumentsBM>().ReverseMap();
            #endregion

            #region Logger
            CreateMap<LogErrorEntity, LogErrorBM>().ReverseMap();
            #endregion

        }
    }
}
