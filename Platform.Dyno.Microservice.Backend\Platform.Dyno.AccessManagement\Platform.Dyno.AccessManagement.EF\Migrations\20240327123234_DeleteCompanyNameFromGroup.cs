﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class DeleteCompanyNameFromGroup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7512033a-3ffb-4156-9c22-014b0b035348"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("80cb2a33-a443-492b-b005-eb51b674e504"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9c28fb26-9536-467e-8667-69716031350a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"));

            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "Group");

             migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("5ba25358-228b-40c5-b27c-6e1ebca68f90"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ca12e32c-1085-448b-b8a0-4958a3356060"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("cdcee1a2-ca9d-4b92-a4e3-22f8b244e68a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e7388a2d-3164-4923-b202-c18f68f4471a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "5b366b8f-c26f-43ce-8639-a26edb92f8ef", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8240) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0af4cefd-8e09-4695-b59a-7735ed791fbe", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8218) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a888c0c2-173d-4857-b881-0a3a0c6e2840", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8228) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "e81095d7-22a6-404e-a1fe-1a3725749d6b", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8244) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "14a3de50-a05a-415d-8c4b-3cc1b47a9f3e", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8236) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "600db60f-1f39-497b-8471-301b96b734e2", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8232) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "32d3b57e-4132-4dd0-8ef7-c34465f3e3e0", new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8635), new DateTime(2024, 3, 27, 12, 32, 33, 861, DateTimeKind.Utc).AddTicks(8635), "AQAAAAEAACcQAAAAELXdw/9cdr5i1qser1V0GuzRhSkgLf67lsjjmuw9XiH68AT0Lrw38x8/EwYVbnmHgQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("e7388a2d-3164-4923-b202-c18f68f4471a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6e75e872-c342-4b64-8048-f19c2014af58"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("5ba25358-228b-40c5-b27c-6e1ebca68f90"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("ca12e32c-1085-448b-b8a0-4958a3356060"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("5ba25358-228b-40c5-b27c-6e1ebca68f90"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("ca12e32c-1085-448b-b8a0-4958a3356060"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("cdcee1a2-ca9d-4b92-a4e3-22f8b244e68a"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("027e7f9e-2548-4ea5-8106-f82351942bed"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("14560ae2-2267-43bb-81ce-d16efccbd5f6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("16a37e97-460d-4c33-8895-36d97bc6e6d5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("22e6540e-7344-4830-94e9-074095b9feb3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2a51214a-5b86-4757-8f87-e9e31541df0b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("409c0fb9-d3f9-46a0-9206-34df9d7a9931"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6023ee65-1c4e-4e90-9134-7f814dda817b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6ec07f93-ae95-47cf-b8eb-aa4405ef1e24"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("72f361b3-6cd2-4bc4-bca5-9bfb7f708036"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7969e298-a7c1-4f19-9407-9e24ae25398b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("84095ea6-e6bc-4076-a1fc-b98db60cf978"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("6e75e872-c342-4b64-8048-f19c2014af58"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8ad1d88d-ef9f-4823-bc1c-c15933884bfc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("6e75e872-c342-4b64-8048-f19c2014af58"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("94e4b934-5528-4687-973c-2995347bccd8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9b876ff2-fb65-437e-bad3-fa941db7ae33"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b8ff37f4-fbb5-4d35-8708-3daf652b5c0e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("bb7c1084-9a9e-41c3-98bc-e7e74453ecbf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d61e7877-35ab-47a2-9758-c620b20d26c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("6e75e872-c342-4b64-8048-f19c2014af58"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d921ab36-c52c-4e94-bc8c-36b3333b96ea"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("def12f1c-4507-49de-bb40-b36e9bc33db3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("6e75e872-c342-4b64-8048-f19c2014af58"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e34d8ca2-7b77-4c41-b9e5-5d1b540c1436"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e6901a58-dd6c-41a1-baea-622cf89dd6f9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e81f2fc6-0990-4596-a0e3-fcfb98e5da71"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("edfcf5e7-d145-465f-8da9-3392b6d3b84e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ffeee9d6-35b8-4b61-8bc0-3055e9988709"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("027e7f9e-2548-4ea5-8106-f82351942bed"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("14560ae2-2267-43bb-81ce-d16efccbd5f6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("16a37e97-460d-4c33-8895-36d97bc6e6d5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("22e6540e-7344-4830-94e9-074095b9feb3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2a51214a-5b86-4757-8f87-e9e31541df0b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("409c0fb9-d3f9-46a0-9206-34df9d7a9931"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6023ee65-1c4e-4e90-9134-7f814dda817b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6ec07f93-ae95-47cf-b8eb-aa4405ef1e24"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("72f361b3-6cd2-4bc4-bca5-9bfb7f708036"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7969e298-a7c1-4f19-9407-9e24ae25398b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("84095ea6-e6bc-4076-a1fc-b98db60cf978"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8ad1d88d-ef9f-4823-bc1c-c15933884bfc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("94e4b934-5528-4687-973c-2995347bccd8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9b876ff2-fb65-437e-bad3-fa941db7ae33"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b8ff37f4-fbb5-4d35-8708-3daf652b5c0e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bb7c1084-9a9e-41c3-98bc-e7e74453ecbf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d61e7877-35ab-47a2-9758-c620b20d26c3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d921ab36-c52c-4e94-bc8c-36b3333b96ea"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("def12f1c-4507-49de-bb40-b36e9bc33db3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e34d8ca2-7b77-4c41-b9e5-5d1b540c1436"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e6901a58-dd6c-41a1-baea-622cf89dd6f9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e81f2fc6-0990-4596-a0e3-fcfb98e5da71"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("edfcf5e7-d145-465f-8da9-3392b6d3b84e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ffeee9d6-35b8-4b61-8bc0-3055e9988709"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3e188beb-fe09-4b3c-abc0-49a4c2f235f3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6e75e872-c342-4b64-8048-f19c2014af58"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("78818b40-fd7c-45da-8d3c-446b06c70511"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c17b5a53-7f6f-4441-88ae-ba02b86d3106"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c4c05874-8e3d-4d3c-a2d5-283ad6fa3ad5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("da7edba7-80d9-448f-88fb-e4d6006865ce"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ba25358-228b-40c5-b27c-6e1ebca68f90"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ca12e32c-1085-448b-b8a0-4958a3356060"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cdcee1a2-ca9d-4b92-a4e3-22f8b244e68a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e7388a2d-3164-4923-b202-c18f68f4471a"));

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "Group",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0acbfbe8-3349-482b-9bfc-4824250a0816", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5106) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0e7a606d-8454-413e-9868-e5bc3eb4f4f3", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5083) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "64543438-a9da-444a-b7db-26ea225cc2a4", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5091) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "8f0319ae-5e56-4005-b90d-3e002266a1e7", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5120) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "312096dc-9485-48db-82a9-8f18c56f720c", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5101) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f0af4f7c-e9a5-4364-ad92-b71a37293160", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5096) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "a90c297a-f5eb-4a40-95ce-f7e870d066df", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), "AQAAAAEAACcQAAAAEJ0+oX6N25DaHXAF43NrLXkDoCQOrjJww9ZiMa6vObnLktq/9yCa12SfPC+8f+IVAg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7512033a-3ffb-4156-9c22-014b0b035348"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("80cb2a33-a443-492b-b005-eb51b674e504"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9c28fb26-9536-467e-8667-69716031350a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }
    }
}
