﻿using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Ticket
{
    public class TicketEntity:ReferentialData
    {
        public Guid TicketId { get; set; }
        public string Name { get; set; } = string.Empty;
        public double Amount { get; set; }
        public int Quantity { get; set; }
        public double TotalAmount { get; set; }
        public WalletType Type { get; set; }

        [ForeignKey("CompanyId")]
        public Guid CompanyId { get; set; }
        public bool IsAutomatic { get; set; }
        public PeriodType PeriodType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public IList<GroupTicketEntity>? GroupTickets { get; set; }
       // public IList<GroupEntity>? Groups { get; set; }

    }
}
