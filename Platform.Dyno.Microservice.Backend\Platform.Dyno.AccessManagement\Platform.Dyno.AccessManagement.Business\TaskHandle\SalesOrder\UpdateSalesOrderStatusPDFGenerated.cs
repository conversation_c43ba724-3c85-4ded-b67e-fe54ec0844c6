﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Cryptography.Xml;
using MimeKit.Cryptography;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("SO_Status_PDF_Generated")]
    public class UpdateSalesOrderStatusPDFGenerated : IExternalTaskHandler
    {
        private readonly ILogger<UpdateSalesOrderStatusPDFGenerated> _logger;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly IHelper<NotificationDTO> _notificationHelper;
        private readonly Configuration _configuration;
        private readonly IInvoiceService _invoiceService;
        public UpdateSalesOrderStatusPDFGenerated(
        ILogger<UpdateSalesOrderStatusPDFGenerated> logger,
        ISalesOrderService salesOrderService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        IHelper<NotificationDTO> notificationHelper,
        Configuration configuration,
        IInvoiceService invoiceService)

        {
            _logger = logger;
            _salesOrderService = salesOrderService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _notificationHelper = notificationHelper;
            _configuration = configuration;
            _invoiceService = invoiceService;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
                //Guid? documentId = Guid.Parse(externalTask.Variables["DocumentId"].Value.ToString());
                Guid documentId;
                if (!externalTask.Variables.TryGetValue("DocumentId", out var documentIdVar)
                    || documentIdVar is not StringVariable stringVar
                    || !Guid.TryParse(stringVar.Value, out documentId))
                {
                    // Gestion d'erreur
                    _logger.LogError("DocumentId invalide ou manquant");
                    throw new ArgumentException("DocumentId invalide");
                }

                if (salesOrderDTO == null)
                {
                    outputVariables.Add("Result_SO_Status_PDF_Generated", new BooleanVariable(false));
                    outputVariables.Add("Error_SO_Status_PDF_Generated", new StringVariable("json conversion failed"));

                }
                if (documentId == null)
                {
                    outputVariables.Add("Result_SO_Status_PDF_Generated", new BooleanVariable(false));
                    outputVariables.Add("Error_SO_Status_PDF_Generated", new StringVariable("documentId conversion failed"));

                }
                else
                {
                    var response = _salesOrderService.UpdateStatus(salesOrderDTO.Id, SalesOrderStatus.PDFGenerated, true, null, null, true, documentId);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        salesOrderDTO.Status = SalesOrderStatus.PDFGenerated;
                        outputVariables.Add("Result_SO_Status_PDF_Generated", new BooleanVariable(true));
                        DocumentsDTO? document = _invoiceService.Get((Guid)documentId).ObjectValue;
                        salesOrderDTO.Document = document;
                        NotificationDTO notification = new NotificationDTO
                        {
                            Id = salesOrderDTO.Id,
                            Title = "Status Sales Order",
                            Message = JsonSerializer.Serialize(salesOrderDTO),
                            SendToGroup = new List<string> { salesOrderDTO.CompanyId.ToString() }
                        };

                        string url = $"{_configuration.NotificationAddress}/Api/Notification/SalesOrderStatusNotif";
                        ResponseAPI<NotificationDTO>? responseAPI = _notificationHelper.Post(url, notification);
                    }
                    else
                    {
                        outputVariables.Add("Result_SO_Status_PDF_Generated", new BooleanVariable(false));
                        outputVariables.Add("Error_SO_Status_PDF_Generated", new JsonVariable(JsonSerializer.Serialize(response)));
                    }

                    
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SO_Status_PDF_Generated", new BooleanVariable(false));
                outputVariables.Add("Error_SO_Status_PDF_Generated", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
