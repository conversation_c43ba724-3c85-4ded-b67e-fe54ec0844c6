﻿using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.SharedClass.PhoneNumber;
using System.ComponentModel.DataAnnotations;

namespace Platform.Dyno.AccessManagement.DTO.Company
{
    public class CompanyDTO : ReferentialData
    {
        public Guid Id { get; set; }
        public string RNECode { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Picture { get; set; }

        [PhoneNumberValidator("TN")]
        public string PhoneNumber { get; set; } = string.Empty;
        public string? CountryCode { get; set; }
        public double? ClientFeePercentage { get; set; }
        public EnterpiseType EntrepriseType { get; set; }
        public ServiceType ServiceType { get; set; }
        public CategoryType? CategoryType { get; set; }
        public Guid WalletId { get; set; }
        public IList<AddressDTO>? Addresses { get; set; }
        public IList<UserDTO>? Users { get; set; }
        public IList<EmployeeDTO>? Employees { get; set; }
        public IList<TicketDTO>? Tickets { get; set; }
        public IList<PaymentDetailsDTO>? PaymentDetails { get; set; }

    }
}


