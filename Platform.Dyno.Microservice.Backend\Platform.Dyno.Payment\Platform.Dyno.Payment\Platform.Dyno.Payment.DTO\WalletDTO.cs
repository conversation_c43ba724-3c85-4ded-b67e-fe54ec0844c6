﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Payment.DTO
{
    public class WalletDTO
    {
        public Guid Id { get; set; }
        public string PrivateKey { get; set; } = string.Empty;
        public string PublicKey { get; set; } = string.Empty;
        public WalletType WalletType { get; set; }
        public UserType AssignedToType { get; set; }
        public string AssignedToName { get; set; } = string.Empty;
        public Guid AssignedToId { get; set; }
        public double Balance { get; set; }
        public WalletStatus Status { get; set; }
    }
}
