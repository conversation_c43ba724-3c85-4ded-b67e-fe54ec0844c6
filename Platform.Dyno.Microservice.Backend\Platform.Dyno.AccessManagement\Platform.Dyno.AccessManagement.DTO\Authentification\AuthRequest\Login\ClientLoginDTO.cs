﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login
{
    public class ClientLoginDTO
    {
        public string CountryCode { get; set; } = string.Empty;
        public string  PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;

        #region User Security
        [Required(ErrorMessage = "MacAddress is required")]
        public string MacAddress { get; set; } = string.Empty;
        #endregion 
    }
}
