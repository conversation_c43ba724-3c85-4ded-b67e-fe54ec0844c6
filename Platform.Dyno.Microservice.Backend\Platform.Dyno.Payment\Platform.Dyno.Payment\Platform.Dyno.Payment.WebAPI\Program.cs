using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Application.Mapping;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Neo4j.Driver;
using Newtonsoft.Json;
using Platform.Dyno.Payment.WebAPI.Worker;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Authorization;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.KafkaMessage;
using Server.Kafka;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();



#region Swagger
builder.Services.AddSwaggerDoc();
#endregion

#region Configuration
builder.Services.AddSingleton<Platform.Dyno.Shared.Configuration>();
builder.Services.AddSingleton<ConfigurationDefaultId>();
#endregion
Console.WriteLine("AWS Config to laod: " );
#region Config File AWS
var config = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
    .Build();

var s3Client = new AmazonS3Client(new BasicAWSCredentials(config["AWSAccessKey"], config["AWSPrivateKey"]), RegionEndpoint.USEast1);
var getObjectRequest = new GetObjectRequest
{
    BucketName = config["AWSBucketName"] ?? "",
    Key = config["AWSKey"]
};
Console.WriteLine("AWS Config Loaded Successfully: " + s3Client);


using (var response = s3Client.GetObjectAsync(getObjectRequest).Result)
{
    using (var streamReader = new StreamReader(response.ResponseStream))
    {
        var content = await streamReader.ReadToEndAsync();
        var jsonData = JsonConvert.DeserializeObject<Platform.Dyno.Shared.Configuration.Config>(content);

        builder.Services.AddSingleton(sp =>
        {
            var driver = GraphDatabase.Driver(jsonData.NEO4J_URI,
             AuthTokens.Basic(jsonData.NEO4J_USERNAME, jsonData.NEO4J_PASSWORD));
            return driver;
        });

        #region WatchDog
        builder.Services.AddSingleton<IProducer<HeartBeatMessage>>(producer => new Producer<HeartBeatMessage>(Topic.TOPIC_WATCHDOG_RECEIVE_MESSAGE, jsonData.KafkaServer));
        builder.Services.AddSingleton<IConsumer<HeartBeatMessage>>(consumer => new Consumer<HeartBeatMessage>(Topic.TOPIC_WATCHDOG_SEND_MESSAGE, jsonData.KafkaServer, jsonData.KafkaGroupId));
        builder.Services.AddHostedService<PaymentWatchDogWorker>();
        #endregion

        #region Infrastatucture Services
        Infrastructure.DependencyInjection.AddServices(builder.Services, jsonData);
        #endregion

        #region Authentication
        builder.Services.ConfigureJWT(jsonData.Key, jsonData.Issuer);
        #endregion
    }
}


#endregion





#region Application Services
Application.DependencyInjection.AddServices(builder.Services);
#endregion

/* package AutoMapper --version 13.0.0 pour remplacer AutoMapper.Extensions.Microsoft.DependencyInjection
 * #region AutoMapper
builder.Services.AddAutoMapper(typeof(MapperEntity_BM));
builder.Services.AddAutoMapper(typeof(MapperDTO_BM));
builder.Services.AddAutoMapper(typeof(MapperINode_Entity));
builder.Services.AddAutoMapper(typeof(MapperIRelationship_Entity));
builder.Services.AddAutoMapper(typeof(MapperDTO_DTO));
#endregion*/
var mapperConfig = new MapperConfiguration(cfg =>
{
    cfg.AddProfile<MapperEntity_BM>();
    cfg.AddProfile<MapperDTO_BM>();
    cfg.AddProfile<MapperINode_Entity>();
    cfg.AddProfile<MapperIRelationship_Entity>();
    cfg.AddProfile<MapperDTO_DTO>();
});

// Cr�e une instance de `IMapper` et l'ajoute aux services
IMapper mapper = mapperConfig.CreateMapper();
builder.Services.AddSingleton(mapper);




#region Authorization
builder.Services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
builder.Services.AddSingleton<IAuthorizationHandler, PermissionAuthorizationHandler>();
builder.Services.Configure<SecurityStampValidatorOptions>(options =>
{
    options.ValidationInterval = TimeSpan.Zero;
});
#endregion

#region Cache Service
builder.Services.AddScoped<IRedisCacheService, RedisCacheService>();
#endregion

#region Business Service
builder.Services.AddScoped<Application.IServices.IDistributedTransactionService, Application.Services.DistributedTransactionService>();
#endregion

#region Helper
Application.DependencyInjection.ScoppedHelper(builder.Services);
#endregion

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI();

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();
