﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.DTO.Sns
{
    public class SnsMessage
    {
        public string Message { get; set; } = string.Empty;
        public string TopicArn { get; set; } = string.Empty;
        public string TopicName { get; set; } = string.Empty;
        public string TargetArn { get; set; } = string.Empty;
        public string MessageGroupId { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Protocol { get; set; } = string.Empty;
        public string EndPoint { get; set; } = string.Empty;
        public string EndPointArn { get; set; } = string.Empty;
        public string SubscriptionArn { get; set; } = string.Empty;
        public string PlatformApplicationArn { get; set; } = string.Empty;
    }
}
