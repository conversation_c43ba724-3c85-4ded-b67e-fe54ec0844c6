﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Platform.Dyno.AccessManagement.EF;

#nullable disable

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    [DbContext(typeof(ContextDB))]
    [Migration("20240326100111_company_TaxCode")]
    partial class company_TaxCode
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("MessageNotificationsId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SubscribersId")
                        .HasColumnType("uuid");

                    b.HasKey("MessageNotificationsId", "SubscribersId");

                    b.HasIndex("SubscribersId");

                    b.ToTable("MessageNotificationEntitySubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FullAddress")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSaved")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("MacAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("MacAddress");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("FeeAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PaymentDetailsId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("ValidationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("WalletBallance")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PaymentDetailsId");

                    b.ToTable("CashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CashbackId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CashbackId");

                    b.ToTable("FailedCashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("CategoryType")
                        .HasColumnType("integer");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EntrepriseType")
                        .HasColumnType("integer");

                    b.Property<double?>("FeePercent")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("RNECode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ServiceType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TaxCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Company");

                    b.HasData(
                        new
                        {
                            Id = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Email = "<EMAIL>",
                            EntrepriseType = 0,
                            FeePercent = 11.0,
                            Name = "Dyno & Motiva Systems",
                            PhoneNumber = "+21699001000",
                            RNECode = "1803838P",
                            ServiceType = 0,
                            Status = 0,
                            TaxCode = "000 M A 1803838P",
                            WalletId = new Guid("00000000-0000-0000-0000-000000000000")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("FeePercentage")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("PaymentDelay")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("RIB")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.Property<Guid>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("EmployeeId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("GroupId");

                    b.HasIndex("UserId");

                    b.ToTable("Employee");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Property<Guid>("GroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("GroupId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Group");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.Property<Guid?>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uuid");

                    b.HasKey("GroupId", "TicketId");

                    b.HasIndex("TicketId");

                    b.ToTable("GroupTicket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Logger.LogErrorEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("API")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Error")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<int>("Microservice")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("LogError");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.HasKey("CompanyId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("CompanyNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MessageGroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Protocol")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Rule")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TopicArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MessageNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatorType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsSeen")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SignalRNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EndPoint")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EndPointArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TargetArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserEntityId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserEntityId");

                    b.ToTable("SubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsLeaf")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool?>("PartialSelected")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("PermissionEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PermissionEntityId");

                    b.HasIndex("RoleId");

                    b.ToTable("Permission");

                    b.HasData(
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea4"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b32f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f5252a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e0f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6c42303e-9466-40a3-9f04-59db7efd39c3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ee424456-2dd5-448b-a24a-a4ffa23db15f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("91ee99a2-9756-4fdd-acf8-dc6aceca52a7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d5cae7c2-e640-417b-98d5-d559aaa87539"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c697a4f3-c352-4279-8845-f7f0ca040b14"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c186861f-08c1-4463-8d63-5484c5652918"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f20b2983-6d9b-4fe0-8ee8-5a0255d5a2e0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a9ccc7d0-0765-4a94-9bd4-385631825d50"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b320"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f52520"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e00"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("25e5a90f-6287-4033-829a-ecc417e54343"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ceceb370-cb0d-49ab-a458-301078a9d126"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("77b55a22-850d-4177-9a00-974f6d7107e2"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0d2c1dc2-5405-4b5d-bb08-f04a90922e10"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("602aa4d2-2f12-40c2-bb07-1a22621d725d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("709b16ea-18c1-4a87-92bb-e9c9c88dbcee"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7dec5f33-050e-42fc-9643-08461b2f878d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("3c0e20f8-fd44-43d9-8ed8-b9ec25a7add3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("62ef46ef-2265-4ab2-9ff0-2b1f5475c398"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("91529635-074d-4e93-9681-4df7aa834ea7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("46c99795-0cd0-414d-be66-af6dcc6af7ed"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f6d7877c-b570-47a1-9b25-cff6d8a03c33"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8818f091-15b7-41e6-9794-eca7202007df"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d6e77324-60ca-4975-955b-4821acb5e205"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("49847712-c67b-4c2d-892c-c2ac5e9e9549"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("057803b1-805f-49e8-83cf-81c26071bdbf"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("896e4f08-4840-420e-bd54-46836d620107"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8a869811-5c92-4d7e-bf05-6ae811172508"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("896e4f08-4840-420e-bd54-46836d620107"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7d2122f7-0300-4f92-b512-241e9244ee6b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("896e4f08-4840-420e-bd54-46836d620107"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("10f010d8-aebd-42d0-8960-3b70192b753d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("896e4f08-4840-420e-bd54-46836d620107"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1680bfa1-17a0-4d50-a5fd-d02e94dc6dc7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("896e4f08-4840-420e-bd54-46836d620107"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("04aaa642-2320-46e0-9622-1599610008d9"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("057454a0-9c0f-4c8a-a233-64e15308e9e8"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("04aaa642-2320-46e0-9622-1599610008d9"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("86a9c0b4-b4be-4a62-a4ce-941791cdb985"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("04aaa642-2320-46e0-9622-1599610008d9"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ed2d78c3-2dc5-4148-b00b-12711541df9f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("04aaa642-2320-46e0-9622-1599610008d9"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("01976278-669c-4fb3-a054-a2400b457e9e"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("04aaa642-2320-46e0-9622-1599610008d9"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4e3eccd9-324e-42d0-9c10-f81eebc96530"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b29c7f50-30b6-4de2-9b9b-e4598781afa9"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a8750d27-7fe7-4243-8aed-9033907aa949"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("17d48710-4642-4143-8bf5-db4b3149624c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4ff1df44-b725-40d6-9eed-6ec0c7979ef9"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b0f95d68-4c89-4dbe-8abb-c5c331d17357"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4deb3535-967a-4ad1-aa70-70e6600d24cc"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("707e694f-59f0-4ca2-9976-bbcdcbc701cd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            ConcurrencyStamp = "1deb2074-1749-469a-bae3-c27c3a6b18f4",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5644),
                            Name = "Default",
                            NormalizedName = "DEFAULT",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            ConcurrencyStamp = "54d55432-72fd-45e8-b2b2-e2d2366eacff",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5652),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "SuperAdmin",
                            NormalizedName = "SUPERADMIN",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            ConcurrencyStamp = "f01d645f-21f7-4b14-b3e6-db12f15cee61",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5657),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Admin",
                            NormalizedName = "ADMIN",
                            Status = 0,
                            UserType = 1
                        },
                        new
                        {
                            Id = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            ConcurrencyStamp = "e18f9697-c753-49c6-8593-48e28bf94647",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5661),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Shop Admin",
                            NormalizedName = "SHOP ADMIN",
                            Status = 0,
                            UserType = 0
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                            ConcurrencyStamp = "c11d7307-75f2-49ea-9f02-39711990e723",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5678),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Client",
                            NormalizedName = "CLIENT",
                            Status = 0,
                            UserType = 4
                        },
                        new
                        {
                            Id = new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                            ConcurrencyStamp = "fc840988-7cb0-4639-9596-55688a85efa3",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5683),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Cashier",
                            NormalizedName = "CASHIER",
                            Status = 0,
                            UserType = 2
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("RoleId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("RoleUser");

                    b.HasData(
                        new
                        {
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            UserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SalesOrderId");

                    b.ToTable("FailedSalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.HasIndex("SalesOrderId")
                        .IsUnique();

                    b.ToTable("SalesInvoice");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.ToTable("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Property<Guid>("TicketId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<Guid?>("CompanyEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutomatic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("PeriodType")
                        .HasColumnType("integer");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("TicketId");

                    b.HasIndex("CompanyEntityId");

                    b.ToTable("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<int>("AuthentificationSource")
                        .HasColumnType("integer");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<int?>("Gender")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("text");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("PinCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            AccessFailedCount = 0,
                            AuthentificationSource = 0,
                            CompanyId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            ConcurrencyStamp = "fc4db08b-aa02-47a9-af4a-d79bc5ff2c57",
                            CountryCode = "",
                            CreationTime = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6007),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            DateOfBirth = new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6006),
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            FullName = "Dyno Root",
                            Gender = 2,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "CFBC54C0-97CE-40FF-9281-79D09713CB71",
                            PasswordHash = "AQAAAAEAACcQAAAAEND7zUinVYKTFkL1KNVZ54BZLmGMB/o1AHyoYCOSY66siyngCJjiDM+J8y6C64D7Jg==",
                            PhoneNumberConfirmed = true,
                            PinCode = "ZFxn5dSGlaMFoTfAET4RbyhYkvs39oCKFHsXjahuXSg=",
                            Status = 0,
                            TwoFactorEnabled = false,
                            UserName = "cfbc54c0-97ce-40ff-9281-79d09713cb71",
                            UserType = 3
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Code")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserOTP");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiredDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("RefreshToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserToken");
                });

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", null)
                        .WithMany()
                        .HasForeignKey("MessageNotificationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", null)
                        .WithMany()
                        .HasForeignKey("SubscribersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Addresses")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("Addresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("MacAddresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", "PaymentDetails")
                        .WithMany("CashBacks")
                        .HasForeignKey("PaymentDetailsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", "Cashback")
                        .WithMany()
                        .HasForeignKey("CashbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cashback");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("PaymentDetails")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Employees")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("Employees")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("GroupTickets")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", "Ticket")
                        .WithMany("GroupTickets")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", "Notification")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("SubscriberDevices")
                        .HasForeignKey("UserEntityId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", null)
                        .WithMany("Permissions")
                        .HasForeignKey("PermissionEntityId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("Permissions")
                        .HasForeignKey("RoleId");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("RoleUsers")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("RoleUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesInvoices")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "DocumentId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Document");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "DocumentId");

                    b.Navigation("Company");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", null)
                        .WithMany("Tickets")
                        .HasForeignKey("CompanyEntityId");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("UserOTPs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CompanyNotifs");

                    b.Navigation("Employees");

                    b.Navigation("PaymentDetails");

                    b.Navigation("SalesInvoices");

                    b.Navigation("SalesOrders");

                    b.Navigation("Tickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Navigation("CashBacks");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Navigation("CompanyNotifs");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Navigation("Permissions");

                    b.Navigation("RoleUsers");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("MacAddresses");

                    b.Navigation("RoleUsers");

                    b.Navigation("SubscriberDevices");

                    b.Navigation("UserOTPs");
                });
#pragma warning restore 612, 618
        }
    }
}
