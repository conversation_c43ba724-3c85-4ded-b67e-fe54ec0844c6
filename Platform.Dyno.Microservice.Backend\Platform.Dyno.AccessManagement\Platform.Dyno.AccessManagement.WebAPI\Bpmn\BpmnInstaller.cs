﻿using Camunda.Worker.Client;
using Camunda.Worker;
using Platform.Dyno.AccessManagement.Business.Bpmn;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice;
using Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder;


using Camunda.Api.Client; 
using System.Net.Http;
using Refit;


namespace Platform.Dyno.AccessManagement.WebAPI.Bpmn
{
    public static class BpmnInstaller
    {
        public static IServiceCollection AddCamunda(this IServiceCollection services, string camundaRestApiUri)
        {
            services.AddSingleton(_ => new BpmnService(camundaRestApiUri));
            services.AddHostedService<BpmnProcessDeployService>();

            services.AddExternalTaskClient(client =>
            {
                client.BaseAddress = new Uri(new Platform.Dyno.Shared.Configuration().CamundaAddress);
            });

            services.AddCamundaWorker("AccessWorker", 1)
            .AddHandler<AddInstanceToSalesOrder>()
            .AddHandler<CreateSalesOrderPDF>()
            .AddHandler<UpdateSalesOrderStatusPDFGenerated>()
            .AddHandler<UpdateSalesOrderStatusPDFFailed>()
            .AddHandler<LogErrorSalesOrderPDFFailed>()
            .AddHandler<SendNotifPDFFailed>()
            .AddHandler<SendNotifSA>()
            .AddHandler<LogErrorSalesOrderEmailFailed>()
            .AddHandler<SendEmailSalesOrder>()
            .AddHandler<UpdateSalesOrderStatusEmailFailed>()
            .AddHandler<UpdateSalesOrderStatusEmailSent>();


            services.AddHealthChecks();
            return services;
        }
    }
}
