﻿using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.User.EmailConfirmation;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IUserOTPService : IGenericService<UserOTPDTO>
    {
        ResponseAPI<UserOTPDTO> Create(string email, Guid? creatorUserId = null, bool updateCache = true);
        ResponseAPI<UserOTPDTO> VerifierDevice(AdminOtpDTO userOtp, bool updateCache = true);
        ResponseAPI<UserOTPDTO> VerifierDevice(ClientOtpDTO userOtp, bool updateCache = true);
        ResponseAPI<UserOTPDTO> SendOTP(UserOTPDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true);
        ResponseAPI<UserOTPDTO> VerifierCode(UserOTPDTO userOTPDTO);
        Task<ResponseAPI<EmailConfirmationDTO>> EmailConfirmation(EmailConfirmationDTO emailConfirmationDTO);
    }
}
