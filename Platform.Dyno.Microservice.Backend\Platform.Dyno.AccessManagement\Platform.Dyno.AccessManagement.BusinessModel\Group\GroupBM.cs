﻿using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.Group;
public class GroupBM : ReferentialData
{
    public Guid GroupId { get; set; }
    public string Name { get; set; }
    public Guid CompanyId { get; set; }
    public CompanyBM? Company { get; set; }
    public IList<EmployeeEntity>? Employees { get; set; }
    public IList<GroupTicketEntity>? GroupTickets { get; set; }
}

