﻿using AutoMapper;
using Enyim.Caching.Memcached.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Win32;
using Newtonsoft.Json.Linq;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.Helpers;
using System.Net;
using System.Security.Claims;
using System.Text.RegularExpressions;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthClientController : ControllerBase
    {
        private readonly IAuthClientService _authService;
        private readonly ILogger<AuthClientController> _logger;
        private readonly IUserOTPService _userOTPService;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMacAddressService _macAddressService;
        private readonly IHelper<WalletDTO> _helper;
        private readonly Platform.Dyno.Shared.Configuration _configuration;
        private readonly IWalletService _walletService;
        public AuthClientController(IAuthClientService authService,
            IMapper mapper,
            IUserOTPService userOTPService,
            ILogger<AuthClientController> logger,
            IHttpContextAccessor httpContextAccessor,
            IHelper<WalletDTO> helper, Platform.Dyno.Shared.Configuration configuration, IMacAddressService macAddressService, IWalletService walletService)
        {
            _authService = authService;
            _userOTPService = userOTPService;
            _mapper = mapper;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _macAddressService = macAddressService;
            _helper = helper;
            _configuration = configuration;
            _walletService = walletService;
        }

        [Route("Register")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Register([FromBody] ClientRegisterDTO user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<AuthResponseDTO> response = await _authService.Register(user);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    string Url = $"{_configuration.PaymentAddress}/Api/WalletBlockchain/CreateDefaultWallets";
                    Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "pinCode", user.PinCode.ToString() }
                    };
                    if (response.ObjectValue?.UserProfile?.Id!=null)
                    {
                        List<WalletDTO> walletDTOList = new List<WalletDTO>()
                        {
                            new WalletDTO()
                            {
                                AssignedToId = (Guid) response.ObjectValue.UserProfile.Id,
                                AssignedToType = UserType.Client
                            }
                        };
                        ResponseAPI<List<WalletDTO>>? paymentApiResponse = _helper.AddRange(Url, walletDTOList, queryParams);
                        if (paymentApiResponse == null || paymentApiResponse.StatusCode == HttpStatusCode.InternalServerError)
                        {
                            response.ExceptionMessage += $"An error occured while creating your wallets, please contact IT support. Exception:{paymentApiResponse?.ExceptionMessage}";
                            //Delete User Created !!                           
                            return BadRequest(response);

                        }
                    }
                    else
                    {
                        response.ExceptionMessage = response.ExceptionMessage + "an error occured while creating your wallets, please contact IT support: user Id is Null";
                        return StatusCode(500, response);
                    }
               
                }
                else if (response.StatusCode != System.Net.HttpStatusCode.Created)
                {
                    return BadRequest(response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Register)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("login")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult Login([FromBody] ClientLoginDTO loginDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? acceptLanguage = _httpContextAccessor.HttpContext?.Request.Headers["Language"];
                LanguageType language = LanguageType.En;
                _ = Enum.TryParse(acceptLanguage, out language);

                ResponseAPI<AuthResponseDTO> response = _authService.Login(loginDto, language);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Login)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        [Route("BiometricLogin/{password}")]
        [HttpGet]
        [Authorize("Role." + DefaultRoles.Client)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult BiometricLogin(string password)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = _authService.BiometricLogin(new Guid(userId), password);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Login)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }


        [Route("Logout")]
        [HttpGet]
        [Authorize]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Logout()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? authorizationHeader = HttpContext.Request.Headers["Authorization"];

                if (!string.IsNullOrEmpty(authorizationHeader) && authorizationHeader.StartsWith("Bearer "))
                {
                    string token = authorizationHeader.Substring("Bearer ".Length);
                    string? userId = HttpContext.User.FindFirstValue("Id");
                    if (userId == null)
                    {
                        return Unauthorized(new ResponseAPI<AuthResponseDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<AuthResponseDTO> response = _authService.LogOut(new Guid(userId), token);
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "No valid token provided."
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Logout)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }



        [Route("RefreshToken")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult RefreshToken(UserTokenDTO token)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                token.UserId = new Guid(userId);

                ResponseAPI<AuthResponseDTO> response = _authService.GetRefreshToken(token);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(RefreshToken)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }



        [Route("ForgetPassword/{countryCode}/{phoneNumber}")]
        [HttpGet]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public async Task<IActionResult> ForgetPassword(string countryCode, string phoneNumber)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = await _authService.ForgetPassword(phoneNumber, countryCode);
                return Ok(response);
            }


            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ForgetPassword)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        [Route("UpdatePassword")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult UpdatePassword(UpdatePasswordDTO passwordDTO)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                passwordDTO.UserId = new Guid(userId);

                ResponseAPI<AuthResponseDTO> response = _authService.UpdatePassword(passwordDTO);
                return Ok(response);
            }


            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ForgetPassword)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }


        }


        [Route("VerifyOTPCode")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult VerifyOTPCode([FromBody] ClientOtpDTO clientOtp)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = _authService.VerifyCode(clientOtp);
                return Ok(response);
            }


            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(VerifyOTPCode)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }


        }


        [Route("ResetPassword")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult ResetPassword([FromBody] ResetPasswordClientDTO resetPassword)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<AuthResponseDTO> response = _authService.ResetPassword(resetPassword);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ResetPasswordClientDTO)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }



        [Route("ConfirmePhoneNumber")]
        [HttpPost]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult ConfirmePhoneNumber([FromBody] ClientOtpDTO clientOtp)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response = _authService.Confirme(clientOtp.CountryCode, clientOtp.PhoneNumber, clientOtp.Code);
                if(response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    //Save Device
                    ResponseAPI<MacAddressDTO> verifierDevice = _macAddressService.SaveDevice(response.ObjectValue?.UserProfile?.Id, clientOtp.MacAddress, clientOtp.IsSaved);
                }
                return Ok(response);
            }


            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ForgetPassword)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }


        }


        
        [Route("CheckNumber/{countryCode}/{phoneNumber}/{macAddress}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult CheckNumber(string countryCode, string phoneNumber, string macAddress)
        {
            try
            {
                // Validation améliorée avec le helper
                if (!MobileValidationHelper.IsValidPhoneNumber(countryCode, phoneNumber))
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Phone Number is invalid"
                    });
                }

                if (!MobileValidationHelper.IsValidMacAddress(macAddress))
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "MAC Address is invalid"
                    });
                }
                ResponseAPI<bool> response = _authService.CheckPhone(countryCode, phoneNumber, macAddress);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CheckNumber)}");
                ResponseAPI<List<bool>> response = new ResponseAPI<List<bool>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }



    }
}
