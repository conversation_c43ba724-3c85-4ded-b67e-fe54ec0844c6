﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;


namespace Platform.Dyno.AccessManagement.Business.Service.Address
{
    public class MacAddressService : IMacAddressService
    {
        private readonly IUnitOfWork<MacAddressEntity> _macAddressRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _macAddressCacheKey = RedisCacheKey.MacAddressCacheKey;

        public MacAddressService(IUnitOfWork<MacAddressEntity> macAddressRepository,
            IMapper mapper,
            IRedisCacheService cache) 
        { 
            _macAddressRepository = macAddressRepository;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<MacAddressDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            List<MacAddressDTO>? macAddressesDTO = _cache.GetData<List<MacAddressDTO>>(_macAddressCacheKey);
            if (macAddressesDTO == null || macAddressesDTO.Count == 0)
            {
                List<MacAddressEntity> macAddressEntities = (List<MacAddressEntity>)_macAddressRepository.Repository.GetAll(includes: new List<string> { "User" }, orderBy: user => user.OrderByDescending(user => user.LastModificationTime));
                List<MacAddressBM> macAddressess = _mapper.Map<List<MacAddressBM>>(macAddressEntities);
                macAddressesDTO = _mapper.Map<List<MacAddressDTO>>(macAddressess);

            }

            ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = macAddressesDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<MacAddressDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<MacAddressDTO>> macAddressesDTO = GetAll();
            PagedList<MacAddressDTO>? pagedList = null;
            if (macAddressesDTO.ObjectValue != null)
            {
                pagedList = PagedList<MacAddressDTO>.ToGenericPagedList(macAddressesDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<MacAddressDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<MacAddressDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<MacAddressDTO>> MacAddressesDTO = GetAll();
            PagedList<MacAddressDTO>? pagedList = null;
            if (MacAddressesDTO.ObjectValue != null)
            {
                pagedList = PagedList<MacAddressDTO>.ToGenericPagedList(MacAddressesDTO.ObjectValue.Where(macAddress => macAddress.Status == Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<MacAddressDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<MacAddressDTO> Get(Guid id)
        {
            MacAddressDTO? macAddressDTO = GetAll().ObjectValue?.Where(macAddress => macAddress.Id == id).FirstOrDefault();
            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = macAddressDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = macAddressDTO == null ? $"Mac address with id {id} Not Found !" : null,
                ObjectValue = macAddressDTO
            };
        }

        public ResponseAPI<List<MacAddressDTO>> Get(Func<MacAddressDTO, bool> expression)
        {
            List<MacAddressDTO>? macAddressesDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<MacAddressDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = macAddressesDTO
            };
        }

       
        #endregion

        #region Create
        public ResponseAPI<MacAddressDTO> Create(MacAddressDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            MacAddressBM macAddress = _mapper.Map<MacAddressBM>(dtoObject);

            #region changed data
            macAddress.Id = Guid.NewGuid();       
            RefDataService<MacAddressBM>.CreateRefData(macAddress, macAddress.Id);
            #endregion

            MacAddressEntity macAddressEntity = _mapper.Map<MacAddressEntity>(macAddress);
            _macAddressRepository.Repository.Insert(macAddressEntity);
            _macAddressRepository.Save();

            dtoObject.Id = macAddressEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_macAddressCacheKey);
            }

            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        #endregion

        #region Update
        public ResponseAPI<MacAddressDTO> Update(MacAddressDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                MacAddressBM macAddress = _mapper.Map<MacAddressBM>(dtoObject);

                #region RefData
                RefDataService<MacAddressBM>.UpdateRefData(macAddress, macAddress.Id);
                #endregion

                MacAddressEntity macAddressEntity = _mapper.Map<MacAddressEntity>(macAddress);
                _macAddressRepository.Repository.Update(macAddressEntity);
                _macAddressRepository.Save();
                dtoObject.Id = macAddressEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_macAddressCacheKey);
                }

                return new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Mac address with id {dtoObject.Id} Not Found !"
            };
        }

        public ResponseAPI<MacAddressDTO> SaveDevice(Guid? userId ,string macAddress, bool isSaved)
        {
            ResponseAPI<List<MacAddressDTO>> response = Get(mac => 
            mac.MacAddress == macAddress &&
            mac.UserId == userId &&
            mac.Status == Shared.Enum.Status.Active);

            if(response.ObjectValue != null && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                MacAddressDTO? macAddressDto = response.ObjectValue.FirstOrDefault();
                if (macAddressDto != null)
                {
                    macAddressDto.IsSaved = isSaved;
                    macAddressDto.IsConfirmed = true;
                    macAddressDto.User = null;
                    ResponseAPI<MacAddressDTO> saveMacAddress = Update(macAddressDto);

                    return saveMacAddress;
                }

            }

            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "MacAddress unFound ."
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<MacAddressDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            MacAddressDTO? macAddressDTO = GetAll().ObjectValue?.Where(dto => dto.Id == id).FirstOrDefault();
            if (macAddressDTO != null)
            {
                MacAddressBM macAddress = _mapper.Map<MacAddressBM>(macAddressDTO);

                #region Refdata
                RefDataService<MacAddressBM>.DeleteRefData(macAddress, macAddress.Id);
                #endregion

                MacAddressEntity macAddressEntity = _mapper.Map<MacAddressEntity>(macAddress);
                _macAddressRepository.Repository.Update(macAddressEntity);
                _macAddressRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_macAddressCacheKey);
                }

                return new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = macAddressDTO
                };

                
            }



            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Mac address with id {id} Not Found !"
            };
        }
     
        public ResponseAPI<MacAddressDTO> Remove(string macAddress)
        {
            ResponseAPI<List<MacAddressDTO>> response = Get(mac => mac.MacAddress == macAddress &&
            mac.Status == Status.Active);

            if (response.ObjectValue != null && response.ObjectValue.Count > 0 && response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                _macAddressRepository.Repository.Delete(response.ObjectValue.FirstOrDefault().Id);
                _macAddressRepository.Save();

                return new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = response.ObjectValue.FirstOrDefault()
                };
            }

            return new ResponseAPI<MacAddressDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "MacAddress unFound ."
            };
        }

        #endregion

    }
}
