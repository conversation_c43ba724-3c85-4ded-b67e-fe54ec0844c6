﻿using Amazon;
using Amazon.Runtime.Internal.Endpoints.StandardLibrary;
using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Org.BouncyCastle.Tls;
using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Text.Json;

namespace Platform.Dyno.Notification.Business.Service
{
    public class SnsService : ISnsService
    {
        private readonly IAmazonSimpleNotificationService _snsClient;

        public SnsService()
        {
            _snsClient = new AmazonSimpleNotificationServiceClient(RegionEndpoint.USEast1);
        }

        public async Task<ResponseAPI<string>> SendNotification(string message, string topicArn ,string targetArn,
            string messageGroupId, string subject)
        {
            string messagePayload = @"
            {
                ""default"": ""Exemples de message de secours"",
                ""GCM"": ""{ \""notification\"": { \""body\"": \""Notification for dyno\"", \""title\"": \""Notification\"" } }""
            }";
            var request = new PublishRequest
            {
                TopicArn = topicArn,
                Message = messagePayload,
                MessageStructure = "json"
            };
            PublishResponse response = await _snsClient.PublishAsync(request);

            if(response.MessageId != null ) 
            {
                return new ResponseAPI<string>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = response.MessageId
                };
            }

            return new ResponseAPI<string>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Message not send"
            };
        }

        public async Task<ResponseAPI<string>> CreateTopic(string topicName)
        {
            CreateTopicResponse? createTopicResponse = await _snsClient.CreateTopicAsync(new CreateTopicRequest
            {
                Name = topicName
            });
            if (createTopicResponse != null)
            {
                string topicArn = createTopicResponse.TopicArn;
                return new ResponseAPI<string>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = topicArn
                };
            }

            return new ResponseAPI<string>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in creation topic"
            };

        }

        public async Task<ResponseAPI<string>> CreateEndPoint(string endpoint, string platformApplicationArn)
        {
            var createEndpointArn = await _snsClient.CreatePlatformEndpointAsync(new CreatePlatformEndpointRequest
            {
                PlatformApplicationArn = platformApplicationArn,
                Token = endpoint // Replace with the device token for the specific platform.
            });
            return new ResponseAPI<string>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = createEndpointArn.EndpointArn
            };
        }

        public async Task<ResponseAPI<string>> DeleteTopic(string topicArn)
        {
            var deleteTopicRequest = new DeleteTopicRequest
            {
                TopicArn = topicArn
            };

            DeleteTopicResponse deleteTopicResponse = await _snsClient.DeleteTopicAsync(deleteTopicRequest);
            return new ResponseAPI<string>
            {
                StatusCode = deleteTopicResponse.HttpStatusCode
            };

        }

        public async Task<ResponseAPI<string>> Subscribe(string protocol, string topicArn, string endPoint)
        {
            var subscribeRequest = new SubscribeRequest
            {
                Protocol = protocol, // the protocol for delivering messages, e.g., "application" for push notifications, "sms" for SMS, "email" for email
                TopicArn = topicArn,
                Endpoint = endPoint //Receive the device endpoint (e.g., FCM token or APNS token) from the mobile app
            };

            SubscribeResponse subscribeResponse = await _snsClient.SubscribeAsync(subscribeRequest);
            return new ResponseAPI<string>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = subscribeResponse.SubscriptionArn
            };
        }

        public async Task<ResponseAPI<string>> Unsubscribe(string subscriptionArn)
        {
            var subscribeRequest = new UnsubscribeRequest
            {
                SubscriptionArn = subscriptionArn
            };

            UnsubscribeResponse UnsubscribeResponse = await _snsClient.UnsubscribeAsync(subscribeRequest);
            return new ResponseAPI<string>
            {
                StatusCode = UnsubscribeResponse.HttpStatusCode
            };
        }
    }
}
