using Application.IServices;
using Application.Models.BMs;
using Platform.Dyno.Payment.DTO;
using AutoMapper;
using Domain.Enums;
using Microsoft.Extensions.Logging;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;

namespace Application.Services
{
    /// <summary>
    /// Service de gestion des transactions distribuées entre Neo4j et Blockchain
    /// Implémente le pattern Saga avec compensation
    /// </summary>
    public class DistributedTransactionService : IDistributedTransactionService
    {
        private readonly ITransactionService _neo4jTransactionService;
        private readonly ITransactionBlockchainService _blockchainTransactionService;
        private readonly ILogger<DistributedTransactionService> _logger;
        private readonly IMapper _mapper;

        public DistributedTransactionService(
            ITransactionService neo4jTransactionService,
            ITransactionBlockchainService blockchainTransactionService,
            ILogger<DistributedTransactionService> logger,
            IMapper mapper)
        {
            _neo4jTransactionService = neo4jTransactionService;
            _blockchainTransactionService = blockchainTransactionService;
            _logger = logger;
            _mapper = mapper;
        }

        /// <summary>
        /// Exécute une transaction distribuée avec garantie ACID
        /// </summary>
        public async Task<ResponseAPI<TransactionDTO>> ExecuteDistributedTransaction(
            TransactionDTO transactionDTO, 
            Guid userId, 
            string pinCode)
        {
            var sagaId = Guid.NewGuid();
            _logger.LogInformation("Starting distributed transaction saga {SagaId} for transaction {TransactionId}", 
                sagaId, transactionDTO.Id);

            var compensationActions = new List<Func<Task<bool>>>();

            try
            {
                // Étape 1: Validation préalable
                var validationResult = await ValidateTransaction(transactionDTO, pinCode);
                if (!validationResult.IsValid)
                {
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = validationResult.ErrorMessage
                    };
                }

                // Étape 2: Réservation des fonds dans Neo4j (sans commit final)
                var neo4jReservationResult = await ReserveFundsInNeo4j(transactionDTO, userId, pinCode);
                if (neo4jReservationResult.StatusCode != HttpStatusCode.OK)
                {
                    return neo4jReservationResult;
                }

                // Ajouter l'action de compensation pour Neo4j
                compensationActions.Add(() => CompensateNeo4jReservation(transactionDTO, userId));

                // Étape 3: Exécution sur la blockchain
                var blockchainResult = await ExecuteBlockchainTransaction(transactionDTO, userId, pinCode);
                if (blockchainResult.StatusCode != HttpStatusCode.OK)
                {
                    _logger.LogError("Blockchain transaction failed for saga {SagaId}, executing compensation", sagaId);
                    await ExecuteCompensation(compensationActions);
                    return blockchainResult;
                }

                // Étape 4: Confirmation finale dans Neo4j
                var neo4jConfirmResult = await ConfirmNeo4jTransaction(transactionDTO, userId);
                if (neo4jConfirmResult.StatusCode != HttpStatusCode.OK)
                {
                    _logger.LogError("Neo4j confirmation failed for saga {SagaId}, executing full compensation", sagaId);
                    
                    // Compenser la blockchain ET Neo4j
                    await CompensateBlockchainTransaction(transactionDTO, userId, pinCode);
                    await ExecuteCompensation(compensationActions);
                    
                    return neo4jConfirmResult;
                }

                _logger.LogInformation("Distributed transaction saga {SagaId} completed successfully", sagaId);
                return neo4jConfirmResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in distributed transaction saga {SagaId}", sagaId);
                await ExecuteCompensation(compensationActions);
                
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "Distributed transaction failed: " + ex.Message
                };
            }
        }

        private async Task<(bool IsValid, string ErrorMessage)> ValidateTransaction(
            TransactionDTO transactionDTO, 
            string pinCode)
        {
            // Validation des wallets
            var senderWallet = await _neo4jTransactionService.GetWalletAsync(transactionDTO.SenderWalletId);
            var receiverWallet = await _neo4jTransactionService.GetWalletAsync(transactionDTO.ReceiverWalletId);

            if (senderWallet == null)
                return (false, "Sender wallet not found");

            if (receiverWallet == null)
                return (false, "Receiver wallet not found");

            if (senderWallet.Balance < transactionDTO.Amount)
                return (false, "Insufficient funds");

            if (!senderWallet.IsActive)
                return (false, "Sender wallet is not active");

            return (true, string.Empty);
        }

        private async Task<ResponseAPI<TransactionDTO>> ReserveFundsInNeo4j(
            TransactionDTO transactionDTO, 
            Guid userId, 
            string pinCode)
        {
            try
            {
                // Créer une transaction de réservation dans Neo4j
                var reservationTransaction = new TransactionDTO
                {
                    Id = transactionDTO.Id,
                    SenderWalletId = transactionDTO.SenderWalletId,
                    ReceiverWalletId = transactionDTO.ReceiverWalletId,
                    Amount = transactionDTO.Amount,
                    Status = Platform.Dyno.Shared.Enum.TransactionStatus.pending, // Status de réservation
                    TransactionDate = DateTime.UtcNow
                };

                return await _neo4jTransactionService.CreateReservationAsync(reservationTransaction, userId, pinCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reserve funds in Neo4j");
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "Failed to reserve funds: " + ex.Message
                };
            }
        }

        private async Task<ResponseAPI<TransactionDTO>> ExecuteBlockchainTransaction(
            TransactionDTO transactionDTO, 
            Guid userId, 
            string pinCode)
        {
            try
            {
                return await Task.FromResult(_blockchainTransactionService.Create(transactionDTO, userId, pinCode));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to execute blockchain transaction");
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "Blockchain transaction failed: " + ex.Message
                };
            }
        }

        private async Task<ResponseAPI<TransactionDTO>> ConfirmNeo4jTransaction(
            TransactionDTO transactionDTO, 
            Guid userId)
        {
            try
            {
                // Confirmer la transaction réservée dans Neo4j
                return await _neo4jTransactionService.ConfirmReservationAsync(transactionDTO.Id, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to confirm Neo4j transaction");
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "Failed to confirm transaction: " + ex.Message
                };
            }
        }

        private async Task<bool> CompensateNeo4jReservation(TransactionDTO transactionDTO, Guid userId)
        {
            try
            {
                _logger.LogInformation("Compensating Neo4j reservation for transaction {TransactionId}", transactionDTO.Id);
                await _neo4jTransactionService.CancelReservationAsync(transactionDTO.Id, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compensate Neo4j reservation");
                return false;
            }
        }

        private async Task<bool> CompensateBlockchainTransaction(TransactionDTO transactionDTO, Guid userId, string pinCode)
        {
            try
            {
                _logger.LogInformation("Compensating blockchain transaction for transaction {TransactionId}", transactionDTO.Id);
                
                // Créer une transaction inverse sur la blockchain
                var compensationTransaction = new TransactionDTO
                {
                    Id = Guid.NewGuid(),
                    SenderWalletId = transactionDTO.ReceiverWalletId,
                    ReceiverWalletId = transactionDTO.SenderWalletId,
                    Amount = transactionDTO.Amount,
                    TransactionDate = DateTime.UtcNow
                };

                var result = _blockchainTransactionService.Create(compensationTransaction, userId, pinCode);
                return result.StatusCode == HttpStatusCode.OK;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compensate blockchain transaction");
                return false;
            }
        }

        private async Task ExecuteCompensation(List<Func<Task<bool>>> compensationActions)
        {
            foreach (var action in compensationActions.AsEnumerable().Reverse())
            {
                try
                {
                    await action();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Compensation action failed");
                }
            }
        }
    }
}
