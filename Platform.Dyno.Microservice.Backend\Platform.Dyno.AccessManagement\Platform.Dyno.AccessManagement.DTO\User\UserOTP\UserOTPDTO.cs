﻿using System.ComponentModel.DataAnnotations;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.DTO.User.UserOTP
{
    public class UserOTPDTO : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Code is required")]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "The 'Code' must be a 4-digit number.")]
        public int Code { get; set; }
        public bool IsConfirmed { get; set; }
        #endregion

        #region Structure
        public UserDTO? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
