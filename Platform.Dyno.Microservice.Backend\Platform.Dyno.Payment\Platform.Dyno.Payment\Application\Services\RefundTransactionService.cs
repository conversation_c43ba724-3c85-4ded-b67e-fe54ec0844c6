﻿using Application.Interfaces;
using Application.IServices;
using Application.Models.BMs;
using Application.Security;
using AutoMapper;
using Domain.Entities;
using Domain.Enums.Query;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Services
{
    public class RefundTransactionService : IRefundTransactionService
    {
        private readonly IGenericRepository _Repository;
        private readonly IMapper _mapper;
        private readonly ILogger<RefundTransactionService> _logger;
        private readonly IWalletService _walletService;

        public RefundTransactionService(IGenericRepository Repository, 
            IMapper mapper,
            ILogger<RefundTransactionService> logger,
            IWalletService walletService)
        {
            _Repository = Repository;
            _mapper = mapper;
            _logger = logger;
            _walletService = walletService;
        }

        

        #region Get
        public ResponseAPI<List<RefundTransactionDTO>> GetAll(string sort = "", Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var query = @$" MATCH (sender:Wallet)-[t:RefundTransaction]->(receiver:Wallet)
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var refundTransactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;

            List<RefundTransactionDTO> refundTransactionsDtO = refundTransactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(t);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                return _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
            }).ToList();

            ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = refundTransactionsDtO,
            };
            return response;
        }

        public ResponseAPI<RefundTransactionDTO> Get(Guid id)
        {
            try
            {
                string? query = @$"MATCH (sender:Wallet)-[t:RefundTransaction{{Id:$TransactionId}}]->(receiver:Wallet)
                            RETURN t{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                    { "TransactionId", id.ToString() }
                };

                Dictionary<string, object> refundTransaction = _Repository.ExecuteReadScalarAsync<Dictionary<string, object>>(query, parameters).Result;
                if(refundTransaction == null )
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"Transaction with id {id} is not found",
                    };
                }


                refundTransaction["TransactionDate"] = ((ZonedDateTime)refundTransaction["TransactionDate"]).UtcDateTime;
                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(refundTransaction);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                RefundTransactionDTO refundTransactionDTO = _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
                
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = refundTransactionDTO,
                }; 

            }
            catch (Exception ex)
            {
                _logger.LogError("Error with get transaction by id", ex);
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Transaction with id {id} is not found",
                }; 
            }
        }

        public ResponseAPI<List<RefundTransactionDTO>> GetUserSentTransactions(Guid userId, string sort = "", Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var query = @$" MATCH (sender:Wallet{{AssignedToId:$userId}})-[t:RefundTransaction]->(receiver:Wallet)
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"userId",userId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            List<Dictionary<string, object>> refundTransactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;
            
            List<RefundTransactionDTO> refundTransactionsDtO = refundTransactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(t);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                return _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
            }).ToList();

            return new ResponseAPI<List<RefundTransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = refundTransactionsDtO,
            };
        }

        public ResponseAPI<List<RefundTransactionDTO>> GetUserReceivedTransactions(Guid userId, string sort = "", Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {

            var query = @$" MATCH (sender:Wallet)-[t:RefundTransaction]->(receiver:Wallet{{AssignedToId:$userId}})
                            RETURN t{{.* }}
                            ORDER BY t.{sort} {order.ToString().ToUpper()}
                            SKIP $skip
                            LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                {"userId",userId.ToString() },
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            List<Dictionary<string, object>> refundTransactions = _Repository.ExecuteReadDictionaryAsync(query, "t", parameters).Result;

            List<RefundTransactionDTO> refundTransactionsDtO = refundTransactions.Select(t =>
            {
                t["TransactionDate"] = ((ZonedDateTime)t["TransactionDate"]).UtcDateTime;
                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(t);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                return _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
            }).ToList();

            return new ResponseAPI<List<RefundTransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = refundTransactionsDtO,
            };
        }

        public ResponseAPI<List<RefundTransactionDTO>> GetUserTransactions(Guid userId, string sort = "", Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0)
        {
            var sentTransactionsResponse = GetUserSentTransactions(userId, sort, order = Ordering.Desc, limit, skip);
            var receivedTransactionsResponse = GetUserReceivedTransactions(userId, sort, order = Ordering.Desc, limit, skip);
            if (sentTransactionsResponse.StatusCode == System.Net.HttpStatusCode.OK && receivedTransactionsResponse.StatusCode == System.Net.HttpStatusCode.OK)
            {
                List<RefundTransactionDTO> sentTransactions = sentTransactionsResponse?.ObjectValue ?? new List<RefundTransactionDTO>();
                List<RefundTransactionDTO> receivedTransactions = receivedTransactionsResponse?.ObjectValue ?? new List<RefundTransactionDTO>();
                if (receivedTransactions != null && receivedTransactions.Count > 0)
                {
                    foreach (var receivedTransaction in receivedTransactions)
                    {
                        receivedTransaction.IsCredit = true;
                    }
                }

                List<RefundTransactionDTO>? transactions = receivedTransactions?.Concat(sentTransactions).ToList();
                return new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = transactions,
                };
            }
            return new ResponseAPI<List<RefundTransactionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = $"An error occured while fetching transactions. Sent Transaction Exeception: {sentTransactionsResponse.ExceptionMessage}. Received transaction exeception : {receivedTransactionsResponse.ExceptionMessage}"
            };
        }

        #endregion

        #region Create
        public ResponseAPI<RefundTransactionDTO> Create(RefundTransactionDTO transactionDTO)
        {
            var transactionBM = _mapper.Map<RefundTransactionBM>(transactionDTO);
            var senderWallet = _walletService.Get(transactionBM.SenderWalletId).ObjectValue;
            if (senderWallet == null)
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Sender wallet is not found",
                };
            }
            var receiverWallet = _walletService.Get(transactionBM.ReceiverWalletId).ObjectValue;
            if (receiverWallet == null)
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Receiver wallet is not found",
                };
            }
            if (transactionBM != null)
            {
                var query = @"MATCH (sender:Wallet {Id: $SenderId}), (receiver:Wallet {Id: $ReceiverId})
                                CREATE (sender)-[t:RefundTransaction
                                {   Id: $TransactionId,
                                    QrCodeId:$QrCodeId,
                                    Amount: $Amount,
                                    SenderWalletId:$SenderId,
                                    ReceiverWalletId:$ReceiverId,
                                    TransactionDate:$TransactionDate,
                                    Status:$Status,
                                    RefundStatus:$RefundStatus
                                    } 
                                ]->(receiver)
                                RETURN t as t;";
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "TransactionId",transactionBM.Id.ToString()  },
                    { "SenderId",transactionBM.SenderWalletId.ToString()  },
                    { "ReceiverId",transactionBM.ReceiverWalletId.ToString()  },
                    { "QrCodeId",transactionBM.QrCodeId.ToString()  },
                    { "Amount",transactionBM.Amount  },
                    { "TransactionDate", transactionBM.TransactionDate },
                    { "Status", transactionBM.Status.ToString() },
                    { "RefundStatus", RefundStatus.InProgress.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
                
                if(result == null)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Transaction is not created",
                    };
                }

                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(result);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                RefundTransactionDTO refundTransactionDTO = _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = refundTransactionDTO,
                }; 
            }
            else
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Transaction must not be null"
                };
            }
        }
        #endregion

        #region Update 
        public ResponseAPI<RefundTransactionDTO> UpdateRefundStatus(Guid id, RefundStatus refundStatus)
        {
            var query = @" MATCH (:Wallet)-[refundTransaction:RefundTransaction {Id: $id}]->(:Wallet)
                           SET refundTransaction.RefundStatus = $refundStatus
                           RETURN refundTransaction;";

            IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "id", id.ToString()  },
                    { "refundStatus", refundStatus.ToString()  },
                };
            var result = _Repository.ExecuteWriteTransactionAsync<IRelationship>(query, parameters).Result;
            if(result != null)
            {
                RefundTransactionEntity refundTransactionEntity = _mapper.Map<RefundTransactionEntity>(result);
                RefundTransactionBM refundTransactionBM = _mapper.Map<RefundTransactionBM>(refundTransactionEntity);
                RefundTransactionDTO refundTransactionDTO = _mapper.Map<RefundTransactionDTO>(refundTransactionBM);
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = refundTransactionDTO
                };
            }

            return new ResponseAPI<RefundTransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest
            };
        }
        #endregion



    }
}
