﻿using Platform.Dyno.Shared.Enum;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WatchDog;

namespace Platform.Dyno.Notification.DTO.Notification
{
    [ProtoContract]
    public class WatchDogNotifDTO
    {
        [ProtoMember(1)]
        public Guid MicroserviceId { get; set; }
        [ProtoMember(2)]
        public string MicroserviceName { get; set; } = string.Empty;
        [ProtoMember(3)]
        public string Type { get; set; } = string.Empty;
        [ProtoMember(4)]
        public LastUpDate LastUpDate { get; set; } = LastUpDate.None;
        [ProtoMember(5)]
        public State State { get; set; }
        [ProtoMember(6)]
        public string Error { get; set; } = string.Empty;
    }
}
