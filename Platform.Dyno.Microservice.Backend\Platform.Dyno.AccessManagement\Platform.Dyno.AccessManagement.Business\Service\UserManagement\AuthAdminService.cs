﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.EmailConfirmation;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class AuthAdminService : IAuthAdminService
    {
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;
        private readonly ICompanyService _companyService;
        private readonly IMacAddressService _macAddressService;
        private readonly IUserTokenService _userTokenService;
        private readonly IUserOTPService _userOTPService;
        private readonly IPermissionService _permissionService;
        private readonly UserManager<UserEntity> _userManager;
        private readonly IEmailingService _emailService;
        private readonly IMapper _mapper;
        private readonly Configuration _configuration;
        private readonly IUnitOfWork<UserEntity> _userRepository;
        private readonly int _maxFailedAttempts = 3;
        private readonly TimeSpan _lockoutDuration = TimeSpan.FromMinutes(3);
        public AuthAdminService(
            IUserService userService,
            IRoleService roleService,
            ICompanyService companyService,
            IMacAddressService macAddressService,
            IUserTokenService userTokenService,
            IUserOTPService userOTPService,
            IPermissionService permissionService,
            UserManager<UserEntity> userManager,
            IEmailingService emailService,
            Configuration configuration,
            IUnitOfWork<UserEntity> userRepository,
            IMapper mapper)
        {
            _userService = userService;
            _roleService = roleService;
            _companyService = companyService;
            _macAddressService = macAddressService;
            _userTokenService = userTokenService;
            _userOTPService = userOTPService;
            _permissionService = permissionService;
            _userManager = userManager;
            _emailService = emailService;
            _configuration = configuration;
            _userRepository = userRepository;
            _mapper = mapper;
        }

        #region Login
        public ResponseAPI<AuthResponseDTO> Login(AdminLoginDTO loginDto, LanguageType language)
        {
            ResponseAPI<List<UserDTO>> response = _userService.Get(user =>
                                                    user.NormalizedEmail == loginDto.Email.ToUpper() &&
                                                    user.Status == Status.Active
                                                    && user.UserType != UserType.Client && 
                                                    user.UserType != UserType.Cashier);

            #region Test exception
            if (response.ObjectValue == null || response.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }

            if (response.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use this email !"
                };
            }

            if (response.ObjectValue.FirstOrDefault()?.EmailConfirmed == false)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Account Not Confirmed: Please check your email for a " +
                    "confirmation link and follow the instructions to confirm your account " +
                    "before logging in."
                };
            }

            if(response.ObjectValue.FirstOrDefault().LockoutEnabled && response.ObjectValue.FirstOrDefault().LockoutEnd > DateTime.UtcNow)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = $"User is steal lockout for another {response.ObjectValue.FirstOrDefault()?.LockoutEnd - DateTime.UtcNow}"
                };
            }
            #endregion

            UserBM user = _mapper.Map<UserBM>(response.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);
            if(userEntity != null && userEntity.Picture != null) 
            {
                string url = $"{_configuration.AWSS3URL}/Images/";
                int? index = userEntity.Picture?.IndexOf(url);
                if (index != null && index != -1)
                {
                    userEntity.Picture = userEntity.Picture?.Substring((int)(index + url.Length));
                };
            };
            

            var passwordHasher = new PasswordHasher<UserEntity>();
            Console.WriteLine("le mot de passe est : " + loginDto.Password);
            bool isCorrectPassword = passwordHasher.VerifyHashedPassword(userEntity, userEntity.PasswordHash, loginDto.Password) == PasswordVerificationResult.Success ? true : false;
            Console.WriteLine("le haché est : " + passwordHasher.HashPassword(userEntity,loginDto.Password));
            if (isCorrectPassword)
            {
                userEntity.LockoutEnabled = false;
                userEntity.AccessFailedCount = 0;
                userEntity.LockoutEnd = null;
                _userRepository.Repository.Update(userEntity);
                _userRepository.Save();

                ResponseAPI<List<PermissionDTO>> permissionsResponse = _permissionService.GetAllAvailablePermissionsByUser(response.ObjectValue?.FirstOrDefault());
                ResponseAPI<CompanyDTO> company = _companyService.Get(user.CompanyId);
                ResponseAPI<UserTokenDTO> userToken = _userTokenService.Create(new UserTokenDTO
                {                  
                    Token = AuthManagement.CreateToken(response.ObjectValue?.FirstOrDefault(), company.ObjectValue, permissionsResponse.ObjectValue,
                    _configuration.Key, _configuration.TokenLifeTime, _configuration.Issuer),
                    RefreshToken = AuthManagement.GenerateRefreshToken(),
                    ExpiredDate = DateTime.Now.ToUniversalTime().AddDays(Convert.ToDouble(_configuration.RefreshTokenLifeTime)),
                    UserId = user.Id
                });

                if (response.ObjectValue != null && response.ObjectValue?.FirstOrDefault()?.MacAddresses != null &&
                response.ObjectValue.FirstOrDefault()?.MacAddresses?.Count > 0)
                {
                    foreach (var macAddress in response.ObjectValue.FirstOrDefault().MacAddresses)
                    {
                        if (macAddress.MacAddress == loginDto.MacAddress && macAddress.IsConfirmed && macAddress.IsSaved)
                        {
                            return new ResponseAPI<AuthResponseDTO>
                            {
                                StatusCode = System.Net.HttpStatusCode.OK,
                                ExceptionMessage = "User login successfully",
                                ObjectValue = new AuthResponseDTO
                                {
                                    Token = userToken.ObjectValue?.Token,
                                    RefreshToken = userToken.ObjectValue?.RefreshToken,
                                    ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                                    UserProfile = new UserProfileDTO
                                    {
                                        FullName = user.FullName,
                                        Email = user.Email,
                                        DateOfBirth = user.DateOfBirth,
                                        CountryCode= user.CountryCode,
                                        PhoneNumber = user.PhoneNumber,
                                        Picture = user.Picture
                                    },
                                    //CompanyId = userToken.ObjectValue?.CompanyId
                                }
                            };
                        }
                    }

                }

                ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                {
                    MacAddress = loginDto.MacAddress,
                    IsConfirmed = false,
                    UserId = user.Id,
                    IsSaved = false
                });

                ResponseAPI<UserOTPDTO> userOTP = _userOTPService.Create(loginDto.Email);
                if (userOTP.ObjectValue != null)
                {
                    ResponseAPI<bool> sendEmail = _emailService.SendMacAddressCodeEmail(loginDto.Email, userOTP.ObjectValue.Code, language);
                }

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Redirect,
                    ExceptionMessage = "You are connecting with new device : Please check your email for a " +
                        "confirmation code and follow the instructions to confirm your account " +
                        "before logging in.",
                    ObjectValue = new AuthResponseDTO
                    {
                        Token = userToken.ObjectValue?.Token,
                        RefreshToken = userToken.ObjectValue?.RefreshToken,
                        ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                        UserProfile = new UserProfileDTO
                        {
                            FullName = user.FullName,
                            Email = user.Email,
                            DateOfBirth = user.DateOfBirth,
                            CountryCode = user.CountryCode,
                            PhoneNumber = user.PhoneNumber,
                            Picture = user.Picture
                        },
                        //CompanyId = userToken.ObjectValue?.CompanyId
                    }
                };
            }
            if(userEntity.LockoutEnabled)
            {
                if(userEntity.LockoutEnd < DateTime.UtcNow)
                {
                    userEntity.LockoutEnabled = false;
                    userEntity.AccessFailedCount = 1;
                    userEntity.LockoutEnd = null;
                    _userRepository.Repository.Update(userEntity);
                    _userRepository.Save();

                    return new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "Incorrect Credentials: Please try again."
                    };

                } 
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = $"User is steal lockout for another {userEntity.LockoutEnd - DateTime.UtcNow}"
                };
            }
            userEntity.AccessFailedCount = ++ userEntity.AccessFailedCount;
            if(userEntity.AccessFailedCount == _maxFailedAttempts)
            {
                userEntity.LockoutEnabled= true;
                userEntity.LockoutEnd = DateTime.UtcNow + _lockoutDuration;
                _userRepository.Repository.Update(userEntity);
                _userRepository.Save();

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = $"User is locked , please wait {_lockoutDuration} and then try again."
                };
            }

            _userRepository.Repository.Update(userEntity);
            _userRepository.Save();

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "Incorrect Credentials: Please try again."
            };

        }
        #endregion

        #region Register
        public async Task< ResponseAPI<AuthResponseDTO>> Register(AdminRegisterDTO registerDto, Guid? createdUserId, Guid? companyId)
        {
            ResponseAPI<List<UserDTO>> responseUser =
               _userService.Get(user => user.NormalizedEmail == registerDto.Email.ToUpper() 
               && user.UserType != UserType.Client 
               && user.UserType != UserType.Cashier 
               && user.Status != Status.Deleted);

            if (responseUser.ObjectValue != null && responseUser.ObjectValue.Count > 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Found,
                    ExceptionMessage = "This Email is already used with another account"
                };
            }

            CompanyDTO? company = _companyService.Get(registerDto.CompanyId).ObjectValue;
            if(company == null) 
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Found,
                    ExceptionMessage = "Company not found !"
                };
            }
            if(company.Status != Status.Active)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Found,
                    ExceptionMessage = "Company not active !"
                };
            }
            UserDTO userDTO = _mapper.Map<UserDTO>(registerDto);
            userDTO.UserName = Guid.NewGuid().ToString();
            userDTO.NormalizedUserName = userDTO.UserName.ToUpper();
            if(companyId != null)
            {
                userDTO.CompanyId = (Guid)(userDTO?.CompanyId == null ? companyId : userDTO.CompanyId);
            }
            
            ResponseAPI<UserDTO> createdUser = _userService.Create(userDTO, creatorUserId:createdUserId, companyId: companyId, userType:registerDto.CreatorUserType); 
            var emailbody = @"
<!DOCTYPE html>
<html>
<head>

  <meta charset=""utf-8"">
  <meta http-equiv=""x-ua-compatible"" content=""ie=edge"">
  <title>Email Confirmation</title>
  <meta name=""viewport"" content=""width=device-width, initial-scale=1"">
  <style type=""text/css"">
 
  @media screen {{
    @font-face {{
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 400;
      src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
    }}
    @font-face {{
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 700;
      src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff');
    }}
  }}

  body,
  table,
  td,
  a {{
    -ms-text-size-adjust: 100%; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }}
 
  table,
  td {{
    mso-table-rspace: 0pt;
    mso-table-lspace: 0pt;
  }}

  img {{
    -ms-interpolation-mode: bicubic;
  }}
 
  a[x-apple-data-detectors] {{
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-decoration: none !important;
  }}

  div[style*=""margin: 16px 0;""] {{
    margin: 0 !important;
  }}
  body {{
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }}

  table {{
    border-collapse: collapse !important;
  }}
  a {{
    color: #1a82e2;
  }}
  img {{
    height: auto;
    line-height: 100%;
    text-decoration: none;
    border: 0;
    outline: none;
  }}
  </style>

</head>
<body style=""background-color: #e9ecef;"">

  <div class=""preheader"" style=""display: none; max-width: 0; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: #fff; opacity: 0;"">
    A preheader is the short summary text that follows the subject line when an email is viewed in the inbox.
  </div>

  <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">

        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">
          <tr>
            <td align=""center"" valign=""top"" style=""padding: 36px 24px;"">
              <a href=""http://loadbalancerdyno-*********.us-east-1.elb.amazonaws.com:80/#/"" target=""_blank"" style=""display: inline-block;"">
                <img src=""https://dynofiles.s3.amazonaws.com/Images/Logo.png"" alt=""Logo"" border=""0"" width=""200"" style=""display: block; width: 200px; max-width: 200px; min-width: 100px;"">
              </a>
            </td>
          </tr>
        </table>
  
      </td>
    </tr>

    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">
          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 36px 24px 0; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; border-top: 3px solid #d4dadf;"">
              <h1 style=""margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -1px; line-height: 48px;"">Confirm Your Email Address</h1>
            </td>
          </tr>
        </table>
 
      </td>
    </tr>


    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"">
              <p style=""margin: 0;"">Tap the button below to confirm your email address. If you didn't create an account with <a href=""http://loadbalancerdyno-*********.us-east-1.elb.amazonaws.com:80/#/"">Dyno</a>, you can safely delete this email.</p>
            </td>
          </tr>
          <tr>
            <td align=""left"" bgcolor=""#ffffff"">
              <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
                <tr>
                  <td align=""center"" bgcolor=""#ffffff"" style=""padding: 12px;"">
                    <table border=""0"" cellpadding=""0"" cellspacing=""0"">
                      <tr>
                        <td align=""center"" bgcolor=""#1a82e2"" style=""border-radius: 6px;"">
                          <a href=""#URL#"" target=""_blank"" style=""display: inline-block; padding: 16px 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px;"">Confirm </a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"">
              <p style=""margin: 0;"">If that doesn't work, copy and paste the following link in your browser:</p>
              <p style=""margin: 0;""><a href=""#URL#/"" target=""_blank"">#URL#</a></p>
            </td>
          </tr>

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px; border-bottom: 3px solid #d4dadf"">
              <p style=""margin: 0;"">Cheers,<br> Paste</p>
            </td>
          </tr>
     

        </table>

      </td>
    </tr>

    <tr>
      <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 24px;"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">

          <tr>
            <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;"">
              <p style=""margin: 0;"">You received this email because we received a request for [type_of_action] for your account. If you didn't request [type_of_action] you can safely delete this email.</p>
            </td>
          </tr>
          <tr>
            <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;"">
              <p style=""margin: 0;"">Dyno & Motiva Systems, THE DOT 1053 Lac Malären, Tunis</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>";


            string? tokenConfirmationEmail = await GenerateEmailConfirmationToken(createdUser.ObjectValue?.Id);

            ResponseAPI<bool> responseAPI = _emailService.sendConfirmationEmail(createdUser.ObjectValue!.Email, emailbody, tokenConfirmationEmail);

            if (responseAPI.ObjectValue)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = responseAPI.StatusCode,
                    ExceptionMessage = responseAPI.ExceptionMessage,
                    ObjectValue= new AuthResponseDTO
                    {
                        Token = tokenConfirmationEmail
                    }
                };
            }
            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = createdUser.StatusCode,
                ExceptionMessage = createdUser.ExceptionMessage
            };
        }

        private async Task<string?> GenerateEmailConfirmationToken(Guid? userId)
        {
            if(userId == null)
            {
                return null;
            }
            ResponseAPI<UserDTO> responseUser = _userService.Get((Guid)userId);
            if(responseUser.ObjectValue == null)
            {
                return null;
            }

            var userBM = _mapper.Map<UserBM>(responseUser.ObjectValue);
            var userEntity = _mapper.Map<UserEntity>(userBM);

            return await _userManager.GenerateEmailConfirmationTokenAsync(userEntity);

        }
        #endregion

        #region Logout
        public ResponseAPI<AuthResponseDTO> LogOut(Guid userId, string accessToken)
        {
            ResponseAPI<List<UserTokenDTO>> tokens = _userTokenService.Get(token =>
            token.UserId == userId && token.Token == accessToken);
            if (tokens.StatusCode == System.Net.HttpStatusCode.OK
                && tokens.ObjectValue != null &&
                tokens.ObjectValue.Count > 0)
            {
                foreach (var token in tokens.ObjectValue)
                {
                    ResponseAPI<UserTokenDTO> deleteToken = _userTokenService.Remove(token.Id);
                    if (deleteToken.StatusCode != System.Net.HttpStatusCode.OK)
                    {
                        return new ResponseAPI<AuthResponseDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Conflict,
                            ExceptionMessage = "User has the same token more than one time : " + deleteToken.ExceptionMessage
                        };
                    }

                }

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = "User logout successfully !"
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "Invalid Token!"
            };
        }
        #endregion

        #region Refresh Token
        public ResponseAPI<AuthResponseDTO> GetRefreshToken(UserTokenDTO token)
        {
            #region Test RefreshToken &&  Token
            UserTokenDTO? userToken = _userTokenService.Get(token =>
            token.Token == token.Token &&
            token.RefreshToken == token.RefreshToken &&
            token.UserId == token.UserId).ObjectValue?.FirstOrDefault();

            if (userToken == null)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Invalid access token or refresh token"
                };
            }

           if (userToken.ExpiredDate < DateTime.UtcNow)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "The refresh token is expired"
                };
            }

            #endregion

            var principal = AuthManagement.GetPrincipalFromExpiredToken(token.Token, _configuration.Issuer,
                _configuration.Key);

            if (principal == null)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Invalid access token or refresh token"
                };
            }

            SigningCredentials signingCredentials = AuthManagement.GetSigningCredentials(_configuration.Key);
            JwtSecurityToken tokenOptions = AuthManagement.GenerateTokenOption(signingCredentials, principal.Claims.ToList(),
                _configuration.TokenLifeTime, _configuration.Issuer);
            string newToken = new JwtSecurityTokenHandler().WriteToken(tokenOptions);

            userToken.Token = newToken;
            userToken.UserId = token.UserId;

            ResponseAPI<UserTokenDTO> newUserToken = _userTokenService.Update(userToken);
            if (newUserToken.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = new AuthResponseDTO
                    {
                        Token = newToken,
                        RefreshToken = token.RefreshToken,
                        ExpiredDate = token.ExpiredDate
                    }
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = newUserToken.StatusCode,
                ExceptionMessage = newUserToken.ExceptionMessage
            };
        }
        #endregion

        #region Forget Password
        public async Task<ResponseAPI<AuthResponseDTO>> ForgetPassword(string email, string? countryCode = null)
        {
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user => user.NormalizedEmail == email.ToUpper() &&
            user.EmailConfirmed == true && 
            (user.UserType != UserType.Client &&
            user.UserType != UserType.Cashier) &&
            user.Status == Status.Active 
            );

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorize"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user admin use the same email," +
                    " please contact our system!"
                };
            }
            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            var token = await _userManager.GeneratePasswordResetTokenAsync(userEntity);

            var encodedToken = HttpUtility.UrlEncode(token);

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ExceptionMessage = "Please check your email to reset your password",
                ObjectValue = new AuthResponseDTO
                {
                    Token = encodedToken,
                }
            };
        }

        public async Task<ResponseAPI<AuthResponseDTO>> ResetPassword(ResetPasswordDTO resetPassword)
        {
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user => 
            user.NormalizedEmail == resetPassword.Email.ToUpper() &&
            user.EmailConfirmed == true && 
            user.UserType != UserType.Client &&
            user.Status == Status.Active);

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorize"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user admin use the same email," +
                    " please contact our system!"
                };
            }

            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            var result = await _userManager.ResetPasswordAsync(userEntity,
                resetPassword.ResetToken,
                resetPassword.NewPassword);

            if (result.Succeeded)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = result.Errors.FirstOrDefault()?.Code
            };
        }

        public ResponseAPI<AuthResponseDTO> UpdatePassword(UpdatePasswordDTO passwordDTO)
        {
            throw new NotImplementedException();
        }
        #endregion
    }
}
