﻿using Camunda.Worker;
using Camunda.Worker.Variables;
using Microsoft.Extensions.Logging;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SI_Send_Notification_New_SI")]
    public class SendNotifSI : IExternalTaskHandler
    {
        private readonly ILogger<AddInstanceToSalesInvoice> _logger;
        private readonly ISignalRNotificationService _signalRNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ConfigurationDefaultId _configuration;
        public SendNotifSI(ISignalRNotificationService signalRNotificationService,
            ICompanyService companyService, ILogger<AddInstanceToSalesInvoice> logger,
            ConfigurationDefaultId configuration)
        {
            _logger = logger;
            _signalRNotificationService = signalRNotificationService;
            _companyService = companyService;
            _configuration = configuration;
        }
        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
            SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
            //var salesInvoiceDTO = externalTask.Variables["SalesInvoice"].GetValue<SalesInvoiceDTO>();
            //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());



            //string? resultSendEmail = externalTask.Variables["Result_Send_Email_SI"].Value?.ToString();

            // Version corrigée avec gestion d'erreur complète
            if (!externalTask.Variables.TryGetValue("Result_Send_Email_SI", out var emailResultVar))
            {
                _logger.LogError("Variable Result_Send_Email_SI non trouvée");
                throw new InvalidOperationException("Variable de résultat d'email manquante");
            }

            if (emailResultVar is not StringVariable stringVariable || string.IsNullOrEmpty(stringVariable.Value))
            {
                _logger.LogError("Format de variable Result_Send_Email_SI invalide");
                throw new InvalidOperationException("La variable Result_Send_Email_SI doit être une chaîne");
            }

            string resultSendEmail = stringVariable.Value;

            // string? resultSendEmail = externalTask.Variables["Result_Send_Email_SI"].Value.GetString();


            if (salesInvoiceDTO != null)
            {
                CompanyDTO? company = _companyService.Get(salesInvoiceDTO.CompanyId).ObjectValue;
                CompanyDTO? superCompany = _companyService.Get(_configuration.CompanyId).ObjectValue;
                if (company != null)
                {
                    if (resultSendEmail == "fasle")
                    {
                        SignalRNotificationDTO notificationDTO = new SignalRNotificationDTO
                        {
                            Title = "Email Failed",
                            Message = "Email Failed To generate in Sales order !",
                            SendToId = new List<CompanyDTO> { superCompany }

                        };
                        _signalRNotificationService.Create(notificationDTO, companyId: superCompany.Id);
                    }
                    if (superCompany != null)
                    {
                        SignalRNotificationDTO superAdminnotificationDTO = new SignalRNotificationDTO
                        {
                            Title = "Sales Invoice",
                            Message = $"Sales invoice {salesInvoiceDTO.Id} request has been validated !",
                            SendToId = new List<CompanyDTO> { company }

                        };
                        _signalRNotificationService.Create(superAdminnotificationDTO, companyId: superCompany.Id);
                    }

                }
            }

            var completeResult = new CompleteResult();
            return completeResult;
        }
    }
}
