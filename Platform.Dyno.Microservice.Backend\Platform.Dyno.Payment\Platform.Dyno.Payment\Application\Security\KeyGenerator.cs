﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Application.Security
{
    public static class KeyGenerator
    {
        public static Tuple<byte[], byte[]> GenerateKeyPair()
        {
            using (var ecdsa = ECDsa.Create(ECCurve.NamedCurves.nistP256))
            {
                //// Generate key pair
                var privateKey1 = Convert.ToBase64String(ecdsa.ExportECPrivateKey());
                var publicKey1 = Convert.ToBase64String(ecdsa.ExportSubjectPublicKeyInfo());

                // Hyperledger Fabric uses secp256r1 curve
                ecdsa.GenerateKey(ECCurve.NamedCurves.nistP256);

                // Get the parameters
                var parameters = ecdsa.ExportParameters(true);

                // Convert to base64 for easy representation
                var privateKey = parameters.D;
                var publicKey = parameters.Q.X.ToArray();
                return new Tuple<byte[], byte[]>(publicKey, privateKey);
            }
        }
    }
}
