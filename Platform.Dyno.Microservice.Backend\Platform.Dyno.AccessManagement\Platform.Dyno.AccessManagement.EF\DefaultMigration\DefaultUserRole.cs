﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.EF.DefaultMigration
{
    public class DefaultUserRole : IEntityTypeConfiguration<RoleUserEntity>
    {
        private readonly ConfigurationDefaultId _defaultId;

        public DefaultUserRole(ConfigurationDefaultId defaultId)
        {
            _defaultId = defaultId;
        }
        public void Configure(EntityTypeBuilder<RoleUserEntity> builder)
        {
            builder.HasData(
                 new RoleUserEntity { UserId = _defaultId.UserId, RoleId = _defaultId.RoleSuperAdminId }
             );
        }
    }
}
