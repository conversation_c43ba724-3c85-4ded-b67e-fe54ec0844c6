﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.AccessManagement.DataModel.Ticket;

namespace Platform.Dyno.AccessManagement.DataModel.Group;
[Table("Group")]
public class GroupEntity : ReferentialData
{
    public Guid GroupId { get; set; }
    public string Name { get; set; }

    [ForeignKey("CompanyId")]
    public Guid CompanyId { get; set; }
    [JsonIgnore]
    public CompanyEntity? Company { get; set; }
    public IList<EmployeeEntity>? Employees { get; set; }
    public IList<GroupTicketEntity>? GroupTickets { get; set; }

}
