﻿using Microsoft.EntityFrameworkCore;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.CashBack;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Logger;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.EF.DefaultMigration;
using Platform.Dyno.Shared;

namespace Platform.Dyno.AccessManagement.EF;

public class ContextDB : DbContext
{
    private readonly ConfigurationDefaultId _defaultId;
    private readonly Configuration _config;

    public ContextDB(DbContextOptions<ContextDB> options, ConfigurationDefaultId defaultId,
        Configuration config) :
        base(options)
    {
        _defaultId = defaultId;
        _config = config;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

#if DEBUG
        optionsBuilder.EnableSensitiveDataLogging();
#endif
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<UserEntity>()
            .Ignore(e => e.Roles);
        modelBuilder.Entity<RoleEntity>()
            .Ignore(e => e.Users);
        modelBuilder.Entity<CompanyEntity>()
            .Ignore(e => e.Users);

        modelBuilder.Entity<SignalRNotificationEntity>()
            .Ignore(e => e.SendToId);

        #region one to one
        modelBuilder.Entity<SalesOrderEntity>()
        .HasOne(s => s.Document)
        .WithOne()
        .HasForeignKey<SalesOrderEntity>(s => s.DocumentId)
        .IsRequired(false);

        modelBuilder.Entity<SalesInvoiceEntity>()
        .HasOne(s => s.Document)
        .WithOne()
        .HasForeignKey<SalesInvoiceEntity>(s => s.DocumentId)
        .IsRequired(false);

        modelBuilder.Entity<SalesInvoiceEntity>()
        .HasOne(s => s.SalesOrder)
        .WithOne()
        .HasForeignKey<SalesInvoiceEntity>(s => s.SalesOrderId);

        modelBuilder.Entity<BlackListedUserEntity>()
        .HasOne(b => b.User)
        .WithOne()
        .HasForeignKey<BlackListedUserEntity>(s => s.UserId)
        .IsRequired(true);
        #endregion

        #region One_To_Many
        modelBuilder.Entity<SalesOrderEntity>()
                .HasOne(e => e.Company)
                .WithMany(e => e.SalesOrders)
                .HasForeignKey(e => e.CompanyId);
        modelBuilder.Entity<SalesInvoiceEntity>()
                .HasOne(e => e.Company)
                .WithMany(e => e.SalesInvoices)
                .HasForeignKey(e => e.CompanyId);
        modelBuilder.Entity<PaymentDetailsEntity>()
                .HasOne(e => e.Company)
                .WithMany(e => e.PaymentDetails)
                .HasForeignKey(e => e.CompanyId);
        modelBuilder.Entity<CashBackEntity>()
                .HasOne(e => e.PaymentDetails)
                .WithMany(e => e.CashBacks)
                .HasForeignKey(e => e.PaymentDetailsId);

        modelBuilder.Entity<MacAddressEntity>()
                    .HasOne(m => m.User)
                    .WithMany(u => u.MacAddresses)
                    .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserOTPEntity>()
                    .HasOne(m => m.User)
                    .WithMany(u => u.UserOTPs)
                    .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<AddressEntity>()
                    .HasOne(m => m.User)
                    .WithMany(u => u.Addresses)
                    .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<UserTokenEntity>()
                    .HasOne(m => m.User)
                    .WithMany(u => u.UserTokens)
                    .OnDelete(DeleteBehavior.Cascade);
        #endregion


        #region Many_To_Many
        #region RoleUser
        modelBuilder.Entity<RoleUserEntity>()
           .HasKey(x => new { x.RoleId, x.UserId });

        modelBuilder.Entity<RoleUserEntity>()
            .HasOne(pt => pt.Role)
            .WithMany(p => p.RoleUsers)
            .HasForeignKey(pt => pt.RoleId);

        modelBuilder.Entity<RoleUserEntity>()
            .HasOne(pt => pt.User)
            .WithMany(t => t.RoleUsers)
            .HasForeignKey(pt => pt.UserId);
        #endregion

        #region Employees
        modelBuilder.Entity<EmployeeEntity>()
            .HasKey(e => e.EmployeeId);

        modelBuilder.Entity<EmployeeEntity>()
            .HasOne(e => e.User)
            .WithMany()
            .HasForeignKey(e => e.UserId);

        modelBuilder.Entity<EmployeeEntity>()
            .HasOne(e => e.Company)
            .WithMany(e => e.Employees)
            .HasForeignKey(e => e.CompanyId);


        #endregion 
        
        #region Groups

/*        modelBuilder.Entity<GroupEntity>()
            .Ignore(g => g.Tickets);*/
        modelBuilder.Entity<GroupEntity>()
           .HasKey(g => g.GroupId);
        #endregion

        #region Tickets

       /* modelBuilder.Entity<TicketEntity>()
            .Ignore(g => g.Groups);*/
        modelBuilder.Entity<TicketEntity>()
            .HasKey(g => g.TicketId);
        #endregion

        modelBuilder.Entity<EmployeeEntity>()
            .HasOne(e => e.Company)
            .WithMany(e => e.Employees)
            .HasForeignKey(e => e.CompanyId);

        #region GroupTicket
        modelBuilder.Entity<GroupTicketEntity>()
           .HasKey(gt => new { gt.GroupId, gt.TicketId });

        // Configuring the many-to-many relationship
        modelBuilder.Entity<GroupTicketEntity>()
            .HasOne(gt => gt.Group)
            .WithMany(g => g.GroupTickets)
            .HasForeignKey(gt => gt.GroupId);

        modelBuilder.Entity<GroupTicketEntity>()
            .HasOne(gt => gt.Ticket)
            .WithMany(t => t.GroupTickets)
            .HasForeignKey(gt => gt.TicketId);
        #endregion

        #region CompanyNotification
        modelBuilder.Entity<CompanyNotifEntity>()
           .HasKey(x => new { x.CompanyId, x.NotificationId });

        modelBuilder.Entity<CompanyNotifEntity>()
            .HasOne(pt => pt.Company)
            .WithMany(p => p.CompanyNotifs)
            .HasForeignKey(pt => pt.CompanyId);

        modelBuilder.Entity<CompanyNotifEntity>()
            .HasOne(pt => pt.Notification)
            .WithMany(t => t.CompanyNotifs)
            .HasForeignKey(pt => pt.NotificationId);
        #endregion

        #endregion

        modelBuilder.ApplyConfiguration(new DefaultCompany(_defaultId, _config));
        modelBuilder.ApplyConfiguration(new DefaultRole(_defaultId));
        modelBuilder.ApplyConfiguration(new DefaultDynoUser(_defaultId, _config));
        modelBuilder.ApplyConfiguration(new DefaultPermission(_defaultId));
        modelBuilder.ApplyConfiguration(new DefaultUserRole(_defaultId));
    }


    #region dbset


    #region Company
    public DbSet<CompanyEntity> Company { get; set; }
    public DbSet<PaymentDetailsEntity> PaymentDetails { get; set; }
    #endregion

    #region CashBack
    public DbSet<CashBackEntity> CashBack { get; set; }
    public DbSet<FailedCashBackEntity> FailedCashBack { get; set; }
    #endregion

    #region Group
    public DbSet<GroupEntity> Group { get; set; }
    public DbSet<GroupTicketEntity> GroupTicket { get; set; }
    #endregion

    #region User
    public DbSet<UserEntity> User { get; set; }
    public DbSet<UserTokenEntity> UserToken { get; set; }
    public DbSet<UserOTPEntity> UserOTP { get; set; }
    #endregion

    #region Role
    public DbSet<RoleEntity> Role { get; set; }
    public DbSet<RoleUserEntity> RoleUser { get; set; }
    public DbSet<PermissionEntity> Permission { get; set; }
    #endregion

    #region Address
    public DbSet<AddressEntity> Address { get; set; }
    public DbSet<MacAddressEntity> MacAddress { get; set; }
    #endregion

    #region Notification
    public DbSet<MessageNotificationEntity> MessageNotification { get; set; }
    public DbSet<SignalRNotificationEntity> SignalRNotification { get; set; }
    public DbSet<CompanyNotifEntity> CompanyNotification { get; set; }
    #endregion

    #region Employee
    public DbSet<EmployeeEntity> Employee { get; set; }
    #endregion

    #region SalesOrder
    public DbSet<SalesOrderEntity> SalesOrder { get; set; }
    public DbSet<FailedSalesOrderEntity> FailedSalesOrder { get; set; }
    public DbSet<SalesInvoiceEntity> SalesInvoice { get; set; }
    #endregion

    #region Ticket
    public DbSet<TicketEntity> Ticket { get; set; }
    #endregion

    #region Reporting
    public DbSet<DocumentsEntity> Document { get; set; }
    #endregion

    #region Logger
    public DbSet<LogErrorEntity> LogError { get; set; }

    #endregion

    #endregion


}

