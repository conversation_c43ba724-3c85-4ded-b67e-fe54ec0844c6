﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.EF.DefaultMigration
{
    public class DefaultRole : IEntityTypeConfiguration<RoleEntity>
    {
        private readonly ConfigurationDefaultId _defaultId;

        public DefaultRole(ConfigurationDefaultId defaultId)
        {
            _defaultId = defaultId;
        }

        public void Configure(EntityTypeBuilder<RoleEntity> builder)
        {
            builder.HasData(
                new RoleEntity
                {
                    Id = _defaultId.RoleId,
                    Name = DefaultRoles.Default,
                    UserType = UserType.SuperAdmin,
                    NormalizedName = DefaultRoles.Default.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow
                },
                new RoleEntity
                {
                    Id = _defaultId.RoleSuperAdminId,
                    Name = DefaultRoles.SuperAdmin,
                    UserType = UserType.SuperAdmin,
                    NormalizedName = DefaultRoles.SuperAdmin.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow,
                    CreatorUserId = _defaultId.CompanyId
                },
                new RoleEntity
                {
                    Id = _defaultId.RoleCompanyAdminId,
                    Name = DefaultRoles.Admin,
                    UserType = UserType.Company,
                    NormalizedName = DefaultRoles.Admin.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow,
                    CreatorUserId = _defaultId.CompanyId
                },
                new RoleEntity
                {
                    Id = _defaultId.RoleShopAdminId,
                    Name = DefaultRoles.ShopAdmin,
                    UserType = UserType.ShopOwner,
                    NormalizedName = DefaultRoles.ShopAdmin.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow,
                    CreatorUserId = _defaultId.CompanyId
                },
                new RoleEntity
                {
                    Id = _defaultId.RoleClientId,
                    Name = DefaultRoles.Client,
                    UserType = UserType.Client,
                    NormalizedName = DefaultRoles.Client.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow,
                    CreatorUserId = _defaultId.CompanyId
                },
                new RoleEntity
                {
                    Id = _defaultId.RoleCashierId,
                    Name = DefaultRoles.Cashier,
                    UserType = UserType.Cashier,
                    NormalizedName = DefaultRoles.Cashier.ToUpper(),
                    Status = Status.Active,
                    CreationTime = DateTime.UtcNow,
                    CreatorUserId = _defaultId.CompanyId
                });


        }
    }
}
