﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Ticket;
using Platform.Dyno.AccessManagement.Business.Service.Ticket;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Ticket
{
    [Route("Api/[controller]")]
    [ApiController]
    public class TicketController : Controller
    {
        private readonly ITicketService _ticketService;
        private readonly ILogger<TicketController> _logger;
        private readonly ISortingUtility _sortingUtility;
        public TicketController(ITicketService ticketService, ILogger<TicketController> logger,ISortingUtility sortingUtility)
        {
            _ticketService = ticketService; ;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
       {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId== Guid.Empty)
                {
                    var response =  new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<List<TicketDTO>> tickets = _ticketService.GetAll(companyId);
                return Ok(tickets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TicketDTO>> response = new ResponseAPI<List<TicketDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid UserId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || UserId == Guid.Empty)
                {
                    var response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<PagedList<TicketDTO>> ticketsDTO = _ticketService.GetAll(companyId,pagedParameters);
                if (ticketsDTO.ObjectValue != null)
                {
                    ticketsDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, ticketsDTO);
                    ticketsDTO.ObjectValue = _sortingUtility.FilterData(pagedParameters, ticketsDTO);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = ticketsDTO.ObjectValue.TotalCount,
                        PageSize = ticketsDTO.ObjectValue.PageSize,
                        CurrentPage = ticketsDTO.ObjectValue.CurrentPage,
                        TotalPages = ticketsDTO.ObjectValue.TotalPages,
                        HasNext = ticketsDTO.ObjectValue.HasNext,
                        HasPrevious = ticketsDTO.ObjectValue.HasPrevious
                    };
                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(ticketsDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllActive")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllActive([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid UserId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || UserId == Guid.Empty)
                {
                    var response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<PagedList<TicketDTO>> ticketsDTO = _ticketService.GetAllActive(companyId,pagedParameters);
                PaginationData metadata = new PaginationData
                {
                    TotalCount = ticketsDTO.ObjectValue.TotalCount,
                    PageSize = ticketsDTO.ObjectValue.PageSize,
                    CurrentPage = ticketsDTO.ObjectValue.CurrentPage,
                    TotalPages = ticketsDTO.ObjectValue.TotalPages,
                    HasNext = ticketsDTO.ObjectValue.HasNext,
                    HasPrevious = ticketsDTO.ObjectValue.HasPrevious
                };

                Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                return Ok(ticketsDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActive)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }
        [Route("GetTicketsByType/{type}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetTicketsByType(WalletType type)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid UserId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || UserId == Guid.Empty)
                {
                    var response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<List<TicketDTO>> ticketsDTO = _ticketService.GetTicketsByType(companyId, type);
                return Ok(ticketsDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetTicketsByType)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }
        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                ResponseAPI<TicketDTO> response = _ticketService.Get(companyId, id);
                if (response.ObjectValue == null)
                {
                    return BadRequest(Response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<TicketDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] TicketDTO ticketDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                ResponseAPI<TicketDTO> response = _ticketService.Create(companyId, ticketDTO, userId);
                if (response.ObjectValue == null)
                {
                    return BadRequest(response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<TicketDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion


        #region Update
        [Route("Update")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] TicketDTO ticketDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                ResponseAPI<TicketDTO> response = _ticketService.Update(companyId, ticketDTO, userId);
                if (response.ObjectValue == null)
                {
                    return BadRequest(response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<TicketDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        
        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<TicketDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                ResponseAPI<TicketDTO> response = _ticketService.Delete(companyId, id, userId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<TicketDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        
        #endregion
    }
}
