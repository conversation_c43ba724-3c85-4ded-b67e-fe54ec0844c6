﻿using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IBlackListedUserService : IGenericService<BlackListedUserDTO>
    {
        public ResponseAPI<BlackListedUserDTO> BlockedUser(BlackListedUserDTO dtoObject, Guid companyId, UserType userType, Guid blockedUserId);

        public ResponseAPI<BlackListedUserDTO> UnBlockedUser(BlackListedUserDTO dtoObject, Guid companyId, UserType userType, Guid unblockedUserId);
    }
}
