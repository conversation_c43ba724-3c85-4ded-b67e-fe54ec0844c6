﻿using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Transaction
{
    public interface ITransactionService
    {
        ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueReceiverTransactions(Guid userId, int pageSize, int pageNumber);
        ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueSenderTransactions(Guid userId, int pageSize, int pageNumber);
        ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueTransactions(Guid userId, int pageSize, int pageNumber);
        ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueTransactionsByWalletType(Guid userId, WalletType walletType, int pageSize, int pageNumber);
        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionForGroup(Guid companyId,Guid userId,Guid groupId,List<TicketDTO> TicketsId);
        public ResponseAPI<List<TransactionUserDTO>> SendMultipleTicketsToGroups(Guid companyId, Guid userId, List<GroupTicketDTO> groupTickets);

        public ResponseAPI<bool> TransactionClientToCashier(TransactionDTO transaction, string pinCode, Guid senderUserId);

        public ResponseAPI<List<CashierTransactionsDTO>> GetCashiersTransactions(Guid shopOwnerId, PagedParameters pagedParameters);

        ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(UniqueQRCodeTransactionDTO transaction, string pinCode, Guid senderUserId);
        ResponseAPI<List<TransactionUserDTO>> SendTicketsToEmployees(Guid companyId, Guid userId, List<EmployeeTicketDTO> employeeTickets);
    }
}
