﻿using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Notification
{
    public interface ISignalRNotificationService : IGenericService<SignalRNotificationDTO>
    {
        ResponseAPI<SignalRNotificationDTO> CreateValidCashbackNotif(SignalRNotificationDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true);
        ResponseAPI<SignalRNotificationDTO> UpdateIsSeen(Guid id, Guid? updateUserId = null, bool updateCache = true);
    }
}
