﻿using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IAuthAdminService : IAuthService<AdminLoginDTO, AdminRegisterDTO>
    {
        public Task<ResponseAPI<AuthResponseDTO>> ResetPassword(ResetPasswordDTO resetPassword);
    }
}
