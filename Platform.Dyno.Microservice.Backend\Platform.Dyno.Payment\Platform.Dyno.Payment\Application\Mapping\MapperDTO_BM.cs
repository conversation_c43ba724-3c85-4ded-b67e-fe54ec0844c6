﻿using Application.Models.BMs;
using AutoMapper;
using Domain.Entities;
using Platform.Dyno.Payment.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Mapping
{
    public class MapperDTO_BM : Profile
    {
        public MapperDTO_BM() {
            CreateMap<WalletDTO, WalletBM>().ReverseMap();
            CreateMap<TransactionDTO, TransactionBM>().ReverseMap();
            CreateMap<UniqueQRCodeTransactionDTO, UniqueQRCodeTransactionBM>().ReverseMap();
            CreateMap<TransactionQrCodeDTO, TransactionQrCodeBM>().ReverseMap();
            CreateMap<WalletQrCodeDTO, WalletQrCodeBM>().ReverseMap();
            CreateMap<RefundTransactionDTO, RefundTransactionBM>().ReverseMap();
        }

    }
}
