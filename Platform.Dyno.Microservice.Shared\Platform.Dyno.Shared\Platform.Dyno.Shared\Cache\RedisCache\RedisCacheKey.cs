﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Cache.RedisCache
{
    public static class RedisCacheKey
    {
        #region AccessManagement

        #region User
        public static readonly string UserCacheKey = "Users";
        public static readonly string UserTokenCacheKey = "UserTokens";
        public static readonly string UserOTPCacheKey = "UserOTPs";
        public static readonly string BlacklistUserCacheKey = "BlacklistUsers";
        #endregion

        #region Role
        public static readonly string RoleCacheKey = "Roles";
        public static readonly string PermissionCacheKey = "Permissions";
        #endregion

        #region Language 
        public static readonly string LanguageCacheKey = "Languages";
        #endregion

        #region Company 
        public static readonly string CompanyCacheKey = "Companies";
        #endregion
        
        #region Group
        public static readonly string GroupCacheKey = "Groups";
        #endregion

        #region Employee
        public static readonly string EmployeeCacheKey = "Employees";

        #endregion

        #region Address
        public static readonly string AddressCacheKey = "Addresses";
        public static readonly string MacAddressCacheKey = "MacAddresses";
        #endregion

        #region Notification
        public static readonly string SubscriberDeviceCacheKey = "SubscriberDevices";
        #endregion

        #region Ticket
        public static readonly string TicketCacheKey = "Tickets";
        #endregion

        #region Sales Order
        public static readonly string SalesOrderCacheKey = "SalesOrder";
        public static readonly string FailedSalesOrderCacheKey = "FailedSalesOrder";
        public static readonly string SalesInvoiceCacheKey = "SalesInvoice";
        #endregion

        #region CashBack
        public static readonly string CashBackCacheKey = "CashBack";
        public static readonly string FailedCashBackCacheKey = "FailedCashBack";
        #endregion

        #region Reporting
        public static readonly string InvoiceCacheKey = "Invoice";
        public static readonly string PurchaseOrderCacheKey = "PurchaseOrder";
        #endregion

        #region Logger
        public static readonly string LogErrorCacheKey = "LogError";
        #endregion

        #endregion

        #region PaymentManagement

        #region Paiement Key
        public static readonly string WalletCacheKey = "Wallets";
        #endregion

        #endregion

        #region NotificationManagement
        public static readonly string NotificationCacheKey = "Notifications";
        #endregion

    }
}
