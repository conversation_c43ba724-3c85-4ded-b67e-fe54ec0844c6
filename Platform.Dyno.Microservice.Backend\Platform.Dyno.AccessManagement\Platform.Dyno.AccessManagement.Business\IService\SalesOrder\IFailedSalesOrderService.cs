﻿using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.SalesOrder
{
    public interface IFailedSalesOrderService
    {        
        public ResponseAPI<List<FailedSalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin);
        public ResponseAPI<PagedList<FailedSalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters);
        public ResponseAPI<FailedSalesOrderDTO> Create(Guid companyId, bool isSuperAdmin , FailedSalesOrderDTO salesOrderDTO, Guid creatorUserId, bool updateCache = true);
    
    }
}
