﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System.IdentityModel.Tokens.Jwt;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class AuthClientService : IAuthClientService
    {
        private readonly IUserService _userService;
        private readonly IRoleService _roleService;
        private readonly IMacAddressService _macAddressService;
        private readonly IUserTokenService _userTokenService;
        private readonly IUserOTPService _userOTPService;
        private readonly Configuration _configuration;

        private readonly IMapper _mapper;
        public AuthClientService(
            IUserService userService,
            IMacAddressService macAddressService,
            IUserTokenService userTokenService,
            IRoleService roleService,
            IUserOTPService userOTPService,
            Configuration configuration,
            IMapper mapper)
        {
            _userService = userService;
            _roleService = roleService;
            _macAddressService = macAddressService;
            _userTokenService = userTokenService;
            _userOTPService = userOTPService;
            _configuration = configuration;
            _mapper = mapper;
        }

        #region Check phoneNumber
        public ResponseAPI<bool> CheckPhone(string countryCode, string phoneNumber, string macAddress)
        {
            ResponseAPI<List<UserDTO>> users = _userService.Get(user =>
            user.PhoneNumber == phoneNumber &&
            user.CountryCode == countryCode &&
            user.UserType == UserType.Client &&
            user.Status == Status.Active);

            if (users.StatusCode != System.Net.HttpStatusCode.OK)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = users.StatusCode,
                    ExceptionMessage = users.ExceptionMessage,
                    ObjectValue = false
                };
            }

            if (users.ObjectValue != null && users.ObjectValue.Count > 1)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use the same phone number",
                    ObjectValue = false
                };
            }

            if (users.ObjectValue != null && users.ObjectValue.Count > 0)
            {
                //exist & !confirmed => otp interface
                if (!users.ObjectValue.FirstOrDefault().PhoneNumberConfirmed)
                {
                    ResponseAPI<List<MacAddressDTO>> macAddressExist = _macAddressService.Get(mac => mac.MacAddress == macAddress && mac.UserId == users.ObjectValue.FirstOrDefault()?.Id);
                    if (macAddressExist.ObjectValue == null || macAddressExist.ObjectValue?.Count == 0)
                    {
                        ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                        {
                            MacAddress = macAddress,
                            IsConfirmed = false,
                            UserId = users.ObjectValue.FirstOrDefault()?.Id
                        });
                    }

                    //Send Otp to confirme device with code value
                    ResponseAPI<UserOTPDTO> createCode = _userOTPService.SendOTP(new UserOTPDTO
                    {
                        UserId = users.ObjectValue?.FirstOrDefault()?.Id,
                        IsConfirmed = false
                    });

                    return new ResponseAPI<bool>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = $"The phone number exist but is not confirmed, The code is : {createCode.ObjectValue?.Code} " +
                        "please confime your phone number ! ",
                        ObjectValue = true
                    };
                }

                //exist & confirmed => login
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = "The phone number found successfully !",
                    ObjectValue = true
                };
            }

            //!exist => register interface
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.Forbidden,
                ObjectValue = false
            };
        }
        #endregion

        #region Login
        public ResponseAPI<AuthResponseDTO> Login(ClientLoginDTO loginDto, LanguageType language)
        {
            ResponseAPI<List<UserDTO>> userExist = _userService.Get(user =>
                                               user.PhoneNumber == loginDto.PhoneNumber &&
                                               user.CountryCode == loginDto.CountryCode &&
                                               user.Status == Status.Active &&
                                               user.UserType == UserType.Client);

            #region Test exception
            if (userExist.ObjectValue == null || userExist.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            if (userExist.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use this phoneNumber !"
                };
            }
            if (userExist.ObjectValue.FirstOrDefault()?.PhoneNumberConfirmed == false)
            {
                ResponseAPI<UserOTPDTO> createCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = userExist.ObjectValue.FirstOrDefault()?.Id,
                    IsConfirmed = false
                });

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Redirect,
                    ExceptionMessage = $"Account Not Confirmed: Please enter your OTP code : {createCode.ObjectValue?.Code}"
                };
            }
            #endregion

            UserBM user = _mapper.Map<UserBM>(userExist.ObjectValue?.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            var passwordHasher = new PasswordHasher<UserEntity>();
            bool isCorrectPassword = passwordHasher.VerifyHashedPassword(userEntity, userEntity.PasswordHash, loginDto.Password) == PasswordVerificationResult.Success ? true : false;

            if (isCorrectPassword)
            {
                ResponseAPI<UserTokenDTO> userToken = _userTokenService.Create(new UserTokenDTO
                {
                    Token = AuthManagement.CreateToken(userExist.ObjectValue?.FirstOrDefault(), null, null,
                    _configuration.Key, _configuration.TokenLifeTime, _configuration.Issuer),
                    RefreshToken = AuthManagement.GenerateRefreshToken(),
                    ExpiredDate = DateTime.Now.ToUniversalTime().AddDays(Convert.ToDouble(_configuration.RefreshTokenLifeTime))

                });

                #region MacAddress Verification

                List<MacAddressDTO>? userMacAddresses = userExist.ObjectValue?.FirstOrDefault()?.MacAddresses?.ToList();

                int? code = 0;
                (bool, int?) verificationResponse = VerificationMacAddress(userMacAddresses, loginDto.MacAddress, user.Id);
                code = verificationResponse.Item2;
                if (verificationResponse.Item1)
                {
                    return new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.OK,
                        ObjectValue = new AuthResponseDTO
                        {
                            Token = userToken.ObjectValue?.Token,
                            RefreshToken = userToken.ObjectValue?.RefreshToken,
                            ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                            UserProfile = new UserProfileDTO
                            {
                                FullName = user.FullName,
                                Email = user.Email,
                                DateOfBirth = user.DateOfBirth,
                                PhoneNumber = user.PhoneNumber,
                                CountryCode = user.CountryCode,
                                Gender= user.Gender,
                                Picture = user.Picture
                            }
                        }
                    };
                }
                return new ResponseAPI<AuthResponseDTO>
                {

                    StatusCode = System.Net.HttpStatusCode.Redirect,
                    ExceptionMessage = "You are connecting with new device : Please check your phone number for a " +
                    "confirmation code and follow the instructions to confirm your account " +
                    $"before logging in. code : {code}",
                    ObjectValue = new AuthResponseDTO
                    {
                        Token = userToken.ObjectValue?.Token,
                        RefreshToken = userToken.ObjectValue?.RefreshToken,
                        ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                        UserProfile = new UserProfileDTO
                        {
                            FullName = user.FullName,
                            Email = user.Email,
                            DateOfBirth = user.DateOfBirth,
                            PhoneNumber = user.PhoneNumber,
                            Picture = user.Picture
                        }
                    }
                };

                #endregion
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "Incorrect Credentials: Please try again."
            };
        }

        public ResponseAPI<AuthResponseDTO> BiometricLogin(Guid id, string password)
        {
            ResponseAPI<UserDTO> userExist = _userService.Get(id);

            #region Test exception
            if (userExist.ObjectValue == null || userExist.ObjectValue.UserType != UserType.Client)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            #endregion

            UserBM user = _mapper.Map<UserBM>(userExist.ObjectValue);
            UserEntity userEntity = _mapper.Map<UserEntity>(user);
            var passwordHasher = new PasswordHasher<UserEntity>();
            bool isCorrectPassword = passwordHasher.VerifyHashedPassword(userEntity, userEntity.PasswordHash, password) == PasswordVerificationResult.Success ? true : false;

            if (isCorrectPassword)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User is Unauthorized"
            };
        }
        #endregion

        #region Register
        public async Task <ResponseAPI<AuthResponseDTO>> Register(ClientRegisterDTO registerDto, Guid? createdUserId, Guid? companyId)
        {
            RoleDTO? clientRole = _roleService.Get(role => role.Name == DefaultRoles.Client).ObjectValue?.FirstOrDefault();

            if (clientRole == null)
            {
                ResponseAPI<RoleDTO> createClientRole = _roleService.Create(new RoleDTO
                {
                    Name = DefaultRoles.Client
                });

                if (createClientRole != null && createClientRole.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    clientRole = createClientRole.ObjectValue;
                };

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = createClientRole.ExceptionMessage
                };
            }

            clientRole.Users = null;

            ResponseAPI<List<UserDTO>> responseUser =
               _userService.Get(user => user.PhoneNumber == registerDto.PhoneNumber 
               && user.CountryCode == registerDto.CountryCode
               && user.UserType == UserType.Client 
               && user.Status != Status.Deleted);

            if (responseUser.ObjectValue != null && responseUser.ObjectValue.Count > 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Found,
                    ExceptionMessage = "This Phone number is already used by another account"
                };
            }

            UserDTO userDTO = _mapper.Map<UserDTO>(registerDto);
            userDTO.Roles.Add(clientRole);

            userDTO.UserType = UserType.Client;
            ResponseAPI<UserDTO> createdUser = _userService.Create(userDTO);
            ResponseAPI<UserOTPDTO> createCode = new ResponseAPI<UserOTPDTO>();

            if (createdUser.StatusCode == System.Net.HttpStatusCode.Created)
            {
                ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                {
                    MacAddress = registerDto.MacAddress ?? "",
                    IsConfirmed = false,
                    UserId = createdUser.ObjectValue?.Id
                });
                createCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = createdUser.ObjectValue?.Id,
                    IsConfirmed = false
                });
            }
            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = createdUser.StatusCode,
                ExceptionMessage = createdUser.ExceptionMessage + $"{createCode.ObjectValue?.Code}",
                ObjectValue = new AuthResponseDTO
                {
                    UserProfile = new UserProfileDTO
                    {
                        Id = userDTO.Id,
                        FullName = userDTO.FullName,
                        Gender = userDTO.Gender,
                        Email = userDTO.Email,
                        PhoneNumber = userDTO.PhoneNumber,
                        Picture = userDTO.Picture,
                        DateOfBirth = userDTO.DateOfBirth,
                    }
                }
            };
        }

        #endregion

        #region Logout
        public ResponseAPI<AuthResponseDTO> LogOut(Guid userId, string accessToken)
        {
            ResponseAPI<List<UserTokenDTO>> tokens = _userTokenService.Get(token => token.Token == accessToken);

            if (tokens.StatusCode == System.Net.HttpStatusCode.OK
                && tokens.ObjectValue != null &&
                tokens.ObjectValue.Count > 0)
            {
                foreach (var token in tokens.ObjectValue)
                {
                    ResponseAPI<UserTokenDTO> deleteToken = _userTokenService.Remove(token.Id);
                    if (deleteToken.StatusCode != System.Net.HttpStatusCode.OK)
                    {
                        return new ResponseAPI<AuthResponseDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Conflict,
                            ExceptionMessage = "User has the same token more than one time : " + deleteToken.ExceptionMessage
                        };
                    }

                }

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = "User logout successfully !"
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "Token unvalid!"
            };

        }
        #endregion

        #region Refresh Token
        public ResponseAPI<AuthResponseDTO> GetRefreshToken(UserTokenDTO token)
        {
            #region Test RefreshToken &&  Token
            UserTokenDTO? userToken = _userTokenService.Get(token =>
            token.Token == token.Token &&
            token.RefreshToken == token.RefreshToken &&
            token.UserId == token.UserId).ObjectValue?.FirstOrDefault();

            if (userToken == null)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Invalid access token or refresh token"
                };
            }

            if (userToken.ExpiredDate < DateTime.UtcNow)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "The refresh token is expired"
                };
            }

            #endregion

            var principal = AuthManagement.GetPrincipalFromExpiredToken(token.Token,
                _configuration.Issuer, _configuration.Key);

            if (principal == null)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Invalid access token or refresh token"
                };
            }

            SigningCredentials signingCredentials = AuthManagement.GetSigningCredentials(_configuration.Key);
            JwtSecurityToken tokenOptions = AuthManagement.GenerateTokenOption(signingCredentials, principal.Claims.ToList(),
                _configuration.TokenLifeTime, _configuration.Issuer);
            string newToken = new JwtSecurityTokenHandler().WriteToken(tokenOptions);
            userToken.Token = newToken;

            ResponseAPI<UserTokenDTO> newUserToken = _userTokenService.Update(userToken);
            if (newUserToken.StatusCode == System.Net.HttpStatusCode.Created)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = new AuthResponseDTO
                    {
                        Token = newToken,
                        RefreshToken = token.RefreshToken,
                        ExpiredDate = token.ExpiredDate
                    }
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = newUserToken.StatusCode,
                ExceptionMessage = newUserToken.ExceptionMessage
            };
        }
        #endregion

        #region Forget Password
        public async Task<ResponseAPI<AuthResponseDTO>> ForgetPassword(string phoneNumber, string? countryCode = null)
        {
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user => user.PhoneNumber == phoneNumber &&
            user.CountryCode == countryCode &&
            user.PhoneNumberConfirmed == true && 
            user.UserType == UserType.Client &&
            user.Status == Status.Active);

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorize"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use the same phone Number," +
                    " please contact our system!"
                };
            }
            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            ResponseAPI<UserOTPDTO> createCode = _userOTPService.SendOTP(new UserOTPDTO
            {
                UserId = user.Id,
                IsConfirmed = false
            });

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = createCode.StatusCode,
                ExceptionMessage = createCode.ExceptionMessage = $"The code is : {createCode.ObjectValue?.Code}",
            };

        }

        public ResponseAPI<AuthResponseDTO> ResetPassword(ResetPasswordClientDTO resetPassword)
        {
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user =>
                user.PhoneNumber == resetPassword.PhoneNumber &&
                user.CountryCode == resetPassword.CountryCode &&
                user.PhoneNumberConfirmed == true &&
                user.UserType == UserType.Client);

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than one user with the same phone number, please contact our system!"
                };
            }

            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);
            var passwordHasher = new PasswordHasher<UserEntity>();
            userEntity.PasswordHash = passwordHasher.HashPassword(userEntity, resetPassword.NewPassword);

            UserBM updateUserBM = _mapper.Map<UserBM>(userEntity);
            UserDTO updateUserDTO = _mapper.Map<UserDTO>(updateUserBM);
            updateUserDTO.Roles = responseUser.ObjectValue.FirstOrDefault()?.Roles ?? new List<RoleDTO>();
            ResponseAPI<UserDTO> updateUser = _userService.Update(updateUserDTO);

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = updateUser.StatusCode,
                ExceptionMessage = updateUser.ExceptionMessage,
            };
        }

        public ResponseAPI<AuthResponseDTO> UpdatePassword(UpdatePasswordDTO passwordDTO)
        {
            if(passwordDTO.OldPassword == passwordDTO.NewPassword)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Please change the password from the old one."
                };
            }
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user =>
                user.Id == passwordDTO.UserId && user.PhoneNumberConfirmed == true &&
                user.UserType == UserType.Client);

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than one user with the same phone number, please contact our system!"
                };
            }

            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);
            var passwordHasher = new PasswordHasher<UserEntity>();
            bool isCorrectPassword = passwordHasher.VerifyHashedPassword(userEntity, userEntity.PasswordHash, passwordDTO.OldPassword) == PasswordVerificationResult.Success ? true : false;

            if(isCorrectPassword)
            {
                userEntity.PasswordHash = passwordHasher.HashPassword(userEntity, passwordDTO.NewPassword);

                UserBM updateUserBM = _mapper.Map<UserBM>(userEntity);
                UserDTO updateUserDTO = _mapper.Map<UserDTO>(updateUserBM);
                updateUserDTO.Roles = responseUser.ObjectValue.FirstOrDefault().Roles;
                ResponseAPI<UserDTO> updateUser = _userService.Update(updateUserDTO);

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = updateUser.StatusCode,
                    ExceptionMessage = updateUser.ExceptionMessage,
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized"
            };
        }
        #endregion

        #region Confirme Phone Number
        public ResponseAPI<AuthResponseDTO> Confirme(string countryCode, string phoneNumber, int code)
        {
            ResponseAPI<List<UserDTO>> response = _userService.Get(user =>
                                               user.PhoneNumber == phoneNumber &&
                                               user.CountryCode == countryCode &&
                                               user.Status == Status.Active &&
                                               (user.UserType == UserType.Client ||user.UserType == UserType.Cashier));

            #region Test exception
            if (response.ObjectValue == null || response.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            if (response.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use this phoneNumber !"
                };
            }
            #endregion

            ResponseAPI<UserOTPDTO> verifierCode = _userOTPService.VerifierCode(new UserOTPDTO
            {
                UserId = response.ObjectValue?.FirstOrDefault().Id ?? Guid.NewGuid(),
                IsConfirmed = true,
                Code = code

            });


            if (verifierCode.StatusCode == System.Net.HttpStatusCode.OK)
            {
                if (response.ObjectValue?.FirstOrDefault() != null)
                {
                    response.ObjectValue.FirstOrDefault().PhoneNumberConfirmed = true;
                    string url = $"{_configuration.AWSS3URL}/Images/";
                    int? index = response.ObjectValue.FirstOrDefault()?.Picture?.IndexOf(url);
                    if (index != null && index != -1)
                    {
                        response.ObjectValue.FirstOrDefault().Picture = response.ObjectValue.FirstOrDefault()?.Picture?.Substring((int)(index + url.Length));
                    };
                    ResponseAPI<UserDTO> confirmePhoneNumber = _userService.Update(response.ObjectValue.FirstOrDefault());
                };
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = new AuthResponseDTO
                    {
                        UserProfile = new UserProfileDTO
                        {
                            Id = response.ObjectValue?.FirstOrDefault()?.Id ?? Guid.NewGuid(),
                        }
                    },
                    ExceptionMessage = "Phone number confirmed successfully !"
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = verifierCode.ExceptionMessage
            };
        }

        public ResponseAPI<AuthResponseDTO> VerifyCode(ClientOtpDTO clientOtp)
        {
            ResponseAPI<List<UserDTO>> user = _userService.Get(user =>
                                               user.PhoneNumber == clientOtp.PhoneNumber &&
                                               user.CountryCode == clientOtp.CountryCode &&
                                               user.Status == Status.Active &&
                                               user.UserType == UserType.Client);

            #region Test exception
            if (user.ObjectValue == null || user.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            if (user.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use this phoneNumber !"
                };
            }
            #endregion

            ResponseAPI<UserOTPDTO> verifyOTP = _userOTPService.VerifierCode(new UserOTPDTO
            {
                UserId = user.ObjectValue?.FirstOrDefault()?.Id,
                Code = clientOtp.Code
            });

            if (verifyOTP.StatusCode != System.Net.HttpStatusCode.OK)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = verifyOTP.StatusCode,
                    ExceptionMessage = "OTP verification failed"
                };
            }


            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ExceptionMessage = "OTP verification successfully"
            };
        }
        #endregion

        #region Private methode
        private (bool, int?) VerificationMacAddress(List<MacAddressDTO>? macAddresses, string userMacAddress, Guid userId)
        {
            bool macAddressExist = false;
            bool macAddressConfirmed = false;
            
            if(macAddresses != null && macAddresses.Count > 0)
            {
                foreach (var macAdress in macAddresses)
                {
                    if (macAdress.MacAddress == userMacAddress && macAdress.IsConfirmed == true)
                    {
                        macAddressConfirmed = true;
                    }
                    if (macAdress.MacAddress == userMacAddress)
                        macAddressExist = true;
                }
            }
            

            if (!macAddressExist)
            {
                ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                {
                    MacAddress = userMacAddress,
                    IsConfirmed = false,
                    UserId = userId
                });

            }

            ResponseAPI<UserOTPDTO> sendOTPCode = new ResponseAPI<UserOTPDTO>();
            if (!macAddressConfirmed)
            {
                sendOTPCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = userId,
                    IsConfirmed = false
                });
            }

            return (macAddressConfirmed, sendOTPCode.ObjectValue?.Code);
        }

        #endregion

    }

}
