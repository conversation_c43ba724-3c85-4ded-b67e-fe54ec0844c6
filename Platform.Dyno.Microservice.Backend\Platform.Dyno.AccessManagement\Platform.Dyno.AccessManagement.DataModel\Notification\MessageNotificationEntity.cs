﻿using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Notification
{
    public class MessageNotificationEntity : ReferentialData
    {
        public Guid Id { get; set; }
        public string Topic { get; set; } = string.Empty;
        public string TopicArn { get; set; } = string.Empty;     
        public string MessageGroupId { get; set; } = string.Empty;
        public string Protocol { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Rule { get; set; } = string.Empty;
        public List<SubscriberDeviceEntity> Subscribers { get; set; } = new List<SubscriberDeviceEntity>();
    }
}
