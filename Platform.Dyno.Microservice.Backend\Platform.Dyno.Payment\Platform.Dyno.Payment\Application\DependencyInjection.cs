﻿using Application.IServices;
using Application.Mapping;
using Application.Services;
using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.QrCode;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddServices(this IServiceCollection services)
        {

            services.AddScoped<IWalletService, WalletService>();
            services.AddScoped<IWalletBlockchainService, WalletBlockchainService>();
            services.AddScoped<ITransactionService, TransactionService>();
            services.AddScoped<IRefundTransactionService, RefundTransactionService>();
            services.AddScoped<ITransactionBlockchainService, TransactionBlockchainService>();
            services.AddScoped<IQrCodeService, QrCodeService>();

            return services;

        }

        public static void ScoppedHelper(this IServiceCollection services)
        {
            #region Wallet
            services.AddScoped<IHelper<WalletDTO>, Helper<WalletDTO>>();
            services.AddScoped<IHelper<List<WalletDTO>>, Helper<List<WalletDTO>>>();
            #endregion

            #region Transaction
            services.AddScoped<IHelper<TransactionDTO>, Helper<TransactionDTO>>();
            services.AddScoped<IHelper<TransactionGroupBlockchain>, Helper<TransactionGroupBlockchain>>();
            services.AddScoped<IHelper<TransactionBlockchain>, Helper<TransactionBlockchain>>();
            #endregion


        }
    }
}
