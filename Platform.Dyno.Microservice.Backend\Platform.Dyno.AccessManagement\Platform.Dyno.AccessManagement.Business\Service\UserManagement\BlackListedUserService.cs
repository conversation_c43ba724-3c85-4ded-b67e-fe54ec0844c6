﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class BlackListedUserService : IBlackListedUserService
    {
        private readonly IUnitOfWork<BlackListedUserEntity> _blacklistUserRepository;
        private readonly IUserService _userService;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _blacklistUserCacheKey = RedisCacheKey.BlacklistUserCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("BlackList User");

        public BlackListedUserService(IUnitOfWork<BlackListedUserEntity> blacklistUserRepository,
            IMapper mapper, 
            IRedisCacheService cache,
            IUserService userService)
        {
            _blacklistUserRepository = blacklistUserRepository;
            _mapper = mapper;
            _cache = cache;
            _userService = userService;
        }

        #region Get

        public ResponseAPI<List<BlackListedUserDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if (userType == UserType.Client || userType == UserType.Cashier)
            {
                return new ResponseAPI<List<BlackListedUserDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            List<BlackListedUserDTO>? usersDTO = _cache.GetData<List<BlackListedUserDTO>>(_blacklistUserCacheKey);
            if (usersDTO == null || usersDTO.Count == 0)
            {
                List<BlackListedUserEntity> userEntities = (List<BlackListedUserEntity>)_blacklistUserRepository.Repository.GetAll(includes: new List<string> { "User" }, orderBy: user => user.OrderByDescending(user => user.BlockedDate));
                List<BlackListedUserBM> users = _mapper.Map<List<BlackListedUserBM>>(userEntities);
                usersDTO = _mapper.Map<List<BlackListedUserDTO>>(users);
                _cache.SetData(_blacklistUserCacheKey, usersDTO, DateTimeOffset.UtcNow.AddDays(1));

            }
            if (userType == UserType.Company || userType == UserType.ShopOwner)
            {
                usersDTO = usersDTO.Where(user => user.User?.CompanyId == companyId).ToList();
            }
            return new ResponseAPI<List<BlackListedUserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = usersDTO
            }; ;
        }

        public ResponseAPI<PagedList<BlackListedUserDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<BlackListedUserDTO>> usersDTO = GetAll();
            PagedList<BlackListedUserDTO>? pagedList = null;
            if (usersDTO.ObjectValue != null)
            {
                pagedList = PagedList<BlackListedUserDTO>.ToGenericPagedList(usersDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<BlackListedUserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<BlackListedUserDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<BlackListedUserDTO>> usersDTO = GetAll();
            PagedList<BlackListedUserDTO>? pagedList = null;
            if (usersDTO.ObjectValue != null)
            {
                pagedList = PagedList<BlackListedUserDTO>.ToGenericPagedList(usersDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<BlackListedUserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }
        public ResponseAPI<BlackListedUserDTO> Get(Guid id)
        {
            BlackListedUserDTO? userDTO = GetAll().ObjectValue?.Where(user => user.Id == id).FirstOrDefault();
            return new ResponseAPI<BlackListedUserDTO>()
            {
                StatusCode = userDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = userDTO == null ? $"User OTP with id {id} Not Found !" : null,
                ObjectValue = userDTO
            };
        }

        public ResponseAPI<List<BlackListedUserDTO>> Get(Func<BlackListedUserDTO, bool> expression)
        {
            List<BlackListedUserDTO>? userDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<BlackListedUserDTO>>()
            {
                StatusCode = userDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ObjectValue = userDTO
            };
        }

        
        #endregion

        #region Create
        public ResponseAPI<BlackListedUserDTO> Create(BlackListedUserDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            BlackListedUserBM blackListedUser = _mapper.Map<BlackListedUserBM>(dtoObject);

            #region changed data
            blackListedUser.Id = Guid.NewGuid();
            blackListedUser.BlockedUserId = creatorUserId ?? dtoObject.BlockedUserId;
            blackListedUser.BlockedDate = DateTime.UtcNow;

            #endregion

            BlackListedUserEntity blackListedUserEntity = _mapper.Map<BlackListedUserEntity>(blackListedUser);

            _blacklistUserRepository.Repository.Insert(blackListedUserEntity);
            _blacklistUserRepository.Save();

            dtoObject.Id = blackListedUserEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_blacklistUserCacheKey);
            }

            return new ResponseAPI<BlackListedUserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public ResponseAPI<BlackListedUserDTO> BlockedUser(BlackListedUserDTO dtoobject, Guid companyId, UserType userType, Guid blockedUserId)
        {
            if(userType == UserType.Cashier || userType == UserType.Client)
            {
                return new ResponseAPI<BlackListedUserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = $"User unauthorize !"
                };
            }
            UserDTO? user = _userService.Get(dtoobject.UserId).ObjectValue;
            if(user != null) 
            { 
                if(userType != UserType.SuperAdmin && user.CompanyId != companyId)
                {
                    return new ResponseAPI<BlackListedUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = $"User unauthorize to block this user !"
                    };
                }

                ResponseAPI<BlackListedUserDTO> response = Create(dtoobject, creatorUserId: blockedUserId);
                return response;
            }
            return new ResponseAPI<BlackListedUserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {dtoobject.UserId} Not Found !"
            };
        }

        public ResponseAPI<BlackListedUserDTO> UnBlockedUser(BlackListedUserDTO dtoObject, Guid companyId, UserType userType, Guid unblockedUserId)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region Update
        public ResponseAPI<BlackListedUserDTO> Update(BlackListedUserDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (Get(dtoObject.Id).ObjectValue != null)
            {
                BlackListedUserBM blackListedUser = _mapper.Map<BlackListedUserBM>(dtoObject);

                BlackListedUserEntity blackListedUserEntity = _mapper.Map<BlackListedUserEntity>(blackListedUser);

                _blacklistUserRepository.Repository.Insert(blackListedUserEntity);
                _blacklistUserRepository.Save();

                dtoObject.Id = blackListedUserEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_blacklistUserCacheKey);
                }

                return new ResponseAPI<BlackListedUserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<BlackListedUserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {dtoObject.Id} Not Found !"
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<BlackListedUserDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            throw new NotImplementedException();
        }
        #endregion

    }
}
