﻿using Application.Interfaces;
using Application.IServices;
using Application.Models.BMs;
using Application.Security;
using AutoMapper;
using Domain.Entities;
using Domain.Enums.Query;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Services
{
    public class WalletBlockchainService : IWalletBlockchainService
    {
        private readonly IHelper<WalletDTO> _walletHelper;
        private readonly IHelper<List<WalletDTO>> _walletsHelper;
        private readonly IGenericRepository _Repository;
        private readonly IMapper _mapper;
        private readonly Configuration _configuration;
        private readonly IRedisCacheService _cache;
        private readonly string _walletCacheKey = RedisCacheKey.WalletCacheKey;
        ExceptionMessages exceptionMessage = new ExceptionMessages("wallet");

        public WalletBlockchainService(
            IHelper<WalletDTO> walletHelper,
            IHelper<List<WalletDTO>> walletsHelper,
            IGenericRepository Repository, 
            IMapper mapper,
            Configuration configuration,
            IRedisCacheService cache)
        {
            _walletHelper = walletHelper;
            _walletsHelper = walletsHelper;
            _Repository = Repository;
            _mapper = mapper;
            _configuration = configuration;
            _cache = cache;
        }

        
        #region get
        public ResponseAPI<List<WalletDTO>> GetAll()
        {
            List<WalletDTO>? walletsDTO = _cache.GetData<List<WalletDTO>>(_walletCacheKey);
            if(walletsDTO == null || walletsDTO.Count == 0) 
            {
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetAllWallets";
                ResponseAPI<List<WalletDTO>>? blockchainResponse = _walletHelper.GetAll(endPoint);
                if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
                {
                    walletsDTO = blockchainResponse.ObjectValue;
                }

            }                    
            ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<WalletDTO>> GetAll(PagedParameters pagedParameters)
        {
            ResponseAPI<List<WalletDTO>>? walletsDTO = GetAll();
            PagedList<WalletDTO>? pagedList = null;
            if(walletsDTO.ObjectValue != null)
            {
                pagedList = PagedList<WalletDTO>.ToGenericPagedList(walletsDTO.ObjectValue, pagedParameters);
            }

            return new ResponseAPI<PagedList<WalletDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }
        public ResponseAPI<WalletDTO> Get(Guid id)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetWallet";
            ResponseAPI<WalletDTO>? blockchainResponse = _walletHelper.Get(endPoint, id);
            if (blockchainResponse != null)
                return blockchainResponse;

            return new ResponseAPI<WalletDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessage.ReadError
            };

        }
        public ResponseAPI<List<WalletDTO>> GetUserWallets(Guid userId)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/getWalletsByUserId/{userId}";
            ResponseAPI<List<WalletDTO>>? blockchainResponse = _walletHelper.GetAll(endPoint);
            List<WalletDTO> walletsDtO = new List<WalletDTO>();
            if (blockchainResponse != null && blockchainResponse.ObjectValue?.Count > 0)
            {
                walletsDtO = blockchainResponse.ObjectValue;
            }

            ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }
        public ResponseAPI<WalletDTO> GetUserWalletByType(Guid userId, WalletType walletType)
        {
            string endPoint = $"{_configuration.PaymentBlockchainAddress}/GetUserWalletByType/{userId}/{walletType}";
            ResponseAPI<WalletDTO>? blockchainResponse = _walletHelper.Get(endPoint);
            if (blockchainResponse != null)
                return blockchainResponse;

            return new ResponseAPI<WalletDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Wallet of user : {userId} with type : {walletType} not found !"
            };
        }
        #endregion

        #region create

        public ResponseAPI<WalletDTO> Create(WalletDTO walletDTO, Guid UserId, string pinCode)
        {
            if (walletDTO != null)
            {
                Keys keys;
                if (walletDTO.AssignedToType == UserType.ShopOwner || walletDTO.AssignedToType==UserType.Company|| walletDTO.AssignedToType==UserType.SuperAdmin)
                {
                    keys = KeyManager.GenerateSecuredKeys(walletDTO.AssignedToId.ToString());
                }
                else
                {
                    keys = KeyManager.GenerateSecuredKeys(pinCode);
                }
                
                if (keys == null)
                {
                    return new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "wallet is not created: error while generating wallet keys",
                    };
                }
                walletDTO.Id = Guid.NewGuid();
                walletDTO.PrivateKey = keys.HashedPrivateKey;
                walletDTO.PublicKey = keys.PublicKey;
                //Create wallet in Blockchain 
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/CreateWallet";
                ResponseAPI<WalletDTO>? blockchainResponse = _walletHelper.Post(endPoint, walletDTO);
                if(blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = blockchainResponse?.ExceptionMessage
                    };
                }
                var query = @"
                    CREATE (w:Wallet {
                        Id: $walletId,
                        PrivateKey:$PrivateKey,
                        PublicKey:$PublicKey,
                        Balance: $balance,
                        WalletType:$WalletType,
                        AssignedToType: $AssignedToType,
                        AssignedToId: $AssignedToId,
                        Status: $Status
                    })
                    RETURN w as w;";
                WalletBM walletBM = _mapper.Map<WalletBM>(walletDTO);   
                
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "walletId",walletBM.Id.ToString()  },
                    { "PrivateKey",keys.EncryptedPrivateKey  },
                    { "PublicKey",keys.PublicKey  },
                    { "balance", walletBM.Balance },
                    { "WalletType", walletBM.WalletType.ToString()},
                    { "AssignedToType", walletBM.AssignedToType.ToString() },
                    { "AssignedToId", walletBM.AssignedToId.ToString() },
                    { "Status", walletBM.Status.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<INode>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not created",
                };
                if (result != null)
                {
                    var walletEntity = _mapper.Map<WalletEntity>(result);
                    var walletsBM = _mapper.Map<WalletBM>(walletEntity);
                    walletDTO = _mapper.Map<WalletDTO>(walletsBM);
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }

                //Delete wallet from blockchain
                string endPointDelete = $"{_configuration.PaymentBlockchainAddress}/deleteWallet";
                ResponseAPI<WalletDTO>? blockchainDeleteResponse = _walletHelper.Delete(endPointDelete, walletBM.Id);
                return response;
            }
            else
            {
                return new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet must not be null"
                };
            }
        }
        public ResponseAPI<List<WalletDTO>> CreateDefaultWallets(WalletDTO walletDTO, Guid CreateUserId, string pinCode)
        {
            if (walletDTO != null)
            {
                var walletBM = _mapper.Map<WalletBM>(walletDTO);
                var wallets = new List<WalletBM>();  
                var blockchainWallets = new List<WalletBM>();

                for (int i = 0; i < 4; i++)
                {
                    Keys keys;
                    if (walletDTO.AssignedToType == UserType.ShopOwner || walletDTO.AssignedToType == UserType.Company || walletDTO.AssignedToType == UserType.SuperAdmin)
                    {
                        keys = KeyManager.GenerateSecuredKeys(walletDTO.AssignedToId.ToString());
                    }
                    else
                    {
                        keys = KeyManager.GenerateSecuredKeys(pinCode);
                    }
                    if (keys == null)
                    {
                        return new ResponseAPI<List<WalletDTO>>
                        {
                            StatusCode = System.Net.HttpStatusCode.BadRequest,
                            ExceptionMessage = "wallet is not created: error while generating wallet keys",
                        };
                    }
                    
                    var walletBm = new WalletBM();
                    walletBm.Id = Guid.NewGuid();
                    walletBm.AssignedToId = walletBM.AssignedToId;
                    walletBm.AssignedToType = walletBM.AssignedToType;
                    walletBm.PrivateKey = keys.EncryptedPrivateKey;
                    walletBm.PublicKey = keys.PublicKey;
                    walletBm.WalletType = (WalletType)i;
                    walletBm.Status = (WalletStatus)0;
                    wallets.Add(walletBm);

                    var walletBmBlockchain = new WalletBM();
                    walletBmBlockchain.Id = walletBm.Id;
                    walletBmBlockchain.AssignedToId = walletBm.AssignedToId;
                    walletBmBlockchain.AssignedToType = walletBm.AssignedToType;
                    walletBmBlockchain.PrivateKey = keys.HashedPrivateKey;
                    walletBmBlockchain.PublicKey = walletBm.PublicKey;
                    walletBmBlockchain.WalletType = walletBm.WalletType;
                    walletBmBlockchain.Status = walletBm.Status;
                    blockchainWallets.Add(walletBmBlockchain);
                }

                List<WalletDTO> walletsDto = _mapper.Map<List<WalletDTO>>(wallets);
                
                List<WalletDTO> bloackchainWalletsDto = _mapper.Map<List<WalletDTO>>(blockchainWallets);

                //Create Defaults wallets in blockchain 
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/CreateDefaultWallets";
                ResponseAPI<List<WalletDTO>>? blockchainResponse = _walletsHelper.Post(endPoint, bloackchainWalletsDto);
                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = blockchainResponse?.ExceptionMessage
                    };
                }

                var query = @"UNWIND $wallets AS walletData
                    CREATE (w:Wallet {
                        Id: walletData.walletId,
                        PrivateKey:walletData.PrivateKey,
                        PublicKey:walletData.PublicKey,
                        Balance: walletData.balance,
                        WalletType:walletData.WalletType,
                        AssignedToType: walletData.AssignedToType,
                        AssignedToId: walletData.AssignedToId,
                        Percentage: walletData.Percentage,
                        IsDefault: walletData.IsDefault,
                        Status: walletData.Status
                    })
                    RETURN w AS w;";

                var parameters = new Dictionary<string, object>
{
                    { "wallets", wallets.Select(wallet =>  new  Dictionary<string, object> {
                                    { "walletId",wallet.Id.ToString()  },
                                    { "PrivateKey",wallet.PrivateKey  },
                                    { "PublicKey",wallet.PublicKey  },
                                    { "balance", wallet.Balance },
                                    { "WalletType", wallet.WalletType.ToString()},
                                    { "AssignedToType", wallet.AssignedToType.ToString() },
                                    { "AssignedToId", wallet.AssignedToId.ToString() },
                                    { "Status", wallet.Status.ToString() }
                                })
                    }
                };
                var result = _Repository.ExecuteWriteRangeTransactionAsync<INode>(query, "w", parameters).Result;
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not created",
                };
                if (result != null)
                {
                    List<WalletDTO> walletsDtO = result.Select(w =>
                    {
                        var walletsEntity = _mapper.Map<WalletEntity>(w);
                        var walletsBM = _mapper.Map<WalletBM>(walletsEntity);
                        return _mapper.Map<WalletDTO>(walletsBM);
                    }).ToList();
                    response = new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletsDtO,
                    };
                    return response;
                }

                //Delete All wallets for user Id 
                return response;
            }
            else
            {
                return new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet must not be null"
                };
            }

        }
        
        #endregion

        #region update
        public ResponseAPI<WalletDTO> Update(WalletDTO walletDTO, Guid UserId)
        {
            if (walletDTO != null)
            {
                ResponseAPI<WalletDTO> walletExist = Get(walletDTO.Id);
                if(walletExist.StatusCode != System.Net.HttpStatusCode.OK || walletExist.ObjectValue==null)
                {
                    return new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"wallet with id : {walletDTO.Id}"
                    };
                }

                walletExist.ObjectValue.Status = walletDTO.Status;
                string endPoint = $"{_configuration.PaymentBlockchainAddress}/updateWallet";
                ResponseAPI<WalletDTO>? blockchainResponse = _walletHelper.Put(endPoint, walletDTO);
                if (blockchainResponse == null || blockchainResponse?.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = $"An error occurred while updating the wallet with the ID : {walletDTO.Id}"
                    };
                }
                var walletBM = _mapper.Map<WalletBM>(walletDTO);
                var query = @$"
                    MATCH (w:Wallet {{Id: $WalletId}})
                    SET w.Status = $Status 
                    RETURN w;";
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "WalletId",walletBM.Id.ToString()  },
                    { "Status", walletBM.Status.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<INode>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not updated",
                };
                if (result != null)
                {
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                return new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet must not be null"
                };
            }
        }
        #endregion

        #region Delete
        public ResponseAPI<WalletDTO> Delete(Guid id, Guid DeleteUserId)
        {

            //Delete wallet from blockchain
            string endPointDelete = $"{_configuration.PaymentBlockchainAddress}/deleteWallet";
            ResponseAPI<WalletDTO>? blockchainDeleteResponse = _walletHelper.Delete(endPointDelete, id);

            if(blockchainDeleteResponse != null && blockchainDeleteResponse.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var query = @$"
                MATCH (w:Wallet {{Id: $WalletId}})
                SET w.Status = $Status                       
                RETURN w;";
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "WalletId",id.ToString()  },
                    { "Status", WalletStatus.Deleted.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<INode>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not deleted",
                };
                if (result != null)
                {
                    WalletEntity walletEntity = _mapper.Map<WalletEntity>(result.Properties);
                    WalletBM walletBM = _mapper.Map<WalletBM>(walletEntity);
                    WalletDTO walletDTO = _mapper.Map<WalletDTO>(walletBM);
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.NoContent,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }
                return response;
            }

            return blockchainDeleteResponse ?? new ResponseAPI<WalletDTO> 
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = $"An error occurred while deleting the wallet with the ID : {id}"
            };
        }
        #endregion
    }
}
