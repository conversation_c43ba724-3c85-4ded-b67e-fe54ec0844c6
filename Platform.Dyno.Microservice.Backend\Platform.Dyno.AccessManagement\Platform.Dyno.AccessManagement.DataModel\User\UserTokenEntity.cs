﻿using Microsoft.AspNetCore.Identity;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.User
{
    public class UserTokenEntity : ReferentialData
    {
        #region Data
        [Required]
        public Guid Id { get; set; }
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiredDate { get; set; }
        #endregion

        #region Structure
        [ForeignKey("UserId")]
        public UserEntity? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
