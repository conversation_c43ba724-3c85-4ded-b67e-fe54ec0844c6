﻿using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register
{
    public class ClientRegisterDTO
    {
        #region User Data
        [Required(ErrorMessage = "UserName is required")]
        public string? FullName { get; set; }
        public Gender? Gender { get; set; }
        public string Password { get; set; } = string.Empty;

        public string CountryCode { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;
        [Required(ErrorMessage = "Date of birth is required")]
        [DataType(DataType.Date, ErrorMessage = "Date of birth should be a valid date")]
        public DateTime? DateOfBirth { get; set; }
        public string? Picture { get; set; }
        public string PinCode { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        #endregion

        #region User Security
        public string? MacAddress { get; set; }
        #endregion
        public ICollection<AddressDTO>? Addresses { get; set; }


    }
}