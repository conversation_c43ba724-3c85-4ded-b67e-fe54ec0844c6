﻿

using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations.Schema;

namespace Platform.Dyno.AccessManagement.DTO.Notification
{
    public class SubscriberDeviceDTO : ReferentialData
    {
        public Guid Id { get; set; }
        public string EndPoint { get; set; } = string.Empty;
        public string EndPointArn { get; set; } = string.Empty;
        public string TargetArn { get; set; } = string.Empty;
        public string SubscriptionArn { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        [ForeignKey("UserEntityId")]
        public UserDTO? User { get; set; }
        public Guid? UserEntityId { get; set; }
        public List<MessageNotificationDTO>? MessageNotifications { get; set; } = new List<MessageNotificationDTO>();
    }
}
