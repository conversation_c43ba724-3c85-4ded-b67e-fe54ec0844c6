﻿using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IAuthClientService : IAuthService<ClientLoginDTO, ClientRegisterDTO>
    {
        public ResponseAPI<AuthResponseDTO> BiometricLogin(Guid Id, string password);
        public ResponseAPI<bool> CheckPhone(string countryCode, string phoneNumber, string macAddress);             
        public ResponseAPI<AuthResponseDTO> ResetPassword(ResetPasswordClientDTO resetPassword);
        public ResponseAPI<AuthResponseDTO> Confirme(string countryCode, string element, int code);
        public ResponseAPI<AuthResponseDTO> VerifyCode(ClientOtpDTO clientOtp);
    }
}
