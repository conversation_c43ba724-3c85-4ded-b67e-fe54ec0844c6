﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass.Generic
{
    public class GenericRepository<C, T> : IGenericRepository<T> where T : class
        where C : DbContext
    {
        private readonly C _context;
        private readonly DbSet<T> _dbSet;

        public GenericRepository(C context)
        {
            _context = context;
            _dbSet = _context.Set<T>();
        }

        #region Get
        public IList<T> GetAll(Expression<Func<T, bool>>? expression = null, Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, List<string>? includes = null)
        {
            IQueryable<T> query = _dbSet;

            if (expression != null)
            {
                query = query.Where(expression);
            }

            if (includes != null)
            {
                foreach (var includeProperty in includes)
                {
                    query = query.Include(includeProperty);
                }
            }

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            return query.AsNoTracking().ToList();
        }

        public IQueryable<T> GetAllIQueryable(Expression<Func<T, bool>>? expression = null, Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, List<string>? includes = null)
        {
            IQueryable<T> query = _dbSet;

            if (expression != null)
            {
                query = query.Where(expression);
            }

            if (includes != null)
            {
                foreach (var includeProperty in includes)
                {
                    query = query.Include(includeProperty);
                }
            }

            if (orderBy != null)
            {
                query = orderBy(query);
            }

            return query;
        }

        public T? Get(Expression<Func<T, bool>>? expression = null, Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, List<string>? includes = null)
        {
            IQueryable<T> query = _dbSet;

            if (expression != null)
            {
                query = query.Where(expression);
            }

                if (includes != null)
            {
                foreach (var includeProperty in includes)
                {
                    query = query.Include(includeProperty);
                }
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }

            return query.AsNoTracking().FirstOrDefault();
        }

        public T? Find(params object[] keyValues)
        {
            return _dbSet.Find(keyValues);

        }
        #endregion

        #region Insert
        public void Insert(T entity)
        {
            _dbSet.Add(entity);
        }

        public void InsertRange(IEnumerable<T> entities)
        {
            _dbSet.AddRange(entities);
        }
        #endregion

        #region Attach 
        public void Attach(T entity)
        {
            _dbSet.Attach(entity);
        }
        #endregion

        #region Detach
        public void Detach(T entity)
        {
            var entry = _context.Entry(entity);
            entry.State = EntityState.Detached;
        }
        #endregion

        #region Update
        public void Update(T entity)
        {
            _context.ChangeTracker.Clear();
            _dbSet.Update(entity);
            _context.Entry(entity).State = EntityState.Modified;
        }

        public void UpdateRange(IEnumerable<T> entities)
        {
            foreach (var entity in entities)
            {
                _context.ChangeTracker.Clear();
                _dbSet.Attach(entity);
                _context.Entry(entity).State = EntityState.Modified;
            }
        }
        #endregion

        #region Delete
        public void Delete(Guid id)
        {
            var entity = _dbSet.Find(id);
            if (entity != null)
            {
                _dbSet.Remove(entity);
            }
        }

        public void DeleteByExpression(Expression<Func<T, bool>> expression)
        {
            var entity = _dbSet.FirstOrDefault(expression);
            if (entity != null)
            {
                _dbSet.Remove(entity);
            }
        }

        public void DeleteCK(object key1, object key2)
        {
            var entity = _dbSet.Find(key1, key2);
            if (entity != null)
            {
                _dbSet.Remove(entity);
            }
        }
        #endregion

        #region Save
        public void Save(T entity)
        {
            _context.Entry(entity).State = EntityState.Detached;
        }

        public void Entry(T entity)
        {
            _context.Entry(entity).State = EntityState.Modified;
        }
        #endregion
        public IDbContextTransaction BeginTransaction()
        {
            return _context.Database.BeginTransaction();
        }



    }
}
