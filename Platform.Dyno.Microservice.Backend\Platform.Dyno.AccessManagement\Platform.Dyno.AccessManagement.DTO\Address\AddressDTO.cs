﻿using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Platform.Dyno.AccessManagement.DTO.Address
{
    public class AddressDTO : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }

        [Range(-180, 180, ErrorMessage = "The Longitude must be between -180 and +180.")]
        public double Longitude { get; set; }

        [Range(-90, 90, ErrorMessage = "The Longitude must be between -90 and +90.")]
        public double Latitude { get; set; }
        public string? FullAddress { get; set; }
        #endregion

        #region Structure
        [JsonIgnore]
        public UserDTO? User { get; set; }
        public Guid? UserId { get; set; }

        [JsonIgnore]
        public CompanyDTO? Company { get; set; }
        public Guid? CompanyId { get; set; }
        #endregion
    }
}
