# 🔧 SCRIPT DE TEST RAPIDE - COMPILATION ET DÉMARRAGE
# Platform Dyno - Vérification du service de transaction distribuée

Write-Host "🔧 VÉRIFICATION DE LA COMPILATION" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Naviguer vers le projet
Set-Location "Platform.Dyno.Microservice.Backend\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.WebAPI"

Write-Host "📁 Répertoire actuel: $(Get-Location)" -ForegroundColor Yellow

# Tenter la compilation
Write-Host "`n🔨 Compilation en cours..." -ForegroundColor Cyan
$buildResult = dotnet build --verbosity minimal 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ COMPILATION RÉUSSIE!" -ForegroundColor Green
    Write-Host "`n🚀 Tentative de démarrage du service..." -ForegroundColor Cyan
    
    # Démarrer le service en arrière-plan
    Start-Process -FilePath "dotnet" -ArgumentList "run" -WindowStyle Hidden
    
    Write-Host "⏳ Attente du démarrage du service (10 secondes)..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # Vérifier si le service écoute sur le port
    $port7018 = netstat -an | Select-String ":7018"
    
    if ($port7018) {
        Write-Host "✅ SERVICE DÉMARRÉ AVEC SUCCÈS!" -ForegroundColor Green
        Write-Host "🌐 Service disponible sur: http://localhost:7018" -ForegroundColor Green
        Write-Host "`n📋 Endpoints de test disponibles:" -ForegroundColor Cyan
        Write-Host "  - GET  /api/Wallet/GetMyWallets" -ForegroundColor White
        Write-Host "  - POST /api/DistributedTransaction/ExecuteSecure" -ForegroundColor White
        Write-Host "  - GET  /api/DistributedTransaction/Status/{id}" -ForegroundColor White
        
        Write-Host "`n🧪 Pour tester le service:" -ForegroundColor Yellow
        Write-Host "  1. Utilisez le fichier test_distributed_transaction.http" -ForegroundColor White
        Write-Host "  2. Ou exécutez test_distributed_transaction.ps1" -ForegroundColor White
        
    } else {
        Write-Host "❌ Le service ne semble pas écouter sur le port 7018" -ForegroundColor Red
        Write-Host "🔍 Vérifiez les logs pour plus de détails" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ ERREURS DE COMPILATION DÉTECTÉES:" -ForegroundColor Red
    Write-Host $buildResult -ForegroundColor Red
    
    Write-Host "`n🔧 CORRECTIONS SUGGÉRÉES:" -ForegroundColor Yellow
    Write-Host "1. Vérifiez les imports manquants" -ForegroundColor White
    Write-Host "2. Assurez-vous que tous les DTOs sont référencés" -ForegroundColor White
    Write-Host "3. Vérifiez les signatures de méthodes" -ForegroundColor White
}

Write-Host "`n📊 RÉSUMÉ:" -ForegroundColor Cyan
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Compilation: SUCCÈS" -ForegroundColor Green
} else {
    Write-Host "❌ Compilation: ÉCHEC" -ForegroundColor Red
}

Write-Host "`n🔄 Retour au répertoire racine..." -ForegroundColor Gray
Set-Location "..\..\..\.."
