﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.Notification.DTO.Emailing;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Notification
{
    public class SignalRNotificationService : ISignalRNotificationService
    {
        private readonly IUnitOfWork<SignalRNotificationEntity> _signalRNotification;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _notificationCacheKey = RedisCacheKey.NotificationCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("notification");
        private readonly IHelper<NotificationDTO> _helper;
        private readonly Configuration _configuration;
        private readonly ICompanyService _companyService;
        public SignalRNotificationService(IUnitOfWork<SignalRNotificationEntity> signalRNotification,
            IMapper mapper,
            IRedisCacheService cache,
            IHelper<NotificationDTO> helper,
            Configuration configuration,
            ICompanyService companyService) 
        {
            _signalRNotification = signalRNotification;
            _mapper = mapper;
            _cache = cache;
            _helper = helper;
            _configuration = configuration;
            _companyService = companyService;
        }

        #region Get
        public ResponseAPI<List<SignalRNotificationDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SignalRNotificationDTO>> response = new ResponseAPI<List<SignalRNotificationDTO>>();

            try
            {
                List<SignalRNotificationDTO>? notificationsDTO = _cache.GetData<List<SignalRNotificationDTO>>(_notificationCacheKey);
                if (notificationsDTO == null || notificationsDTO.Count() == 0)
                {
                    List<SignalRNotificationEntity> notificationEntity = _signalRNotification.Repository
                    .GetAll(
                        includes: new List<string> { "CompanyNotifs" },
                        orderBy: notif => notif.OrderByDescending(notif => notif.CreationTime),
                        expression: notif => notif.IsSeen == false && (notif.CompanyNotifs != null ? notif.CompanyNotifs.Any(c => c.CompanyId == companyId) : false))
                    .ToList();
                    List<SignalRNotificationBM> notificationsBM = _mapper.Map<List<SignalRNotificationBM>>(notificationEntity);
                    notificationsDTO = _mapper.Map<List<SignalRNotificationDTO>>(notificationsBM);

                    _cache.SetData(_notificationCacheKey, notificationsDTO, DateTimeOffset.UtcNow.AddDays(1));
                }
                response.StatusCode = notificationsDTO == null || notificationsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = notificationsDTO;
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }

        public ResponseAPI<PagedList<SignalRNotificationDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SignalRNotificationDTO>> notificationsDTO = GetAll();
            PagedList<SignalRNotificationDTO>? pagedList = null;
            if (notificationsDTO.ObjectValue != null)
            {
                pagedList = PagedList<SignalRNotificationDTO>.ToGenericPagedList(notificationsDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<SignalRNotificationDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<SignalRNotificationDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SignalRNotificationDTO>> notificationsDTO = GetAll();
            PagedList<SignalRNotificationDTO>? pagedList = null;
            if (notificationsDTO.ObjectValue != null)
            {
                pagedList = PagedList<SignalRNotificationDTO>.ToGenericPagedList(notificationsDTO.ObjectValue.Where(macAddress => macAddress.Status == Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<SignalRNotificationDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<SignalRNotificationDTO> Get(Guid id)
        {
            SignalRNotificationDTO? notificationDTO = GetAll().ObjectValue?.Where(notif => notif.Id == id).FirstOrDefault();
            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = notificationDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = notificationDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = notificationDTO
            };
        }

        public ResponseAPI<List<SignalRNotificationDTO>> Get(Func<SignalRNotificationDTO, bool> expression)
        {
            List<SignalRNotificationDTO>? notificationsDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<SignalRNotificationDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = notificationsDTO
            };
        }
        #endregion

        #region Create
        public ResponseAPI<SignalRNotificationDTO> Create(SignalRNotificationDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            if(companyId == null)
            {
                return new ResponseAPI<SignalRNotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized"
                };
            };
            CompanyDTO? companyCreator = _companyService.Get((Guid)companyId).ObjectValue;
            if(companyCreator == null)
            {
                return new ResponseAPI<SignalRNotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized"
                };
            }
            dtoObject.Message = dtoObject.Message + " " + companyCreator.Name;
            SignalRNotificationBM notificationBM = _mapper.Map<SignalRNotificationBM>(dtoObject);

            #region changed data
            notificationBM.Id = Guid.NewGuid();
            RefDataService<SignalRNotificationBM>.CreateRefData(notificationBM, creatorUserId);
            #endregion

            SignalRNotificationEntity notificationEntity = _mapper.Map<SignalRNotificationEntity>(notificationBM);

            _signalRNotification.Repository.Insert(notificationEntity);
            _signalRNotification.Save();
            List<string> sendToGroups = new List<string>();
            if (notificationBM.SendToId != null)
            {
                foreach (var companySender in notificationBM.SendToId)
                {
                    CompanyDTO? company = _companyService.Get(companySender.Id).ObjectValue;
                    if (company != null)
                    {
                        CompanyBM companyBM = _mapper.Map<CompanyBM>(company);
                        CompanyEntity companyEntity = _mapper.Map<CompanyEntity>(companyBM);
                        notificationEntity.CompanyNotifs?.Add(new CompanyNotifEntity
                        {
                            Company = companyEntity,
                            Notification = notificationEntity
                        });

                        sendToGroups.Add(company.Id.ToString());
                    }

                }
            }
            _signalRNotification.Save();
            dtoObject.Id = notificationEntity.Id;
            

            NotificationDTO notification = new NotificationDTO
            {
                Id= notificationEntity.Id,
                Title = dtoObject.Title,
                Message = dtoObject.Message,
                SendToGroup = sendToGroups
            };

            string url = $"{_configuration.NotificationAddress}/Api/Notification/Send";
            ResponseAPI<NotificationDTO>? responseAPI = _helper.Post(url, notification);

            if (updateCache)
            {
                _cache.RemoveData(_notificationCacheKey);
            }

            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };


        }
        public ResponseAPI<SignalRNotificationDTO> CreateValidCashbackNotif(SignalRNotificationDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true)
        {

            SignalRNotificationBM notificationBM = _mapper.Map<SignalRNotificationBM>(dtoObject);

            #region changed data
            notificationBM.Id = Guid.NewGuid();
            RefDataService<SignalRNotificationBM>.CreateRefData(notificationBM, creatorUserId);
            #endregion

            SignalRNotificationEntity notificationEntity = _mapper.Map<SignalRNotificationEntity>(notificationBM);

            _signalRNotification.Repository.Insert(notificationEntity);
            _signalRNotification.Save();
            List<string> sendToGroups = new List<string>();
            if (notificationBM.SendToId != null)
            {
                foreach (var companySender in notificationBM.SendToId)
                {
                    CompanyDTO? company = _companyService.Get(companySender.Id).ObjectValue;
                    if (company != null)
                    {
                        CompanyBM companyBM = _mapper.Map<CompanyBM>(company);
                        CompanyEntity companyEntity = _mapper.Map<CompanyEntity>(companyBM);
                        notificationEntity.CompanyNotifs?.Add(new CompanyNotifEntity
                        {
                            Company = companyEntity,
                            Notification = notificationEntity
                        });

                        sendToGroups.Add(company.Id.ToString());
                    }

                }
            }
            dtoObject.Id = notificationEntity.Id;


            NotificationDTO notification = new NotificationDTO
            {
                Id = notificationEntity.Id,
                Title = dtoObject.Title,
                Message = dtoObject.Message,
                SendToGroup = sendToGroups
            };

            string url = $"{_configuration.NotificationAddress}/Api/Notification/Send";
            ResponseAPI<NotificationDTO>? responseAPI = _helper.Post(url, notification);

            if (updateCache)
            {
                _cache.RemoveData(_notificationCacheKey);
            }

            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };


        }
        #endregion

        #region Update
        public ResponseAPI<SignalRNotificationDTO> Update(SignalRNotificationDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                SignalRNotificationBM notification = _mapper.Map<SignalRNotificationBM>(dtoObject);

                #region RefData
                RefDataService<SignalRNotificationBM>.UpdateRefData(notification, notification.Id);
                #endregion

                SignalRNotificationEntity notificationEntity = _mapper.Map<SignalRNotificationEntity>(notification);
                _signalRNotification.Repository.Update(notificationEntity);
                _signalRNotification.Save();
                dtoObject.Id = notificationEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_notificationCacheKey);
                }

                return new ResponseAPI<SignalRNotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }

        public ResponseAPI<SignalRNotificationDTO> UpdateIsSeen(Guid id,Guid? updateUserId = null, bool updateCache = true)
        {
            SignalRNotificationEntity? notification = _signalRNotification.Repository.Get(u => u.Id == id);
            if (notification != null)
            {
                #region RefData
                notification.LastModificationTime = DateTime.Now.ToUniversalTime();
                notification.LastModifierUserId = updateUserId;
                notification.IsSeen = true;
                #endregion

                _signalRNotification.Repository.Update(notification);
                _signalRNotification.Save();

                SignalRNotificationBM notificationBM = _mapper.Map<SignalRNotificationBM>(notification);
                SignalRNotificationDTO notificationDTO = _mapper.Map<SignalRNotificationDTO>(notificationBM);


                if (updateCache)
                {
                    _cache.RemoveData(_notificationCacheKey);
                }

                return new ResponseAPI<SignalRNotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = notificationDTO
                };
            }

            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }

        #endregion

        #region Delete
        public ResponseAPI<SignalRNotificationDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            SignalRNotificationDTO? notificationDTO = GetAll().ObjectValue?.Where(dto => dto.Id == id).FirstOrDefault();
            if (notificationDTO != null)
            {
                SignalRNotificationBM notification = _mapper.Map<SignalRNotificationBM>(notificationDTO);

                #region Refdata
                RefDataService<SignalRNotificationBM>.DeleteRefData(notification, notification.Id);
                #endregion

                SignalRNotificationEntity notificationEntity = _mapper.Map<SignalRNotificationEntity>(notification);

                _signalRNotification.Repository.Update(notificationEntity);
                _signalRNotification.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_notificationCacheKey);
                }

                return new ResponseAPI<SignalRNotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = notificationDTO
                };
            }

            return new ResponseAPI<SignalRNotificationDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.DeleteError
            };
        }
        #endregion
    }
}
