﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.BMs
{
    public class WalletBM
    {
        public Guid Id { get; set; }
        public string PrivateKey { get; set; } = string.Empty;
        public string PublicKey { get; set; } = string.Empty;
        public WalletType WalletType { get; set; }
        public UserType AssignedToType { get; set; }
        public Guid AssignedToId { get; set; }
        public double Balance { get; set; }
        public WalletStatus Status { get; set; }
    }
}
