﻿using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations;

namespace Platform.Dyno.AccessManagement.DTO.User
{
    public class UserDTO : ReferentialData
    {

        #region User Identity Data
        public Guid? Id { get; set; }

        [Required(ErrorMessage = "UserName is required")]
        public string? FullName { get; set; }

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;

        public string? NormalizedEmail { get; set; }

        public bool EmailConfirmed { get; set; } = false;
        public string PhoneNumber { get; set; } = string.Empty;
        public string CountryCode { get; set; }

        public bool PhoneNumberConfirmed { get; set; }

        public string Password { get; set; } = string.Empty;
        public string PinCode { get; set; } = string.Empty;

        public string UserName { get; set; } = string.Empty;

        public string NormalizedUserName { get; set; } = string.Empty;
        #endregion

        #region User Data
        public string? Picture { get; set; }
        public Gender? Gender { get; set; }

        [Required(ErrorMessage = "Date of birth is required")]
        [DataType(DataType.Date, ErrorMessage = "Date of birth should be a valid date")]
        public DateTime? DateOfBirth { get; set; }

        public UserType UserType { get; set; }

        #endregion

        #region User Security
        public string? FCMToken { get; set; }
        public AuthentificationSource AuthentificationSource { get; set; }
        public string SecurityStamp { get; set; } = string.Empty;

        public string ConcurrencyStamp { get; set; } = string.Empty;

        public DateTimeOffset? LockoutEnd { get; set; }
        public bool LockoutEnabled { get; set; }
        public int AccessFailedCount { get; set; }
        #endregion

        #region Structure
        public IList<RoleDTO> Roles { get; set; } = new List<RoleDTO>();
        public IList<AddressDTO>? Addresses { get; set; }
        public ICollection<MacAddressDTO>? MacAddresses { get; set; }
        public Guid CompanyId { get; set; }
        #endregion
    }
}
