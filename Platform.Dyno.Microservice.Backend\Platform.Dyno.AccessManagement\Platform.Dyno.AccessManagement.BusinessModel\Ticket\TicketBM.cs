﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.Ticket
{
    public class TicketBM: ReferentialData
    {
        public Guid TicketId { get; set; }
        public string Name { get; set; } = string.Empty;
        public double Amount { get; set; }
        public int Quantity { get; set; }
        public double TotalAmount { get; set; }
        public WalletType Type { get; set; }
        public Guid CompanyId { get; set; }
        public bool IsAutomatic { get; set; }
        public PeriodType PeriodType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
