﻿using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Role
{
    [Table("RoleUser")]
    public class RoleUserEntity
    {
        [ForeignKey("RoleId")]
        public Guid RoleId { get; set; }
        public RoleEntity? Role { get; set; } 



        [ForeignKey("UserId")]
        public Guid UserId { get; set; }
        public UserEntity? User { get; set; } 

    }
}
