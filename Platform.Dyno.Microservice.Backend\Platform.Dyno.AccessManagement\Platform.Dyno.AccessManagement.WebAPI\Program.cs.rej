diff a/Platform.Dyno.Microservice.Backend/Platform.Dyno.AccessMangement/Platform.Dyno.AccessManagement.WebAPI/Program.cs b/Platform.Dyno.Microservice.Backend/Platform.Dyno.AccessMangement/Platform.Dyno.AccessManagement.WebAPI/Program.cs	(rejected hunks)
@@ -32,7 +32,6 @@
 #region Connection DataBase
 
 var configuration = new ConfigurationBuilder()
-            .AddJsonFile("C:\\Users\\<USER>\\Desktop\\DynoRepo\\Configuration\\connectionDB.json", optional: false, reloadOnChange: true)
             .AddJsonFile("C:\\Users\\<USER>\\Desktop\\Dyno.Backend\\Configuration\\connectionDB.json", optional: false, reloadOnChange: true)
             .Build();
 builder.Services.AddDbContext<ContextDB>(
