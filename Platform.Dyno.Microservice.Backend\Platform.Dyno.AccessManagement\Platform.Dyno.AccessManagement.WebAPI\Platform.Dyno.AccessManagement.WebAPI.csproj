﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>efbe3054-bee7-461f-85f7-5a01fc79919f</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DisableGenerateStaticWebAssetEndpoints>true</DisableGenerateStaticWebAssetEndpoints>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <FileAlignment>1024</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <FileAlignment>1024</FileAlignment>
  </PropertyGroup>
  <PropertyGroup>
    <StaticWebAssetsEnabled>false</StaticWebAssetsEnabled>
    <DisableGenerateStaticWebAssetEndpoints>true</DisableGenerateStaticWebAssetEndpoints>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="Camunda.Api.Client" />
    <PackageReference Include="Camunda.Worker" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions.Http" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj" />
    <ProjectReference Include="..\..\Platform.Dyno.Payment\Platform.Dyno.Payment\Platform.Dyno.Payment.DTO\Platform.Dyno.Payment.DTO.csproj" />
    <ProjectReference Include="..\..\Server.Kafka\Server.Kafka\Server.Kafka.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.BusinessModel\Platform.Dyno.AccessManagement.BusinessModel.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.Business\Platform.Dyno.AccessManagement.Business.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.DTO\Platform.Dyno.AccessManagement.DTO.csproj" />
    <ProjectReference Include="..\Platform.Dyno.AccessManagement.EF\Platform.Dyno.AccessManagement.EF.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="HTML\image\" />
    <Folder Include="wwwroot\" />
  </ItemGroup>
</Project>