# 📱 GUIDE COMPLET FLUTTER - PLATFORM DYNO

## 🎯 **DOCUMENTATION POUR AGENT IA MOBILE**

Cette documentation complète permet à un agent IA de développer l'application mobile Flutter pour Platform Dyno.

## 🏗️ **ARCHITECTURE SYSTÈME**

```
📱 FLUTTER APP (Employés)
    ↓ HTTPS/JSON + SignalR
🌐 PLATFORM DYNO BACKEND
├── 🔐 AuthClient (7274) - Auth employés
├── 💰 Payment (7018) - Transactions sécurisées  
├── 🔔 Notification (7038) - Temps réel
└── 🏢 Company (7274) - Données entreprises
```

## 🔐 **AUTHENTIFICATION COMPLÈTE**

### **URLs de Base**
```dart
// Production
static const String authUrl = 'https://api.dyno-motiva.com/api/AuthClient';
static const String paymentUrl = 'https://payment-api.dyno-motiva.com/api';

// Développement  
static const String authUrl = 'http://localhost:7274/api/AuthClient';
static const String paymentUrl = 'http://localhost:7018/api';
```

### **Modèles Dart Complets**

```dart
// auth_models.dart
class AuthResponse {
  final String token;
  final String refreshToken;
  final DateTime expiredDate;
  final UserProfile userProfile;

  AuthResponse({
    required this.token,
    required this.refreshToken,
    required this.expiredDate,
    required this.userProfile,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      refreshToken: json['refreshToken'],
      expiredDate: DateTime.parse(json['expiredDate']),
      userProfile: UserProfile.fromJson(json['userProfile']),
    );
  }
}

class UserProfile {
  final String id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String userType;
  final String? companyId;

  UserProfile({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.userType,
    this.companyId,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      fullName: json['fullName'],
      email: json['email'],
      phoneNumber: json['phoneNumber'],
      userType: json['userType'],
      companyId: json['companyId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'userType': userType,
      'companyId': companyId,
    };
  }
}

// wallet_models.dart
class Wallet {
  final String id;
  final String walletType;
  final String assignedToName;
  final String assignedToId;
  final double balance;
  final String status;

  Wallet({
    required this.id,
    required this.walletType,
    required this.assignedToName,
    required this.assignedToId,
    required this.balance,
    required this.status,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      id: json['id'],
      walletType: json['walletType'],
      assignedToName: json['assignedToName'],
      assignedToId: json['assignedToId'],
      balance: json['balance'].toDouble(),
      status: json['status'],
    );
  }

  bool get isActive => status == 'Active';
}

// transaction_models.dart
class Transaction {
  final String? id;
  final String senderWalletId;
  final String receiverWalletId;
  final double amount;
  final String description;
  final DateTime transactionDate;
  final String? status;

  Transaction({
    this.id,
    required this.senderWalletId,
    required this.receiverWalletId,
    required this.amount,
    required this.description,
    required this.transactionDate,
    this.status,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      senderWalletId: json['senderWalletId'],
      receiverWalletId: json['receiverWalletId'],
      amount: json['amount'].toDouble(),
      description: json['description'] ?? '',
      transactionDate: DateTime.parse(json['transactionDate']),
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'senderWalletId': senderWalletId,
      'receiverWalletId': receiverWalletId,
      'amount': amount,
      'description': description,
      'transactionDate': transactionDate.toIso8601String(),
    };
  }
}

// api_response.dart
class ApiResponse<T> {
  final int statusCode;
  final T? objectValue;
  final String? exceptionMessage;

  ApiResponse({
    required this.statusCode,
    this.objectValue,
    this.exceptionMessage,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      statusCode: json['statusCode'],
      objectValue: json['objectValue'] != null && fromJsonT != null
          ? fromJsonT(json['objectValue'])
          : json['objectValue'],
      exceptionMessage: json['exceptionMessage'],
    );
  }

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
  bool get isUnauthorized => statusCode == 401;
  bool get isBadRequest => statusCode == 400;
  bool get isNotFound => statusCode == 404;
}
```

## 🔧 **SERVICE API COMPLET**

```dart
// api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String authBaseUrl = 'http://localhost:7274/api/AuthClient';
  static const String paymentBaseUrl = 'http://localhost:7018/api';
  
  String? _token;
  
  void setToken(String token) {
    _token = token;
  }

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'acceptLanguage': 'en',
    if (_token != null) 'Authorization': 'Bearer $_token',
  };

  Map<String, String> get _authHeaders => {
    'Content-Type': 'application/json',
    'acceptLanguage': 'en',
    'acceptTermsAndConditions': 'true',
  };

  // AUTHENTIFICATION
  Future<ApiResponse<AuthResponse>> login(
    String phoneNumber, 
    String password, 
    String countryCode
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$authBaseUrl/login'),
        headers: _authHeaders,
        body: jsonEncode({
          'phoneNumber': phoneNumber,
          'password': password,
          'countryCode': countryCode,
        }),
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => AuthResponse.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  Future<ApiResponse<AuthResponse>> refreshToken(
    String token, 
    String refreshToken, 
    DateTime expiredDate
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$authBaseUrl/RefreshToken'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'token': token,
          'refreshToken': refreshToken,
          'expiredDate': expiredDate.toIso8601String(),
        }),
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => AuthResponse.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  Future<ApiResponse<String>> forgetPassword(
    String countryCode, 
    String phoneNumber
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$authBaseUrl/ForgetPassword/$countryCode/$phoneNumber'),
        headers: _authHeaders,
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, null);
    } catch (e) {
      return ApiResponse<String>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  Future<ApiResponse<String>> verifyOTP(
    String phoneNumber, 
    String otpCode, 
    String countryCode
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$authBaseUrl/VerifyOTPCode'),
        headers: _authHeaders,
        body: jsonEncode({
          'phoneNumber': phoneNumber,
          'otpCode': otpCode,
          'countryCode': countryCode,
        }),
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, null);
    } catch (e) {
      return ApiResponse<String>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  // WALLETS
  Future<ApiResponse<List<Wallet>>> getMyWallets() async {
    try {
      final response = await http.get(
        Uri.parse('$paymentBaseUrl/Wallet/GetMyWallets'),
        headers: _headers,
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => (data as List).map((item) => Wallet.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse<List<Wallet>>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  Future<ApiResponse<double>> getTotalBalance() async {
    try {
      final response = await http.get(
        Uri.parse('$paymentBaseUrl/Wallet/GetTotalBalance'),
        headers: _headers,
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => data.toDouble(),
      );
    } catch (e) {
      return ApiResponse<double>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  // TRANSACTIONS SÉCURISÉES (NOUVEAU)
  Future<ApiResponse<Transaction>> executeSecureTransaction(
    Transaction transaction, 
    String pinCode
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$paymentBaseUrl/DistributedTransaction/ExecuteSecure?pinCode=$pinCode'),
        headers: _headers,
        body: jsonEncode(transaction.toJson()),
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => Transaction.fromJson(data),
      );
    } catch (e) {
      return ApiResponse<Transaction>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getTransactionStatus(String transactionId) async {
    try {
      final response = await http.get(
        Uri.parse('$paymentBaseUrl/DistributedTransaction/Status/$transactionId'),
        headers: _headers,
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, null);
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }

  // TRANSACTIONS STANDARD
  Future<ApiResponse<List<Transaction>>> getMyTransactions({
    int pageSize = 20,
    int pageNumber = 1,
    String order = 'DESC',
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$paymentBaseUrl/Transaction/GetAllPaged?pageSize=$pageSize&pageNumber=$pageNumber&order=$order'),
        headers: _headers,
      );

      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(
        jsonResponse,
        (data) => (data as List).map((item) => Transaction.fromJson(item)).toList(),
      );
    } catch (e) {
      return ApiResponse<List<Transaction>>(
        statusCode: 500,
        exceptionMessage: 'Network error: $e',
      );
    }
  }
}
```
