﻿using AutoMapper;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Mapping
{
    public class MappingDTO_DTO : Profile
    {
        public MappingDTO_DTO() 
        {
            #region User
            CreateMap<AdminRegisterDTO, UserDTO>().ReverseMap();
            CreateMap<ClientRegisterDTO, UserDTO>().ReverseMap();
            CreateMap<UserProfileDTO, UserDTO>().ReverseMap();
            #endregion

            #region Transaction
            CreateMap<TransactionDTO, PreTransactionDTO>().ReverseMap();
            CreateMap<TransactionDTO, UniqueQRCodeTransactionDTO>().ReverseMap();
            #endregion
            #region Sales
            CreateMap<SalesOrderDTO, SalesInvoiceDTO>().ReverseMap();
            #endregion
        }

    }
}
