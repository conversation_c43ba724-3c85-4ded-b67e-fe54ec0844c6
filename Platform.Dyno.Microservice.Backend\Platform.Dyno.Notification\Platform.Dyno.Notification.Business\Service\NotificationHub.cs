﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Notification.DTO.Notification;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.Business.Service
{
    public class NotificationHub : Hub
    {
        private static int connectedClientsCount = 0;

        public async Task AddToGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            await Clients.Group(groupName).SendAsync("ReceiveNotification", $"{Context.ConnectionId} joined {groupName}");
        }

        public override async Task OnConnectedAsync()
        {
            var accessToken = Context.GetHttpContext()?.Request.Query["access_token"];
            var handler = new JwtSecurityTokenHandler();

            // Read the token without validating it
            var jsonToken = handler.ReadToken(accessToken) as JwtSecurityToken;

            if (jsonToken != null)
            {
                // Access claims from the decoded token
                var companyClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "Company")?.Value;
                var userTypeClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "UserType")?.Value;
                var requestUrl = Context.GetHttpContext()?.Request.Path.Value;
                string groupName = $"{requestUrl}/{companyClaim}";
                if (!string.IsNullOrEmpty(groupName))
                {
                    await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
                    await Clients.Caller.SendAsync("ReceiveMessage", $"You have joined the group: {groupName}");
                    await Clients.Group(groupName).SendAsync("ReceiveMessage", $"{Context.ConnectionId} has joined the group");
                }
            }

            
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            connectedClientsCount--;
            await Clients.All.SendAsync("ClientCountChanged", connectedClientsCount);
            await base.OnDisconnectedAsync(exception);
        }
    }
}
