<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0pxyr8g" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.19.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="Process_SalesOrder_Management" name="Sales Order Management" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:sequenceFlow id="Flow_1b3ua8z" sourceRef="Activity_1wast8y" targetRef="Event_0te5nqr" />
    <bpmn:sequenceFlow id="Flow_1djmybl" sourceRef="Activity_1n5m1ax" targetRef="Activity_1wast8y" />
    <bpmn:sequenceFlow id="Flow_0dpnstt" sourceRef="Activity_1b0s858" targetRef="Activity_1wast8y" />
    <bpmn:sequenceFlow id="Flow_0gpf44f" sourceRef="Activity_1bqbpu7" targetRef="Activity_1b0s858" />
    <bpmn:sequenceFlow id="Flow_03vxsi6" sourceRef="Activity_0vd3ese" targetRef="Activity_1rv5vxk" />
    <bpmn:sequenceFlow id="Flow_18p28f0" sourceRef="Activity_022cdbo" targetRef="Activity_0vd3ese" />
    <bpmn:sequenceFlow id="Flow_0bncapp" sourceRef="Gateway_110l2fy" targetRef="Activity_1n5m1ax">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Send_Email_SO ==  true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sw8bqf" sourceRef="Gateway_110l2fy" targetRef="Activity_1bqbpu7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Send_Email_SO == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0q1rp61" sourceRef="Activity_0c1uma3" targetRef="Gateway_110l2fy" />
    <bpmn:sequenceFlow id="Flow_0f7ga3i" sourceRef="Activity_08l6jvu" targetRef="Activity_0c1uma3" />
    <bpmn:sequenceFlow id="Flow_1nvo3iy" sourceRef="Gateway_1uxdjtt" targetRef="Activity_08l6jvu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_SO_PDF == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1aebetc" sourceRef="Gateway_1uxdjtt" targetRef="Activity_022cdbo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Create_SO_PDF == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0lpwrp3" sourceRef="Activity_0sacem3" targetRef="Gateway_1uxdjtt" />
    <bpmn:serviceTask id="Activity_1wast8y" name="Send Notification SA" camunda:type="external" camunda:topic="Send_Notification_New_SO">
      <bpmn:incoming>Flow_1djmybl</bpmn:incoming>
      <bpmn:incoming>Flow_0dpnstt</bpmn:incoming>
      <bpmn:outgoing>Flow_1b3ua8z</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1n5m1ax" name="Status SO : Email Sent" camunda:type="external" camunda:topic="SO_Status_Email_Sent">
      <bpmn:incoming>Flow_0bncapp</bpmn:incoming>
      <bpmn:outgoing>Flow_1djmybl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1b0s858" name="Log-Error: Email Failed" camunda:type="external" camunda:topic="SO_Log_Error_Email_Failed">
      <bpmn:incoming>Flow_0gpf44f</bpmn:incoming>
      <bpmn:outgoing>Flow_0dpnstt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1bqbpu7" name="Status SO : Email Failed" camunda:type="external" camunda:topic="SO_Status_Email_Failed">
      <bpmn:incoming>Flow_1sw8bqf</bpmn:incoming>
      <bpmn:outgoing>Flow_0gpf44f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_1rv5vxk" name="Send Notification SA : SO PDF failed" camunda:type="external" camunda:topic="Notification_PDF_Failed">
      <bpmn:incoming>Flow_03vxsi6</bpmn:incoming>
      <bpmn:outgoing>Flow_08z61ty</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_022cdbo" name="Status SO : PDF failed" camunda:type="external" camunda:topic="SO_Status_PDF_Failed">
      <bpmn:incoming>Flow_1aebetc</bpmn:incoming>
      <bpmn:incoming>Flow_0kfif7s</bpmn:incoming>
      <bpmn:outgoing>Flow_18p28f0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0vd3ese" name="Log-Error: PDF Failed" camunda:type="external" camunda:topic="SO_Log_Error_PDF_Failed">
      <bpmn:incoming>Flow_18p28f0</bpmn:incoming>
      <bpmn:outgoing>Flow_03vxsi6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0c1uma3" name="Send Email" camunda:type="external" camunda:topic="Send_Email_SO">
      <bpmn:incoming>Flow_0f7ga3i</bpmn:incoming>
      <bpmn:outgoing>Flow_0q1rp61</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_110l2fy">
      <bpmn:incoming>Flow_0q1rp61</bpmn:incoming>
      <bpmn:outgoing>Flow_1sw8bqf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bncapp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_08l6jvu" name="Status SO: PDF Generated" camunda:type="external" camunda:topic="SO_Status_PDF_Generated">
      <bpmn:incoming>Flow_1nvo3iy</bpmn:incoming>
      <bpmn:outgoing>Flow_0f7ga3i</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1uxdjtt">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0lpwrp3</bpmn:incoming>
      <bpmn:outgoing>Flow_1aebetc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1nvo3iy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Activity_0sacem3" name="Create SO PDF" camunda:type="external" camunda:topic="Create_SO_PDF">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1a69l3s</bpmn:incoming>
      <bpmn:outgoing>Flow_0lpwrp3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_05mlnv4</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_08z61ty" sourceRef="Activity_1rv5vxk" targetRef="Event_0ea41rf" />
    <bpmn:endEvent id="Event_0te5nqr">
      <bpmn:incoming>Flow_1b3ua8z</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_07itya0" />
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_05mlnv4" sourceRef="StartEvent_1" targetRef="Activity_15r9cd9" />
    <bpmn:exclusiveGateway id="Gateway_1szo20y">
      <bpmn:incoming>Flow_1pekplj</bpmn:incoming>
      <bpmn:outgoing>Flow_1a69l3s</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kfif7s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1pekplj" sourceRef="Activity_15r9cd9" targetRef="Gateway_1szo20y" />
    <bpmn:sequenceFlow id="Flow_1a69l3s" sourceRef="Gateway_1szo20y" targetRef="Activity_0sacem3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Add_Instance_To_SO == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0kfif7s" sourceRef="Gateway_1szo20y" targetRef="Activity_022cdbo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Result_Add_Instance_To_SO == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_15r9cd9" name="Add instance to SO" camunda:type="external" camunda:topic="Add_Instance_To_SO">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_05mlnv4</bpmn:incoming>
      <bpmn:outgoing>Flow_1pekplj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0ea41rf">
      <bpmn:incoming>Flow_08z61ty</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_0o16taz" />
    </bpmn:endEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_SalesOrder_Management">
      <bpmndi:BPMNShape id="Activity_07vqsvq_di" bpmnElement="Activity_1wast8y">
        <dc:Bounds x="1750" y="327" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13jdmgy_di" bpmnElement="Activity_1n5m1ax">
        <dc:Bounds x="1450" y="327" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sfxyxl_di" bpmnElement="Activity_1b0s858">
        <dc:Bounds x="1560" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rcdxpd_di" bpmnElement="Activity_1bqbpu7">
        <dc:Bounds x="1400" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1yw1mwk_di" bpmnElement="Activity_1rv5vxk">
        <dc:Bounds x="1240" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15gd16f_di" bpmnElement="Activity_022cdbo">
        <dc:Bounds x="940" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1t8wo6l_di" bpmnElement="Activity_0vd3ese">
        <dc:Bounds x="1090" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_138e665_di" bpmnElement="Activity_0c1uma3">
        <dc:Bounds x="1140" y="327" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_110l2fy_di" bpmnElement="Gateway_110l2fy" isMarkerVisible="true">
        <dc:Bounds x="1295" y="342" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ka4oe3_di" bpmnElement="Activity_08l6jvu">
        <dc:Bounds x="970" y="327" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uxdjtt_di" bpmnElement="Gateway_1uxdjtt" isMarkerVisible="true">
        <dc:Bounds x="865" y="342" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0apoqqh_di" bpmnElement="Activity_0sacem3">
        <dc:Bounds x="600" y="327" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="349" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1rgm8r9_di" bpmnElement="Event_0te5nqr">
        <dc:Bounds x="1982" y="349" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1szo20y_di" bpmnElement="Gateway_1szo20y" isMarkerVisible="true">
        <dc:Bounds x="465" y="342" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mtsphg_di" bpmnElement="Activity_15r9cd9">
        <dc:Bounds x="280" y="327" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0pb96ly_di" bpmnElement="Event_0ea41rf">
        <dc:Bounds x="1392" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1b3ua8z_di" bpmnElement="Flow_1b3ua8z">
        <di:waypoint x="1850" y="367" />
        <di:waypoint x="1982" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1djmybl_di" bpmnElement="Flow_1djmybl">
        <di:waypoint x="1550" y="367" />
        <di:waypoint x="1750" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dpnstt_di" bpmnElement="Flow_0dpnstt">
        <di:waypoint x="1660" y="260" />
        <di:waypoint x="1705" y="260" />
        <di:waypoint x="1705" y="337" />
        <di:waypoint x="1750" y="337" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gpf44f_di" bpmnElement="Flow_0gpf44f">
        <di:waypoint x="1500" y="260" />
        <di:waypoint x="1560" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03vxsi6_di" bpmnElement="Flow_03vxsi6">
        <di:waypoint x="1190" y="120" />
        <di:waypoint x="1240" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18p28f0_di" bpmnElement="Flow_18p28f0">
        <di:waypoint x="1040" y="120" />
        <di:waypoint x="1090" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bncapp_di" bpmnElement="Flow_0bncapp">
        <di:waypoint x="1345" y="367" />
        <di:waypoint x="1450" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sw8bqf_di" bpmnElement="Flow_1sw8bqf">
        <di:waypoint x="1320" y="342" />
        <di:waypoint x="1320" y="260" />
        <di:waypoint x="1400" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q1rp61_di" bpmnElement="Flow_0q1rp61">
        <di:waypoint x="1240" y="367" />
        <di:waypoint x="1295" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f7ga3i_di" bpmnElement="Flow_0f7ga3i">
        <di:waypoint x="1070" y="367" />
        <di:waypoint x="1140" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nvo3iy_di" bpmnElement="Flow_1nvo3iy">
        <di:waypoint x="915" y="367" />
        <di:waypoint x="970" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aebetc_di" bpmnElement="Flow_1aebetc">
        <di:waypoint x="890" y="342" />
        <di:waypoint x="890" y="120" />
        <di:waypoint x="940" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lpwrp3_di" bpmnElement="Flow_0lpwrp3">
        <di:waypoint x="700" y="367" />
        <di:waypoint x="865" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08z61ty_di" bpmnElement="Flow_08z61ty">
        <di:waypoint x="1340" y="120" />
        <di:waypoint x="1392" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05mlnv4_di" bpmnElement="Flow_05mlnv4">
        <di:waypoint x="188" y="367" />
        <di:waypoint x="280" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pekplj_di" bpmnElement="Flow_1pekplj">
        <di:waypoint x="380" y="367" />
        <di:waypoint x="465" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a69l3s_di" bpmnElement="Flow_1a69l3s">
        <di:waypoint x="515" y="367" />
        <di:waypoint x="600" y="367" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kfif7s_di" bpmnElement="Flow_0kfif7s">
        <di:waypoint x="490" y="342" />
        <di:waypoint x="490" y="120" />
        <di:waypoint x="940" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
