﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Reflection.PortableExecutable;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Role
{
    [Route("api/[controller]")]
    [ApiController]
    public class PermissionController : ControllerBase
    {
        private readonly IPermissionService _permissionService;
        private readonly ILogger<PermissionController> _logger;
        private readonly IUserService _userService;
        private readonly ISortingUtility _sortingUtility;

        public PermissionController(IPermissionService permissionService,
            ILogger<PermissionController> logger,
            IUserService userService,
            ISortingUtility sortingUtility)
        {
            _permissionService = permissionService;
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<PermissionDTO>> permissions = _permissionService.GetAll(new Guid(companyId), enumUserType);
                    return Ok(permissions);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<PermissionDTO>> permissions = _permissionService.GetAll(pagedParameters, new Guid(companyId), enumUserType);
                    if (permissions.ObjectValue != null)
                    {
                        permissions.ObjectValue = _sortingUtility.SortData(pagedParameters, permissions);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = permissions.ObjectValue.TotalCount,
                            PageSize = permissions.ObjectValue.PageSize,
                            CurrentPage = permissions.ObjectValue.CurrentPage,
                            TotalPages = permissions.ObjectValue.TotalPages,
                            HasNext = permissions.ObjectValue.HasNext,
                            HasPrevious = permissions.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(permissions);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<PermissionDTO> permission = _permissionService.Get(id);
                return Ok(permission);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAll/{roleId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll(Guid roleId)
        {
            try
            {
                ResponseAPI<List<PermissionDTO>> permissions = _permissionService.Get(permission => permission.RoleId == roleId && permission.Status == Status.Active);
                return Ok(permissions);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllAvailableUserPermissions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllAvailableUserPermissions()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? userId = HttpContext.User.FindFirstValue("Id");
                Guid? UserId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));

                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<UserDTO> user = _userService.Get(new Guid(userId));
                if (user.ObjectValue == null || user.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<PermissionDTO>> permissions = _permissionService.GetAllAvailablePermissionsByUser(user.ObjectValue);

                return Ok(permissions);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Create

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] PermissionDTO permission)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? createUserId = HttpContext.User.FindFirstValue("Id");

                if (createUserId == null)
                {
                    return Unauthorized(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PermissionDTO> response = _permissionService.Create(permission, creatorUserId:new Guid(createUserId), companyId:new Guid(companyId), userType:enumUserType);
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Update

        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] PermissionDTO permission)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? updateUserId = HttpContext.User.FindFirstValue("Id");

                if (updateUserId == null)
                {
                    return Unauthorized(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<PermissionDTO> response = _permissionService.Update(permission, new Guid(updateUserId));
                return Ok(response);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Delete

        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                if (deleteUserId == null)
                {
                    return Unauthorized(new ResponseAPI<PermissionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "Role unauthorized !"
                    });
                }

                ResponseAPI<PermissionDTO> response = _permissionService.Delete(id, new Guid(deleteUserId));
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion
    }
}
