﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Payment.DTO
{
    public class TransactionGroupDTO
    {
        public Guid Id { get; set; }
        public Guid SenderUserId { get; set; }
        public double TotalAmount { get; set; }
        public Guid QrCodeId { get; set; }
        public DateTime TransactionDate { get; set; }
        public TransactionStatus Status { get; set; }
        public List<ReceiverListDTO>? ReceiverList { get; set; } 

    }
}
