using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;

namespace Platform.Dyno.Shared.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";
            
            var response = new ResponseAPI<object>
            {
                StatusCode = HttpStatusCode.InternalServerError,
                ExceptionMessage = "An internal server error occurred"
            };

            switch (exception)
            {
                case ArgumentNullException:
                    response.StatusCode = HttpStatusCode.BadRequest;
                    response.ExceptionMessage = "Invalid request data";
                    break;
                case UnauthorizedAccessException:
                    response.StatusCode = HttpStatusCode.Unauthorized;
                    response.ExceptionMessage = "Unauthorized access";
                    break;
                case KeyNotFoundException:
                    response.StatusCode = HttpStatusCode.NotFound;
                    response.ExceptionMessage = "Resource not found";
                    break;
                case InvalidOperationException:
                    response.StatusCode = HttpStatusCode.BadRequest;
                    response.ExceptionMessage = "Invalid operation";
                    break;
            }

            context.Response.StatusCode = (int)response.StatusCode;
            
            var jsonResponse = JsonConvert.SerializeObject(response, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }
}
