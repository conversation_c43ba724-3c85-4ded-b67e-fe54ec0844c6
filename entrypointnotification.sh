#!/bin/bash
echo "execution du script "
cat /tmp/config.json
cat /app/Configuration/config.json
set -e

# Si un fichier de configuration est monté dans /tmp/config.json, le copier vers l'emplacement attendu
if [ -f /tmp/config.json ]; then
  echo "Copie du fichier de configuration depuis /tmp/config.json vers /app/Configuration/config.json"  >&2
  cp /tmp/config.json /app/Configuration/config.json
fi

# Lancer le processus par défaut de l'image (par exemple dotnet)
exec dotnet Platform.Dyno.Notification.WebAPI.dll
