﻿using Camunda.Worker.Client;
using Camunda.Worker;
using Platform.Dyno.AccessManagement.Business.Bpmn;
using Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice;
using Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder;
using Platform.Dyno.Shared;

namespace Platform.Dyno.AccessManagement.WebAPI.Bpmn
{
    public static class BpmnInstallerInvoice
    {
        public static IServiceCollection AddCamundaInvoice(this IServiceCollection services, string camundaRestApiUri)
        {
            services.AddSingleton(_ => new BpmnService(camundaRestApiUri));
            services.AddHostedService<BpmnProcessDeployService>();

            services.AddExternalTaskClient(client =>
            {
                client.BaseAddress = new Uri(new Platform.Dyno.Shared.Configuration().CamundaAddress);
            });

            services.AddCamundaWorker("SalesInvoiceWorker", 1)
            .AddHandler<AddInstanceToSalesInvoice>()
            .AddHandler<CreateTansaction>()
            .AddHandler<CreateSalesInvoicePDF>()
            .AddHandler<UpdateSalesInvoiceStatusPDFGenerated>()
            .AddHandler<UpdateSalesInvoiceStatusPDFFailed>()
            .AddHandler<SendNotifSIPDFFailed>()
            .AddHandler<LogErrorSalesInvoicePDFFailed>()
            .AddHandler<LogErrorSalesInvoiceEmailFailed>()
            .AddHandler<SendEmailSalesInvoice>()
            .AddHandler<SendNotifSI>()
            .AddHandler<SendNotifTransactionFailed>()
            .AddHandler<UpdateSalesInvoiceStatusEmailFailed>()
            .AddHandler<UpdateSalesInvoiceStatusEmailSent>()
            .AddHandler<UpdateSalesInvoiceStatusTransactionFailed>()
            .AddHandler<UpdateSalesInvoiceStatusTransactionSucceeded>()
            .AddHandler<LogErrorSalesInvoiceTransactionFailed>()
            .AddHandler<UpdateSalesOrderStatusValid>()
            .AddHandler<UpdateSalesOrderStatusInvalid>();

            services.AddHealthChecks();
            return services;
        }
    }
}
