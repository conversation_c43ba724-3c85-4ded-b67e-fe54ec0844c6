﻿using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Pagination
{
    public interface ISortingUtility
    {
        PagedList<TDTO> SortData<TDTO>(PagedParameters pagedParameters, ResponseAPI<PagedList<TDTO>> dtoList) where TDTO : class;
        PagedList<T> FilterData<T>(PagedParameters pagedParameters, ResponseAPI<PagedList<T>> data) where T : class;
    }


}
