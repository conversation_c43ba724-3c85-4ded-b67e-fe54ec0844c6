﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.User;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Notification
{
    public class CompanyNotifEntity
    {
        [ForeignKey("CompanyId")]
        public Guid CompanyId { get; set; }
        public CompanyEntity Company { get; set; } = new CompanyEntity();



        [ForeignKey("NotificationId")]
        public Guid NotificationId { get; set; }
        public SignalRNotificationEntity Notification { get; set; } = new SignalRNotificationEntity();
    }
}
