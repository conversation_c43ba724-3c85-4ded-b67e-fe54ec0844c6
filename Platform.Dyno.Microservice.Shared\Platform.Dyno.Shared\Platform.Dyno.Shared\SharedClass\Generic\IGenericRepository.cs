﻿using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass.Generic
{
    public interface IGenericRepository<T> where T : class
    {
        #region Get
        IList<T> GetAll(Expression<Func<T, bool>>? expression = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            List<string>? includes = null);

        IQueryable<T> GetAllIQueryable(Expression<Func<T, bool>>? expression = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            List<string>? includes = null);

        T? Get(Expression<Func<T, bool>>? expression = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
           List<string>? includes = null);

        T? Find(params object[] keyValues);
        #endregion

        #region Insert
        void Insert(T entity);
        void InsertRange(IEnumerable<T> entities);
        #endregion

        #region Attach
        void Attach(T entity);
        #endregion

        #region Detach
        void Detach(T entity);
        #endregion

        #region Update
        void Update(T entity);
        void UpdateRange(IEnumerable<T> entities);
        #endregion

        #region Delete
        void Delete(Guid id);
        void DeleteByExpression(Expression<Func<T, bool>> expression);
        void DeleteCK(object key1, object key2);
        #endregion

        #region Save
        void Save(T entity);

        void Entry(T entity);
        #endregion
        IDbContextTransaction BeginTransaction();

    }
}
