﻿
using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using static Camunda.Api.Client.Filter.FilterInfo;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("Create_SO_PDF")]
    public class CreateSalesOrderPDF : IExternalTaskHandler
    {
        private readonly ILogger<CreateSalesOrderPDF> _logger;
        private readonly IInvoiceService _invoiceService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly ISalesOrderService _salesOrderService;
        private readonly Configuration _configuration;

        public CreateSalesOrderPDF(
        ILogger<CreateSalesOrderPDF> logger,
        IInvoiceService invoiceService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        Configuration configuration,
        ISalesOrderService salesOrderService)

        {
            _logger = logger;
            _invoiceService = invoiceService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _configuration = configuration;
            _salesOrderService = salesOrderService;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
                //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

                if (salesOrderDTO == null)
                {
                    outputVariables.Add("Result_Create_SO_PDF", new BooleanVariable(false));
                    outputVariables.Add("Error_Create_SO_PDF", new StringVariable("json conversion failed"));

                }
                else
                {
                    var salesOrderHavePDF = _salesOrderService.Get(null, true, salesOrderDTO.Id).ObjectValue?.DocumentId != null && _salesOrderService.Get(null, true, salesOrderDTO.Id).ObjectValue?.DocumentId != Guid.Empty;
                    if (salesOrderHavePDF)
                    {
                        outputVariables.Add("Result_Create_SO_PDF", new BooleanVariable(false));
                        outputVariables.Add("Error_Create_SO_PDF", new StringVariable("document already exist"));
                    }
                    else
                    {
                        var PDFCreationResult = await _invoiceService.CreatePDF(salesOrderDTO.Code,
                        salesOrderDTO.ProductType,
                        salesOrderDTO.DynoAmount,
                        salesOrderDTO.NetAmount,
                        salesOrderDTO.VATAmount,
                        salesOrderDTO.TotalAmount,
                        salesOrderDTO.PaymentMethod,
                        _configurationDefaultId.CompanyId,
                        salesOrderDTO.CompanyId,
                        DocumentType.SalesOrder);

                        if (PDFCreationResult.ObjectValue != null && PDFCreationResult.StatusCode == System.Net.HttpStatusCode.Created)
                        {
                            outputVariables.Add("Result_Create_SO_PDF", new BooleanVariable(true));
                            outputVariables.Add("PDFLink", new StringVariable($"{_configuration.AWSS3URL}/PDFs/Invoice/{PDFCreationResult.ObjectValue.Name}.pdf"));
                            outputVariables.Add("DocumentId", new StringVariable(PDFCreationResult.ObjectValue.Id.ToString()));
                        }
                        else
                        {
                            outputVariables.Add("Result_Create_SO_PDF", new BooleanVariable(false));
                            outputVariables.Add("Error_Create_SO_PDF", new JsonVariable(JsonSerializer.Serialize(PDFCreationResult)));
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_Create_SO_PDF", new BooleanVariable(false));
                outputVariables.Add("Error_Create_SO_PDF", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
