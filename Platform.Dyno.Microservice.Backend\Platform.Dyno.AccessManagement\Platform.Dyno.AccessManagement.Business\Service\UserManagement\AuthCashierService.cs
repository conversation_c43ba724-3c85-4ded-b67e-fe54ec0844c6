﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Authentification;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.DTO.Company;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Payment.DTO;
using System.Security.Cryptography.Xml;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.AccessManagement.DTO.Transaction;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class AuthCashierService : IAuthCashierService
    {
        private readonly IUserService _userService;
        private readonly IMapper _mapper;
        private readonly IMacAddressService _macAddressService;
        private readonly IUserTokenService _userTokenService;
        private readonly IUserOTPService _userOTPService;
        private readonly IRoleService _roleService;
        private readonly ICompanyService _companyService;
        private readonly IHelper<TransactionDTO> _transactionHelper;
        private readonly IHelper<RefundTransactionDTO> _refundTransactionHelper;
        private readonly IHelper<WalletDTO> _walletHelper;
        private readonly IWalletService _walletService;
        private readonly Configuration _configuration;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("cashier");

        public AuthCashierService(IUserService userService, 
            IMapper mapper,
            IMacAddressService macAddressService,
            IUserTokenService userTokenService,
            IUserOTPService userOTPService,
            IRoleService roleService,
            ICompanyService companyService,
            IHelper<TransactionDTO> transactionHelper,
            IHelper<RefundTransactionDTO> refundTransactionHelper,
            IHelper<WalletDTO> walletHelper,
            IWalletService walletService,
            Configuration configuration)
        {
            _userService = userService;
            _mapper = mapper;
            _macAddressService = macAddressService;
            _userTokenService = userTokenService;
            _userOTPService = userOTPService;
            _roleService = roleService;
            _companyService = companyService;
            _transactionHelper = transactionHelper;
            _refundTransactionHelper = refundTransactionHelper;
            _walletHelper = walletHelper;
            _walletService = walletService;
            _configuration = configuration;
        }

        #region Login
        public ResponseAPI<AuthResponseDTO> Login(ClientLoginDTO loginDto, LanguageType language)
        {
            ResponseAPI<List<UserDTO>> userExist = _userService.Get(user =>
                                               user.PhoneNumber == loginDto.PhoneNumber &&
                                               user.CountryCode == loginDto.CountryCode &&
                                               user.Status == Status.Active &&
                                               user.UserType == UserType.Cashier);

            #region Test exception
            if (userExist.ObjectValue == null || userExist.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            if (userExist.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use this phoneNumber !"
                };
            }
            if (userExist.ObjectValue.FirstOrDefault()?.PhoneNumberConfirmed == false)
            {
                ResponseAPI<UserOTPDTO> createCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = userExist.ObjectValue.FirstOrDefault()?.Id,
                    IsConfirmed = false
                });

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Redirect,
                    ExceptionMessage = $"Account Not Confirmed: Please enter your OTP code : {createCode.ObjectValue?.Code}"
                };
            }
            #endregion

            UserBM user = _mapper.Map<UserBM>(userExist.ObjectValue?.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            var passwordHasher = new PasswordHasher<UserEntity>();
            bool isCorrectPassword = passwordHasher.VerifyHashedPassword(userEntity, userEntity.PasswordHash, loginDto.Password) == PasswordVerificationResult.Success ? true : false;

            if (isCorrectPassword)
            {
                ResponseAPI<UserTokenDTO> userToken = _userTokenService.Create(new UserTokenDTO
                {
                    Token = AuthManagement.CreateToken(userExist.ObjectValue?.FirstOrDefault(), null, null,
                    _configuration.Key, _configuration.TokenLifeTime, _configuration.Issuer),
                    RefreshToken = AuthManagement.GenerateRefreshToken(),
                    ExpiredDate = DateTime.Now.ToUniversalTime().AddDays(Convert.ToDouble(_configuration.RefreshTokenLifeTime))

                });

                #region MacAddress Verification

                List<MacAddressDTO>? userMacAddresses = userExist.ObjectValue?.FirstOrDefault()?.MacAddresses?.ToList();

                int? code = 0;
                if (userMacAddresses != null && userMacAddresses.Count > 0)
                {
                    (bool, int?) verificationResponse = VerificationMacAddress(userMacAddresses, loginDto.MacAddress, user.Id);
                    code = verificationResponse.Item2;
                    if (verificationResponse.Item1)
                    {
                        return new ResponseAPI<AuthResponseDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.OK,
                            ObjectValue = new AuthResponseDTO
                            {
                                Token = userToken.ObjectValue?.Token,
                                RefreshToken = userToken.ObjectValue?.RefreshToken,
                                ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                                UserProfile = new UserProfileDTO
                                {
                                    FullName = user.FullName,
                                    Email = user.Email,
                                    DateOfBirth = user.DateOfBirth,
                                    PhoneNumber = user.PhoneNumber,
                                    CountryCode = user.CountryCode,
                                    Gender= user.Gender,
                                    Picture = user.Picture
                                }
                            }
                        };
                    }
                }
                return new ResponseAPI<AuthResponseDTO>
                {

                    StatusCode = System.Net.HttpStatusCode.Redirect,
                    ExceptionMessage = "You are connecting with new device : Please check your phone number for a " +
                    "confirmation code and follow the instructions to confirm your account " +
                    $"before logging in. code : {code}",
                    ObjectValue = new AuthResponseDTO
                    {
                        Token = userToken.ObjectValue?.Token,
                        RefreshToken = userToken.ObjectValue?.RefreshToken,
                        ExpiredDate = userToken.ObjectValue?.ExpiredDate,
                        UserProfile = new UserProfileDTO
                        {
                            FullName = user.FullName,
                            Email = user.Email,
                            DateOfBirth = user.DateOfBirth,
                            PhoneNumber = user.PhoneNumber,
                            Picture = user.Picture
                        }
                    }
                };

                #endregion
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "Incorrect Credentials: Please try again."
            };
        }
        #endregion

        #region Create

        private string GenerateBadge(string companyName)
        {
            Random random = new Random();
            int code = random.Next(1000, 10000);
            return $"Cashier-{companyName}-{code}";
        }

        public ResponseAPI<UserDTO> Add(Guid? createdUserId, Guid companyId)
        {
            CompanyDTO? company = _companyService.Get(companyId).ObjectValue;
            if(company == null)
            {
                return new ResponseAPI<UserDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Company not found"
                };
            }
            RoleDTO? cashierRole = _roleService.Get(role => role.Name == DefaultRoles.Cashier).ObjectValue?.FirstOrDefault();

            if (cashierRole == null)
            {
                ResponseAPI<RoleDTO> createCashierRole = _roleService.Create(new RoleDTO
                {
                    Name = DefaultRoles.Cashier
                });

                if (createCashierRole != null && createCashierRole.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    cashierRole = createCashierRole.ObjectValue;
                };

                return new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = createCashierRole?.ExceptionMessage
                };
            }

            cashierRole.Users = null;

            string userName = GenerateBadge(company.Name);

            UserDTO userDTO = new UserDTO
            {
                CompanyId = companyId,
                CreationTime = DateTime.UtcNow,
                CreatorUserId = createdUserId,
                UserName = userName,
                NormalizedUserName = userName.ToUpper(),
                Status = Status.Active,
                UserType = UserType.Cashier,
                CountryCode = ""
            };
            userDTO.Roles.Add(cashierRole);
            ResponseAPI<UserDTO> createdUser = _userService.Create(userDTO, createdUserId, companyId: companyId);
            if(createdUser.StatusCode != HttpStatusCode.Created)
            {
                return new ResponseAPI<UserDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessages.CreateError
                };
            }
            string pincode = "0000";
            string Url = $"{_configuration.PaymentAddress}/Api/Wallet/Create/{pincode}";
            var walletDTO = new WalletDTO()
            {
                AssignedToId = createdUser.ObjectValue?.Id ?? Guid.Empty,
                AssignedToType = UserType.Cashier,
            };
            ResponseAPI<WalletDTO>? response = _walletHelper.Post(Url, walletDTO);
            if (response == null || response.StatusCode != HttpStatusCode.Created)
            {
                UserDTO? deletedUser = _userService.Remove(createdUser.ObjectValue?.Id ?? Guid.Empty).ObjectValue;
                return new ResponseAPI<UserDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = exceptionMessages.CreateError
                };
            };

            return createdUser;
        }

        public ResponseAPI<AuthResponseDTO> Register(ClientRegisterDTO registerDto)
        {
            List<UserDTO>? cashiers = _userService.Get(user => user.UserName == registerDto.UserName).ObjectValue;

            if(cashiers == null || cashiers.Count() < 0) 
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            if(cashiers.FirstOrDefault() == null)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }



            #region Updated Data
            UserDTO cashier = cashiers.FirstOrDefault() ?? new UserDTO();
            cashier.Addresses = (IList<AddressDTO>?)registerDto.Addresses;
            cashier.PhoneNumber = registerDto.PhoneNumber;
            cashier.Email = registerDto.Email;  
            cashier.NormalizedEmail = registerDto.Email.ToUpper();
            cashier.DateOfBirth = registerDto.DateOfBirth;
            cashier.FullName = registerDto.FullName;
            cashier.Gender = registerDto.Gender;
            cashier.UserType = UserType.Cashier;
            cashier.CountryCode = registerDto.CountryCode;
            var passwordHasher = new PasswordHasher<UserDTO>();
            cashier.Password = passwordHasher.HashPassword(cashier, registerDto.Password);
            #endregion

            UserDTO? updatedCashier = _userService.Update(cashier).ObjectValue;
            ResponseAPI<UserOTPDTO> createCode = new ResponseAPI<UserOTPDTO>();
            if (updatedCashier != null)
            {
                ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                {
                    MacAddress = registerDto.MacAddress ?? "",
                    IsConfirmed = false,
                    UserId = updatedCashier.Id
                });
                createCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = updatedCashier.Id,
                    IsConfirmed = false
                });

                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = createCode.StatusCode,
                    ExceptionMessage = createCode.ExceptionMessage + $"{createCode.ObjectValue?.Code}",
                    ObjectValue = new AuthResponseDTO
                    {
                        UserProfile = new UserProfileDTO
                        {
                            Id = updatedCashier.Id,
                            FullName = updatedCashier.FullName,
                            Gender = updatedCashier.Gender,
                            Email = updatedCashier.Email,
                            PhoneNumber = updatedCashier.PhoneNumber,
                            Picture = updatedCashier.Picture,
                            DateOfBirth = updatedCashier.DateOfBirth,
                        }
                    }
                };
            }

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = HttpStatusCode.BadRequest,
                ExceptionMessage = exceptionMessages.CreateError
            };
        }

        #endregion

        #region Check
        public ResponseAPI<PhoneNumberDTO> CheckCashierBadge(string badge)
        {
            List<UserDTO>? cashiers = _userService.Get(user => user.UserName == badge).ObjectValue;
            if(cashiers != null && cashiers.Count() > 0) 
            {
                if(string.IsNullOrEmpty(cashiers.FirstOrDefault()?.Password))
                {
                    return new ResponseAPI<PhoneNumberDTO>
                    {
                        StatusCode = HttpStatusCode.OK
                    };
                }

                return new ResponseAPI<PhoneNumberDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = new PhoneNumberDTO
                    {
                        PhoneNumber = cashiers.FirstOrDefault()?.PhoneNumber ?? string.Empty,
                        CountryCode = cashiers.FirstOrDefault()?.CountryCode ?? string.Empty
                    }
                };

            }


            return new ResponseAPI<PhoneNumberDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "The Badget code is wrong !"
            };
        }
        #endregion

        #region Private methode
        private (bool, int?) VerificationMacAddress(List<MacAddressDTO> macAddresses, string userMacAddress, Guid userId)
        {
            bool macAddressExist = false;
            bool macAddressConfirmed = false;
            foreach (var macAdress in macAddresses)
            {
                if (macAdress.MacAddress == userMacAddress && macAdress.IsConfirmed == true)
                {
                    macAddressConfirmed = true;
                }
                if (macAdress.MacAddress == userMacAddress)
                    macAddressExist = true;
            }

            if (!macAddressExist)
            {
                ResponseAPI<MacAddressDTO> macAddressCreate = _macAddressService.Create(new MacAddressDTO
                {
                    MacAddress = userMacAddress,
                    IsConfirmed = false,
                    UserId = userId
                });

            }

            ResponseAPI<UserOTPDTO> sendOTPCode = new ResponseAPI<UserOTPDTO>();
            if (!macAddressConfirmed)
            {
                sendOTPCode = _userOTPService.SendOTP(new UserOTPDTO
                {
                    UserId = userId,
                    IsConfirmed = false
                });
            }

            return (macAddressConfirmed, sendOTPCode.ObjectValue?.Code);
        }



        #endregion

        #region Get
        public ResponseAPI<List<CashierWalletDTO>> GetCashierWallet(Guid id)
        {
            ResponseAPI<List<UserDTO>> userExist = _userService.Get(user =>
                                               user.Id == id &&
                                               user.Status == Status.Active &&
                                               user.UserType == UserType.Cashier);

            #region Test exception
            if (userExist.ObjectValue == null || userExist.ObjectValue.Count == 0)
            {
                return new ResponseAPI<List<CashierWalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            #endregion

            ResponseAPI<List<WalletDTO>>? wallets =
                _walletHelper.GetAll($"{_configuration.PaymentAddress}/Api/Wallet/GetUserWallets"
                    , new Dictionary<string, string> { { "userId", id.ToString() } });

            if (wallets == null
                || wallets.StatusCode != HttpStatusCode.OK
                || wallets.ObjectValue == null
                || wallets.ObjectValue.Count <= 0)
            {
                return new ResponseAPI<List<CashierWalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User is Unauthorized"
                };
            }
            WalletDTO wallet = wallets.ObjectValue?.FirstOrDefault() ?? new WalletDTO();
            ResponseAPI<List<TransactionDTO>>? receiverTransaction =
                _transactionHelper.GetAll($"{_configuration.PaymentAddress}/Api/Transaction/GetWalletReceivedTransactions",
                new Dictionary<string, string>
                {
                        {"walletId", wallet.Id.ToString()},
                        {"sort","TransactionDate" },
                        { "order", "DESC" }
                });

            if (receiverTransaction == null
                || receiverTransaction.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<List<CashierWalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Internal server error in payment microservice"
                };
            }

            ResponseAPI<List<TransactionDTO>>? senderTransaction =
                _transactionHelper.GetAll($"{_configuration.PaymentAddress}/Api/Transaction/GetWalletSentTransactions",
                new Dictionary<string, string>
                {
                        {"walletId", wallet.Id.ToString()},
                        {"sort","TransactionDate" },
                        { "order", "DESC" }
                });

            if (senderTransaction == null
                || senderTransaction.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<List<CashierWalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Internal server error in payment microservice"
                };
            }

            ResponseAPI<List<RefundTransactionDTO>>? refundTransaction =
                _refundTransactionHelper.GetAll($"{_configuration.PaymentAddress}/Api/RefundTransaction/GetUserRefundTransactions/{id}");

            if (refundTransaction == null
                || refundTransaction.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<List<CashierWalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "Internal server error in payment microservice"
                };
            }

            List<CashierWalletDTO> cashiersWallet = new List<CashierWalletDTO> 
            { 
                    new CashierWalletDTO
                    {
                        Id = wallet.Id,
                        Balance = receiverTransaction.ObjectValue?.Sum(x => x.Amount) ?? 0,
                        Type = CashierWalletType.Received
                    },
                    new CashierWalletDTO
                    {
                        Id = wallet.Id,
                        Balance = senderTransaction.ObjectValue?.Sum(x => x.Amount) ?? 0,
                        Type = CashierWalletType.Sended
                    },
                    new CashierWalletDTO
                    {
                        Id = wallet.Id,
                        Balance = refundTransaction.ObjectValue?.Sum(x => x.Amount) ?? 0,
                        Type = CashierWalletType.Refunded
                    }
            };
            return new ResponseAPI<List<CashierWalletDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = cashiersWallet
            };
        }

        #endregion

        #region Forget Password
        public ResponseAPI<AuthResponseDTO> ForgetPassword(string phoneNumber, string? countryCode = null)
        {
            ResponseAPI<List<UserDTO>> responseUser = _userService.Get(user => user.PhoneNumber == phoneNumber &&
            user.CountryCode == countryCode &&
            user.PhoneNumberConfirmed == true &&
            user.UserType == UserType.Cashier &&
            user.Status == Status.Active);

            if (responseUser == null ||
                responseUser.ObjectValue == null ||
                responseUser.ObjectValue.Count == 0)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorize"
                };
            }

            if (responseUser.ObjectValue.Count > 1)
            {
                return new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "There is more than user use the same phone Number," +
                    " please contact our system!"
                };
            }
            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            ResponseAPI<UserOTPDTO> createCode = _userOTPService.SendOTP(new UserOTPDTO
            {
                UserId = user.Id,
                IsConfirmed = false
            });

            return new ResponseAPI<AuthResponseDTO>
            {
                StatusCode = createCode.StatusCode,
                ExceptionMessage = createCode.ExceptionMessage = $"The code is : {createCode.ObjectValue?.Code}",
            };
        }
        #endregion
    }
}
