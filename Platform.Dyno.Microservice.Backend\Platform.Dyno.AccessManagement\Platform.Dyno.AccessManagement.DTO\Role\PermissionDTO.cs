﻿using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Platform.Dyno.AccessManagement.DTO.Role
{
    public class PermissionDTO : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsLeaf { get; set; } = false;
        public bool IsRoot { get; set; } = false;
        public bool? PartialSelected { get; set; }
        public bool? IsSelectable { get; set; }
        #endregion

        #region Structure
        [JsonIgnore]
        public RoleDTO? Role { get; set; }
        public Guid? RoleId { get; set; }
        public ICollection<PermissionDTO>? Permissions { get; set; }
        #endregion
    }
}
