﻿using Microsoft.AspNetCore.Authorization;


namespace Platform.Dyno.Shared.Authorization
{
    public class PermissionsRequirement : IAuthorizationRequirement
    {
        public string? Permission { get; private set; }
        public string? Role { get; private set; }

        public PermissionsRequirement(string? permission, string? role)
        {
            Permission = permission;
            Role = role;
        }
    }
}
