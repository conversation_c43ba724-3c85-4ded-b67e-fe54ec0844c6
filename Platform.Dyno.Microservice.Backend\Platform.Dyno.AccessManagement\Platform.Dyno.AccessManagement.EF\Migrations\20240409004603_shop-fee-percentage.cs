﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class shopfeepercentage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("046d7d2b-ff90-4839-90aa-46dcd6f54ad9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("162cd37b-21b9-41b2-a2f9-3f1d9611a07b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("287b5fc9-6062-4cc5-991a-b010921ea6e7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("299f2262-279b-4d6c-97e6-38aa26c4f37e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("322d408e-d544-4743-a4aa-8b9a668e586e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("36f66cd3-1563-4140-a386-a50bf894a5f9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3778d987-4c05-4c59-9365-19a5fa63f1ce"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("607da380-1ae9-4844-aaed-587c24091fff"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("62d05ae4-f854-4762-8207-8a4c7af55f3e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("63754930-a8fc-434b-b164-6a03f36225e4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("67419b99-77f6-4655-90d8-6e3f226a3550"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("70437bb0-e11a-4387-bc8b-063a0903c803"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7f60991c-53a1-489e-8848-9ca0450bd78e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8386f535-125a-42f6-88a4-330ab51c9166"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8ab6dba2-b83a-4d0b-9150-91cc0ee438f0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("99d99f55-5839-4f61-8626-1a5c2fcc917f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a61868a3-0318-4571-b06e-90e89f6c80c5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bf4e2d00-24a5-4abf-9b5a-c4e8af3261be"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c8febbbf-a513-4662-80d5-0d583501eb6f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cb0011a6-bb2c-4129-81c1-1827650a128f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("df684c29-6c99-40dd-8819-1de22ba3e1d1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("dfdd94c0-cb56-40bd-babb-fb2d3a969c19"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ee5c5c0d-b231-4ff6-b704-5110b5020334"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f81d5ac1-04f9-4c91-ab1c-15d3d62839dc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("60c0e790-6df7-470d-9431-42047b74de5d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57a5d357-c1c3-40e9-90c0-415d0a91bacc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8cee2e65-6d23-46e8-a905-d5f12ab29dee"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d9ca0c41-bdf6-45af-8570-5b9bdf2f48d6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fa5230db-847b-4a6c-a6e8-e0052fd386d0"));

            migrationBuilder.RenameColumn(
                name: "FeePercentage",
                table: "PaymentDetails",
                newName: "ShopFeePercentage");

            migrationBuilder.RenameColumn(
                name: "FeePercent",
                table: "Company",
                newName: "ClientFeePercentage");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fc33819a-cf6b-4aac-992c-1338ea50fd38", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9383) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a9aa4b2a-4664-4f4e-a5c5-54717044811b", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9364) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "25f0821f-2bfa-43e3-ac83-c96eee0444bd", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9372) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "323713be-f909-4f95-b60c-46cf2ce9b7bc", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9391) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "d56c2296-2579-4015-b787-923dbe943402", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9380) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "099c10ca-9442-4dc6-ad9d-74091b78e997", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9376) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "93f5f46e-0cbd-4b07-a97d-24f606028e41", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669), new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669), "AQAAAAEAACcQAAAAEHqL+5v6qGBES5d8090am7wSb/dCyRynxjXBvY03Pr/EDGlf6IhcpIMrh/G8GnlqUQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1db3f662-80aa-4373-83b3-1a040b312965"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2bcaaff9-1c46-4d36-9363-5dded74fda55"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2f9facb1-2291-4acd-a753-dc1fbeca5a12"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3666fb12-a9cf-46e4-9282-55f01a0e3ee4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("37bbff10-04c0-41e9-b3e7-1c8949e64df6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("50f3274f-c7ed-48fb-88c8-e6f1c642de89"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57189e50-82c8-4eac-a604-8ffcbd0d7142"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("58bef5e1-79ce-45c6-9575-be103795d67c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5ddd7d9c-5a4c-4c2a-92bf-a24119b0ce7e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("608608e2-7edf-4599-8b11-ef9c2f65e841"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6b735ebc-a774-4da1-8ff0-6de78332c746"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7bbee4f9-1a50-4a07-a14a-37861c129811"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8b5018c2-e58b-46d7-aa86-c23a299e2ea1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9d1d7b60-446c-4e14-9a25-fbed8bc96dad"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a890dfaf-cd23-4f56-9b8d-049cb6636be3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b5c9b1a2-7fc7-4566-b85a-4c4a7707375b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b7223cc0-774b-4ba5-9fe4-117f1de8ba43"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("cf66edaa-cb00-4524-a0a1-bac0a4d2dae2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ded1362c-7406-410e-a8ad-1f1c106d7cfa"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e4b7bc84-bbf2-423a-8614-59e7e5b3e3d8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f78763e2-5d85-41cd-aa61-fa6d34add3dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("fa58580f-446b-4b2f-80c7-117cd5e1256f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fc05ec08-2bff-4e84-9b83-aa77f3399b9a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fcae0b52-9667-4abb-b561-c8b39ac1cc90"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1db3f662-80aa-4373-83b3-1a040b312965"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2bcaaff9-1c46-4d36-9363-5dded74fda55"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2f9facb1-2291-4acd-a753-dc1fbeca5a12"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3666fb12-a9cf-46e4-9282-55f01a0e3ee4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("37bbff10-04c0-41e9-b3e7-1c8949e64df6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50f3274f-c7ed-48fb-88c8-e6f1c642de89"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57189e50-82c8-4eac-a604-8ffcbd0d7142"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("58bef5e1-79ce-45c6-9575-be103795d67c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ddd7d9c-5a4c-4c2a-92bf-a24119b0ce7e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("608608e2-7edf-4599-8b11-ef9c2f65e841"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6b735ebc-a774-4da1-8ff0-6de78332c746"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7bbee4f9-1a50-4a07-a14a-37861c129811"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8b5018c2-e58b-46d7-aa86-c23a299e2ea1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9d1d7b60-446c-4e14-9a25-fbed8bc96dad"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a890dfaf-cd23-4f56-9b8d-049cb6636be3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b5c9b1a2-7fc7-4566-b85a-4c4a7707375b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7223cc0-774b-4ba5-9fe4-117f1de8ba43"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cf66edaa-cb00-4524-a0a1-bac0a4d2dae2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ded1362c-7406-410e-a8ad-1f1c106d7cfa"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e4b7bc84-bbf2-423a-8614-59e7e5b3e3d8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f78763e2-5d85-41cd-aa61-fa6d34add3dd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fa58580f-446b-4b2f-80c7-117cd5e1256f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fc05ec08-2bff-4e84-9b83-aa77f3399b9a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fcae0b52-9667-4abb-b561-c8b39ac1cc90"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"));

            migrationBuilder.RenameColumn(
                name: "ShopFeePercentage",
                table: "PaymentDetails",
                newName: "FeePercentage");

            migrationBuilder.RenameColumn(
                name: "ClientFeePercentage",
                table: "Company",
                newName: "FeePercent");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("57a5d357-c1c3-40e9-90c0-415d0a91bacc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8cee2e65-6d23-46e8-a905-d5f12ab29dee"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d9ca0c41-bdf6-45af-8570-5b9bdf2f48d6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fa5230db-847b-4a6c-a6e8-e0052fd386d0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "7e45c4f7-8918-45db-9f9a-4c7b55f9c6e9", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3520) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "142b79e6-3b10-40da-889a-e08d71c0aba5", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3498) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fee828e5-68c6-4136-bbd3-f79d3a8a770e", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3507) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1074c234-9573-4ea3-87bd-fd5de793f8aa", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3524) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1721260c-405f-4c43-a54e-22a59a7775b1", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3516) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "ca6315d4-edad-472b-b3f7-8ce0b8f831fa", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3512) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "3d9db625-f43e-4cb8-8686-b980c9d14f70", new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3879), new DateTime(2024, 4, 3, 5, 9, 52, 987, DateTimeKind.Utc).AddTicks(3879), "AQAAAAEAACcQAAAAEAZdRzMxE1S+QTuNvi1rtCIJ7DoRo7xJuEJkRZ/qK6/kSjd7e7VoOmscYOZU07nkxw==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("8cee2e65-6d23-46e8-a905-d5f12ab29dee"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("fa5230db-847b-4a6c-a6e8-e0052fd386d0"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("60c0e790-6df7-470d-9431-42047b74de5d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("8cee2e65-6d23-46e8-a905-d5f12ab29dee"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("57a5d357-c1c3-40e9-90c0-415d0a91bacc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("d9ca0c41-bdf6-45af-8570-5b9bdf2f48d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("57a5d357-c1c3-40e9-90c0-415d0a91bacc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("046d7d2b-ff90-4839-90aa-46dcd6f54ad9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("162cd37b-21b9-41b2-a2f9-3f1d9611a07b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("287b5fc9-6062-4cc5-991a-b010921ea6e7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("299f2262-279b-4d6c-97e6-38aa26c4f37e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("322d408e-d544-4743-a4aa-8b9a668e586e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("36f66cd3-1563-4140-a386-a50bf894a5f9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("3778d987-4c05-4c59-9365-19a5fa63f1ce"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("60c0e790-6df7-470d-9431-42047b74de5d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("607da380-1ae9-4844-aaed-587c24091fff"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("62d05ae4-f854-4762-8207-8a4c7af55f3e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("63754930-a8fc-434b-b164-6a03f36225e4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("67419b99-77f6-4655-90d8-6e3f226a3550"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("70437bb0-e11a-4387-bc8b-063a0903c803"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7f60991c-53a1-489e-8848-9ca0450bd78e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8386f535-125a-42f6-88a4-330ab51c9166"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("0fdef61d-6556-49bc-9561-4a0c2dd3b727"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8ab6dba2-b83a-4d0b-9150-91cc0ee438f0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("99d99f55-5839-4f61-8626-1a5c2fcc917f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("60c0e790-6df7-470d-9431-42047b74de5d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("a61868a3-0318-4571-b06e-90e89f6c80c5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("615e7e70-666e-4ee0-85ed-58e8adc77625"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("bf4e2d00-24a5-4abf-9b5a-c4e8af3261be"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("60c0e790-6df7-470d-9431-42047b74de5d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c8febbbf-a513-4662-80d5-0d583501eb6f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("60c0e790-6df7-470d-9431-42047b74de5d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("cb0011a6-bb2c-4129-81c1-1827650a128f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("df684c29-6c99-40dd-8819-1de22ba3e1d1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("dfdd94c0-cb56-40bd-babb-fb2d3a969c19"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("9b43d66d-3981-4528-a5c6-fc88861eff05"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ee5c5c0d-b231-4ff6-b704-5110b5020334"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("9a59a171-5959-4693-ac25-a5fafa7b7795"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f81d5ac1-04f9-4c91-ab1c-15d3d62839dc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("07b58256-4328-48ee-b9ff-f7300e25298c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }
    }
}
