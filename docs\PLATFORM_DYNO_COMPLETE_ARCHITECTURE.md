# 🏗️ ARCHITECTURE COMPLÈTE PLATFORM DYNO - SPÉCIFICATIONS FINALES

## 🎯 **FLUX MÉTIER COMPLET VALIDÉ**

Cette documentation présente l'architecture complète et validée de Platform Dyno avec tous les acteurs, interfaces et flux métier.

## 👥 **ACTEURS ET INTERFACES**

### **👑 SuperAdmin**
- **Interface** : Web (Angular)
- **APIs** : `/api/AuthAdmin/*` 
- **Authentification** : Email + mot de passe
- **Responsabilités** :
  - Créer les entreprises
  - Créer les commerçants  
  - Recevoir virements/chèques des entreprises (système externe banque)
  - Convertir argent réel → tokens
  - Alimenter les wallets globaux des entreprises

### **🏢 Entreprise (Employés avec droits)**
- **Interface** : Web (Angular)
- **APIs** : `/api/AuthAdmin/*`
- **Authentification** : Email + mot de passe
- **Responsabilités** :
  - Alimenter wallet global (virement/chèque → SuperAdmin)
  - Créer/gérer des groupes d'employés
  - Modifier détails des employés
  - Distribuer tickets (restaurant/cadeaux) vers employés/groupes
  - Gestion des rôles et permissions

### **👤 Employé**
- **Interface** : Mobile (Flutter)
- **APIs** : `/api/AuthClient/*` via AccessManagement
- **Authentification** : Numéro téléphone + adresse MAC
- **Validation** : OTP si nouvelle adresse MAC
- **Responsabilités** :
  - Consulter ses wallets par type (restaurant, cadeaux)
  - Générer QR codes pour paiements
  - Scanner QR codes commerçants
  - Effectuer transactions avec commerçants

### **🏪 Commerçant**
- **Interface** : Web (Angular)
- **APIs** : `/api/AuthAdmin/*`
- **Authentification** : Email + mot de passe
- **Responsabilités** :
  - Gérer flux et cash-out
  - Générer QR codes avec montant
  - Recevoir paiements employés (automatique)

### **💰 Caissier (FUTUR)**
- **Interface** : Mobile (Flutter) - Application distincte
- **APIs** : À définir
- **Responsabilités** : Traitement paiements pour commerçants

## 💰 **FLUX DES TOKENS**

### **🔄 Cycle Complet :**
```
1. 🏢 Entreprise → 💳 Virement/Chèque → 🏦 Banque
2. 👑 SuperAdmin → 🔄 Conversion → 💰 Tokens → 🏢 Wallet Entreprise
3. 🏢 Entreprise → 🎫 Distribution Tickets → 👤 Wallets Employés
4. 👤 Employé → 💳 Transaction → 🏪 Commerçant
5. 🏪 Commerçant → 💸 Cash-out → 🏦 Banque
```

### **🎫 Types de Tickets :**
- **Tickets Restaurant** → Wallet Type 0
- **Tickets Cadeaux** → Wallet Type 1
- **Autres types** → Wallet Types 2, 3

## 🔐 **AUTHENTIFICATION PAR INTERFACE**

### **🌐 Interfaces Web (Angular) :**
```http
# SuperAdmin, Entreprises, Commerçants
POST /api/AuthAdmin/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### **📱 Interfaces Mobile (Flutter) :**
```http
# Employés uniquement
POST /api/AuthClient/login
{
  "phoneNumber": "+33123456789",
  "password": "password123",
  "countryCode": "+33",
  "macAddress": "AA:BB:CC:DD:EE:FF"
}
```

**Validation MAC :** Si nouvelle adresse MAC → Processus OTP automatique

## 📋 **ENDPOINTS PAR ACTEUR**

### **👑 SuperAdmin (AuthAdmin) :**
```http
# Gestion entreprises
POST /api/Company/Create
GET  /api/Company/GetAll
PUT  /api/Company/Update

# Gestion commerçants  
POST /api/Merchant/Create
GET  /api/Merchant/GetAll

# Alimentation wallets entreprises
POST /api/Wallet/FundCompanyWallet
GET  /api/Transaction/GetFundingHistory
```

### **🏢 Entreprise (AuthAdmin) :**
```http
# Gestion employés
POST /api/Employee/Create
PUT  /api/Employee/Update
GET  /api/Employee/GetByCompany
DELETE /api/Employee/Delete

# Gestion groupes
POST /api/EmployeeGroup/Create
PUT  /api/EmployeeGroup/Update
GET  /api/EmployeeGroup/GetByCompany

# Distribution tickets
POST /api/Ticket/DistributeToEmployee
POST /api/Ticket/DistributeToGroup
GET  /api/Ticket/GetDistributionHistory

# Gestion rôles (déjà implémenté)
GET  /api/Role/GetAll
POST /api/UserRole/Assign
```

### **👤 Employé (AuthClient via AccessManagement) :**
```http
# Authentification avec MAC
POST /api/AuthClient/login
POST /api/AuthClient/VerifyOTPCode  # Si nouvelle MAC

# Wallets par type
GET  /api/Wallet/GetMyWallets
GET  /api/Wallet/GetMyWalletsByType?type=0  # Restaurant
GET  /api/Wallet/GetMyWalletsByType?type=1  # Cadeaux

# Transactions QR Code
POST /api/Transaction/CreateFromQR
POST /api/QRCode/Generate  # Générer QR pour paiement
POST /api/QRCode/Scan      # Scanner QR commerçant

# Historique
GET  /api/Transaction/GetMyTransactions
```

### **🏪 Commerçant (AuthAdmin) :**
```http
# Gestion transactions
GET  /api/Transaction/GetMerchantTransactions
GET  /api/Transaction/GetDailyReport

# QR Code pour paiements
POST /api/QRCode/GeneratePaymentRequest
GET  /api/QRCode/GetActiveRequests

# Cash-out
POST /api/Cashout/Request
GET  /api/Cashout/GetHistory
GET  /api/Cashout/GetBalance
```

## 🎯 **SCÉNARIOS DE TRANSACTION QR CODE**

### **Scénario 1 : Employé initie le paiement**
```
1. 👤 Employé → Génère QR code avec montant
2. 🏪 Commerçant → Scanne QR code
3. 🔄 Transaction automatique (sans validation manuelle)
4. ✅ Confirmation instantanée
```

### **Scénario 2 : Commerçant initie le paiement**
```
1. 🏪 Commerçant → Génère QR code avec montant
2. 👤 Employé → Scanne QR code
3. 👤 Employé → Confirme le paiement
4. 🔄 Transaction automatique
5. ✅ Confirmation instantanée
```

## 🔧 **TYPES DE WALLETS (WalletType)**

```dart
enum WalletType {
  Restaurant = 0,    // Tickets restaurant
  Cadeaux = 1,       // Tickets cadeaux
  Transport = 2,     // Tickets transport (futur)
  Culture = 3        // Tickets culture (futur)
}
```

## 🏗️ **ARCHITECTURE TECHNIQUE**

### **🌐 Frontend Angular (Web) :**
```
📍 Localisation: C:\Dyno\DynoCode\dynoworkspace-dyno.angular-bc8ab99096ab
👥 Utilisateurs: SuperAdmin, Entreprises, Commerçants
🔐 APIs: AuthAdmin
📧 Auth: Email + Password
```

### **📱 Frontend Flutter (Mobile) :**
```
👥 Utilisateurs: Employés
🔐 APIs: AuthClient via AccessManagement
📱 Auth: Phone + MAC Address + OTP
🎯 Point d'entrée: AccessManagement (Port 7274)
```

### **🔄 Backend Services :**
```
🌐 AccessManagement (7274) - Point d'entrée mobile
💰 Payment (7018) - Transactions et wallets
🔔 Notification (7038) - Notifications temps réel
📊 Autres microservices
```

## 🎯 **PRIORITÉS DE DÉVELOPPEMENT**

### **Phase 1 : Mobile Employés (Actuel)**
- ✅ Authentification avec MAC + OTP
- ✅ Consultation wallets par type
- ✅ Génération/scan QR codes
- ✅ Transactions automatiques

### **Phase 2 : Gestion Tickets (Entreprises)**
- 🔄 Interface distribution tickets
- 🔄 Gestion groupes employés
- 🔄 Historique distributions

### **Phase 3 : Cash-out Commerçants**
- ⏳ Interface gestion cash-out
- ⏳ Rapports et analytics
- ⏳ Intégration bancaire

### **Phase 4 : Mobile Caissiers (Futur)**
- ⏳ Application Flutter distincte
- ⏳ APIs dédiées caissiers
- ⏳ Gestion avancée paiements

## ✅ **VALIDATION ARCHITECTURE**

Cette architecture assure :
- ✅ **Séparation claire** des responsabilités
- ✅ **Sécurité** avec authentification adaptée par interface
- ✅ **Évolutivité** avec microservices
- ✅ **Traçabilité** complète des flux financiers
- ✅ **Flexibilité** pour différents types de tickets
- ✅ **Automatisation** des transactions QR code

**L'architecture est maintenant complètement clarifiée et prête pour le développement !**
