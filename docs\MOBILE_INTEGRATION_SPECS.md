# 📱 SPÉCIFICATIONS D'INTÉGRATION MOBILE - PLATFORM DYNO

## 🎯 **CONFIGURATION ENVIRONNEMENTS**

### **URLs de Base**
```typescript
const API_CONFIG = {
  production: {
    accessManagement: 'https://api.dyno-motiva.com',
    payment: 'https://payment-api.dyno-motiva.com',
    notification: 'https://notification-api.dyno-motiva.com'
  },
  staging: {
    accessManagement: 'https://staging-api.dyno-motiva.com',
    payment: 'https://staging-payment-api.dyno-motiva.com',
    notification: 'https://staging-notification-api.dyno-motiva.com'
  },
  development: {
    accessManagement: 'http://localhost:7274',
    payment: 'http://localhost:7018',
    notification: 'http://localhost:7038'
  }
};
```

## 🔐 **AUTHENTIFICATION JWT**

### **Structure du Token**
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "Id": "user-uuid",
    "Company": "company-uuid",
    "UserType": "Client|Company|Admin",
    "Role": "Employee|Manager|Admin",
    "Permission": ["ReadTransactions", "CreateTransactions"],
    "exp": 1640995200,
    "iat": 1640908800
  }
}
```

### **Gestion des Tokens**
```typescript
interface AuthTokens {
  token: string;
  refreshToken: string;
  expiredDate: string;
}

class AuthManager {
  private tokens: AuthTokens | null = null;
  
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await api.post('/api/AuthClient/login', credentials);
    this.tokens = response.data.objectValue;
    this.scheduleTokenRefresh();
    return response.data;
  }
  
  async refreshToken(): Promise<void> {
    if (!this.tokens) throw new Error('No tokens available');
    
    const response = await api.post('/api/AuthClient/RefreshToken', {
      token: this.tokens.token,
      refreshToken: this.tokens.refreshToken,
      expiredDate: this.tokens.expiredDate
    });
    
    this.tokens = response.data.objectValue;
  }
  
  private scheduleTokenRefresh(): void {
    const expiryTime = new Date(this.tokens!.expiredDate).getTime();
    const refreshTime = expiryTime - (5 * 60 * 1000); // 5 minutes before expiry
    
    setTimeout(() => this.refreshToken(), refreshTime - Date.now());
  }
}
```

## 📱 **MODÈLES DE DONNÉES MOBILES**

### **Utilisateur**
```typescript
interface MobileUser {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  picture?: string;
  dateOfBirth?: string;
  gender?: string;
  isVerified: boolean;
  lastLoginDate: string;
}
```

### **Transaction**
```typescript
interface MobileTransaction {
  id: string;
  amount: number;
  currency: string;
  type: 'Purchase' | 'Cashback' | 'Transfer' | 'Refund';
  status: 'Pending' | 'Completed' | 'Failed' | 'Cancelled';
  description: string;
  transactionDate: string;
  merchantName?: string;
  reference?: string;
  walletId: string;
}
```

### **Wallet**
```typescript
interface MobileWallet {
  id: string;
  balance: number;
  currency: string;
  type: 'Main' | 'Cashback' | 'Bonus';
  isActive: boolean;
  lastUpdated: string;
}
```

### **Entreprise**
```typescript
interface MobileCompany {
  id: string;
  name: string;
  logo?: string;
  description?: string;
  category: string;
  cashbackRate?: number;
  isActive: boolean;
  address?: string;
  phoneNumber?: string;
  distance?: number; // Pour géolocalisation
}
```

## 🔄 **GESTION DES REQUÊTES**

### **Client HTTP avec Intercepteurs**
```typescript
class ApiClient {
  private axiosInstance: AxiosInstance;
  
  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DynoMobile/1.0.0'
      }
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = AuthManager.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        const language = DeviceSettings.getLanguage();
        config.headers.Language = language;
        
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await AuthManager.refreshToken();
          return this.axiosInstance.request(error.config);
        }
        return Promise.reject(error);
      }
    );
  }
}
```

### **Pagination**
```typescript
interface PaginationParams {
  pageSize?: number;
  pageNumber?: number;
  order?: 'ASC' | 'DESC';
  sort?: string;
}

interface PaginatedResponse<T> {
  statusCode: number;
  objectValue: T[];
  pagination: {
    totalCount: number;
    pageSize: number;
    currentPage: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

class PaginationManager<T> {
  private currentPage = 1;
  private hasMore = true;
  private items: T[] = [];
  
  async loadMore(apiCall: (params: PaginationParams) => Promise<PaginatedResponse<T>>): Promise<T[]> {
    if (!this.hasMore) return this.items;
    
    const response = await apiCall({
      pageSize: 20,
      pageNumber: this.currentPage,
      order: 'DESC'
    });
    
    this.items.push(...response.objectValue);
    this.currentPage++;
    this.hasMore = response.pagination.hasNext;
    
    return this.items;
  }
}
```

## 🔔 **NOTIFICATIONS PUSH**

### **Configuration SignalR**
```typescript
class NotificationManager {
  private connection: HubConnection;
  
  async connect(userId: string): Promise<void> {
    this.connection = new HubConnectionBuilder()
      .withUrl(`${API_CONFIG.notification}/notificationHub`, {
        accessTokenFactory: () => AuthManager.getToken()
      })
      .withAutomaticReconnect()
      .build();
    
    await this.connection.start();
    await this.connection.invoke('JoinGroup', `/Notify/${userId}`);
    
    this.connection.on('ReceiveNotification', (notification) => {
      this.handleNotification(notification);
    });
  }
  
  private handleNotification(notification: any): void {
    // Afficher notification locale
    LocalNotifications.schedule({
      notifications: [{
        title: notification.title,
        body: notification.message,
        id: notification.id,
        extra: notification
      }]
    });
  }
}
```

## 💾 **CACHE ET STOCKAGE LOCAL**

### **Stratégie de Cache**
```typescript
class CacheManager {
  private static readonly CACHE_KEYS = {
    USER_PROFILE: 'user_profile',
    TRANSACTIONS: 'transactions',
    COMPANIES: 'companies',
    WALLETS: 'wallets'
  };
  
  static async set<T>(key: string, data: T, ttl: number = 300000): Promise<void> {
    const cacheItem = {
      data,
      timestamp: Date.now(),
      ttl
    };
    
    await Storage.set({
      key,
      value: JSON.stringify(cacheItem)
    });
  }
  
  static async get<T>(key: string): Promise<T | null> {
    const result = await Storage.get({ key });
    if (!result.value) return null;
    
    const cacheItem = JSON.parse(result.value);
    const isExpired = Date.now() - cacheItem.timestamp > cacheItem.ttl;
    
    if (isExpired) {
      await Storage.remove({ key });
      return null;
    }
    
    return cacheItem.data;
  }
}
```

## 🔒 **SÉCURITÉ MOBILE**

### **Certificate Pinning**
```typescript
class SecurityManager {
  static setupCertificatePinning(): void {
    // Configuration pour Capacitor HTTP
    CapacitorHttp.configure({
      certificatePinning: {
        'api.dyno-motiva.com': {
          certificates: ['sha256/CERTIFICATE_HASH']
        }
      }
    });
  }
  
  static async validateDevice(): Promise<boolean> {
    const deviceInfo = await Device.getInfo();
    const macAddress = await this.getMacAddress();
    
    // Validation avec le backend
    const response = await api.get(
      `/api/AuthClient/CheckNumber/${deviceInfo.countryCode}/${deviceInfo.phoneNumber}/${macAddress}`
    );
    
    return response.data.objectValue;
  }
}
```

Cette spécification fournit tous les éléments techniques nécessaires pour l'intégration mobile de Platform Dyno.
