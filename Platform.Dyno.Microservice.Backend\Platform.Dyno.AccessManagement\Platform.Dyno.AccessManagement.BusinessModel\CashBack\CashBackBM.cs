﻿using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.CashBack
{
    public class CashBackBM: ReferentialData
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public CompanyBM? Company { get; set; }
        public string Code { get; set; } = string.Empty;
        public double WalletBallance { get; set; }
        public double DynoAmount { get; set; }
        public double FeeAmount { get; set; }
        public Currency Currency { get; set; }
        public double NetAmount { get; set; }
        public double VATAmount { get; set; }
        public double TotalAmount { get; set; }
        public Guid PaymentDetailsId { get; set; }
        public PaymentDetailsBM? PaymentDetails { get; set; }
        public DateTime ValidationDate { get; set; }
        public new CashBackStatus Status { get; set; }
    }
}
