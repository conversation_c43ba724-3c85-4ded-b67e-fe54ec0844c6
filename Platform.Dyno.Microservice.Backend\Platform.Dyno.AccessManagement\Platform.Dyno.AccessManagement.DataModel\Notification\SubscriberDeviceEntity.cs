﻿using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Notification
{
    public class SubscriberDeviceEntity : ReferentialData
    {
        public Guid Id { get; set; }
        public string EndPoint { get; set; } = string.Empty;
        public string EndPointArn { get; set; } = string.Empty;
        public string TargetArn { get; set; } = string.Empty;
        public string SubscriptionArn { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;

        [ForeignKey("UserEntityId")]
        public UserEntity? User { get; set; }
        public Guid? UserEntityId { get; set; }

        public List<MessageNotificationEntity> MessageNotifications { get; set; } = new List<MessageNotificationEntity>();
    }
}
