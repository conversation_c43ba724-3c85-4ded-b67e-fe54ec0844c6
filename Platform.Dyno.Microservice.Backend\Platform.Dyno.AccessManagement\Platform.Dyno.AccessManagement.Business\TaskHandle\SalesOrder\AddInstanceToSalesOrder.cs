﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("Add_Instance_To_SO")]
    public class AddInstanceToSalesOrder : IExternalTaskHandler
    {
        private readonly ILogger<AddInstanceToSalesOrder> _logger;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public AddInstanceToSalesOrder(
        ILogger<AddInstanceToSalesOrder> logger,
        ISalesOrderService salesOrderService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId)

        {
            _logger = logger;
            _salesOrderService = salesOrderService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);

                var instanceId = externalTask.ProcessInstanceId;

                if (salesOrderDTO == null || instanceId == null)
                {
                    outputVariables.Add("Result_Add_Instance_To_SO", new BooleanVariable(false));
                    outputVariables.Add("Error_Add_Instance_To_SO", new StringVariable("json conversion failed"));

                }
                else
                {
                    var response = _salesOrderService.AddInstanceToSO(salesOrderDTO.Id, instanceId, true);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        outputVariables.Add("Result_Add_Instance_To_SO", new BooleanVariable(true));
                    }
                    else
                    {
                        outputVariables.Add("Result_Add_Instance_To_SO", new BooleanVariable(false));
                        outputVariables.Add("Error_Add_Instance_To_SO", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Add_Instance_To_SO", new BooleanVariable(false));
                outputVariables.Add("Add_Instance_To_SO", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
