# 🌐 GUIDE COMPLET FRONTEND ANGULAR - PLATFORM DYNO

## 🎯 **DOCUMENTATION POUR L'ÉQUIPE FRONTEND**

Cette documentation complète fournit toutes les spécifications pour développer et réviser l'interface web Angular de Platform Dyno.

## 📊 **ÉTAT ACTUEL DU PROJET**

### **Informations Générales**
- **Nom du projet :** apollo-ng
- **Version Angular :** 17.3.3
- **UI Framework :** PrimeNG 16.0.2 + PrimeFlex
- **Autres librairies :** SignalR, Chart.js, JWT-decode, FullCalendar

### **Structure Actuelle**
```
src/app/
├── layout/                 # ✅ Bonne structure de layout
├── routes/                 # ✅ Organisation par fonctionnalités
├── services/               # ⚠️ Tous les services dans un dossier
├── shared/                 # ✅ Composants partagés
├── ParentAuthGuard.ts      # ⚠️ Guards à la racine
└── RoleGuard.ts           # ⚠️ Guards à la racine
```

## 🏗️ **ARCHITECTURE CLARIFIÉE**

### **👥 Utilisateurs de l'Interface Web Angular :**

1. **👑 SuperAdmin**
   - Créer/gérer entreprises et commerçants
   - Alimenter wallets entreprises (après réception virements)
   - Monitoring global du système

2. **🏢 Entreprise (Employés avec droits)**
   - Gérer employés et groupes d'employés
   - Distribuer tickets (restaurant/cadeaux) par type
   - Consulter historiques et rapports
   - Gestion des rôles et permissions

3. **🏪 Commerçant**
   - Consulter transactions reçues
   - Gérer cash-out et rapports
   - Générer QR codes pour paiements
   - Monitoring des ventes

## 🔐 **AUTHENTIFICATION WEB (AuthAdmin)**

### **Configuration Environnements**
```typescript
// environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:7274/api',
  authEndpoint: '/AuthAdmin',  // ✅ IMPORTANT: Web utilise AuthAdmin
  paymentApiUrl: 'http://localhost:7018/api',
  notificationUrl: 'http://localhost:7038',
  s3Url: 'https://dynofiles.s3.amazonaws.com/'
};

// environments/environment.prod.ts
export const environment = {
  production: true,
  apiUrl: 'https://api.dyno-motiva.com/api',
  authEndpoint: '/AuthAdmin',
  paymentApiUrl: 'https://payment-api.dyno-motiva.com/api',
  notificationUrl: 'https://notification-api.dyno-motiva.com',
  s3Url: 'https://dynofiles.s3.amazonaws.com/'
};
```

### **Service d'Authentification**
```typescript
// core/services/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { environment } from '../../environments/environment';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  statusCode: number;
  objectValue: {
    token: string;
    refreshToken: string;
    expiredDate: string;
    userProfile: UserProfile;
  };
}

export interface UserProfile {
  id: string;
  fullName: string;
  email: string;
  userType: string;
  companyId?: string;
  roles: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'dyno_token';
  private readonly REFRESH_TOKEN_KEY = 'dyno_refresh_token';
  private readonly USER_KEY = 'dyno_user';
  
  private currentUserSubject = new BehaviorSubject<UserProfile | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.loadStoredAuth();
  }

  login(credentials: LoginCredentials): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(
      `${environment.apiUrl}${environment.authEndpoint}/login`,
      credentials
    ).pipe(
      tap(response => {
        if (response.statusCode === 200) {
          this.setAuthData(response.objectValue);
        }
      })
    );
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  refreshToken(): Observable<AuthResponse> {
    const token = localStorage.getItem(this.TOKEN_KEY);
    const refreshToken = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    
    return this.http.post<AuthResponse>(
      `${environment.apiUrl}${environment.authEndpoint}/RefreshToken`,
      { token, refreshToken }
    ).pipe(
      tap(response => {
        if (response.statusCode === 200) {
          this.setAuthData(response.objectValue);
        }
      })
    );
  }

  private setAuthData(authData: any): void {
    localStorage.setItem(this.TOKEN_KEY, authData.token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, authData.refreshToken);
    localStorage.setItem(this.USER_KEY, JSON.stringify(authData.userProfile));
    this.currentUserSubject.next(authData.userProfile);
  }

  private loadStoredAuth(): void {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      const user = JSON.parse(userStr);
      this.currentUserSubject.next(user);
    }
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  hasRole(role: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.roles?.includes(role) || false;
  }

  isSuperAdmin(): boolean {
    return this.hasRole('SuperAdmin');
  }

  isCompanyAdmin(): boolean {
    return this.hasRole('CompanyAdmin');
  }

  isMerchant(): boolean {
    return this.hasRole('Merchant');
  }
}
```

## 📋 **ENDPOINTS PAR ACTEUR**

### **👑 SuperAdmin**
```typescript
// services/super-admin.service.ts
export class SuperAdminService {
  private baseUrl = `${environment.apiUrl}`;

  // Gestion entreprises
  createCompany(company: Company): Observable<ApiResponse<Company>> {
    return this.http.post<ApiResponse<Company>>(`${this.baseUrl}/Company/Create`, company);
  }

  getAllCompanies(pageSize = 20, pageNumber = 1): Observable<ApiResponse<Company[]>> {
    return this.http.get<ApiResponse<Company[]>>(
      `${this.baseUrl}/Company/GetAllPaged?pageSize=${pageSize}&pageNumber=${pageNumber}`
    );
  }

  // Gestion commerçants
  createMerchant(merchant: Merchant): Observable<ApiResponse<Merchant>> {
    return this.http.post<ApiResponse<Merchant>>(`${this.baseUrl}/Merchant/Create`, merchant);
  }

  // Alimentation wallets entreprises
  fundCompanyWallet(companyId: string, amount: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/Wallet/FundCompanyWallet`, {
      companyId,
      amount
    });
  }

  getFundingHistory(companyId?: string): Observable<ApiResponse<Transaction[]>> {
    const url = companyId 
      ? `${this.baseUrl}/Transaction/GetFundingHistory?companyId=${companyId}`
      : `${this.baseUrl}/Transaction/GetFundingHistory`;
    return this.http.get<ApiResponse<Transaction[]>>(url);
  }
}
```

### **🏢 Entreprise**
```typescript
// services/company.service.ts
export class CompanyService {
  private baseUrl = `${environment.apiUrl}`;

  // Gestion employés
  createEmployee(employee: Employee): Observable<ApiResponse<Employee>> {
    return this.http.post<ApiResponse<Employee>>(`${this.baseUrl}/Employee/Create`, employee);
  }

  updateEmployee(employee: Employee): Observable<ApiResponse<Employee>> {
    return this.http.put<ApiResponse<Employee>>(`${this.baseUrl}/Employee/Update`, employee);
  }

  getEmployeesByCompany(): Observable<ApiResponse<Employee[]>> {
    return this.http.get<ApiResponse<Employee[]>>(`${this.baseUrl}/Employee/GetByCompany`);
  }

  // Gestion groupes
  createEmployeeGroup(group: EmployeeGroup): Observable<ApiResponse<EmployeeGroup>> {
    return this.http.post<ApiResponse<EmployeeGroup>>(`${this.baseUrl}/EmployeeGroup/Create`, group);
  }

  getEmployeeGroups(): Observable<ApiResponse<EmployeeGroup[]>> {
    return this.http.get<ApiResponse<EmployeeGroup[]>>(`${this.baseUrl}/EmployeeGroup/GetByCompany`);
  }

  // Distribution tickets
  distributeTicketsToEmployee(distribution: TicketDistribution): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/Ticket/DistributeToEmployee`, distribution);
  }

  distributeTicketsToGroup(distribution: GroupTicketDistribution): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/Ticket/DistributeToGroup`, distribution);
  }

  getDistributionHistory(): Observable<ApiResponse<TicketDistribution[]>> {
    return this.http.get<ApiResponse<TicketDistribution[]>>(`${this.baseUrl}/Ticket/GetDistributionHistory`);
  }
}
```

### **🏪 Commerçant**
```typescript
// services/merchant.service.ts
export class MerchantService {
  private baseUrl = `${environment.apiUrl}`;

  // Gestion transactions
  getMerchantTransactions(pageSize = 20, pageNumber = 1): Observable<ApiResponse<Transaction[]>> {
    return this.http.get<ApiResponse<Transaction[]>>(
      `${this.baseUrl}/Transaction/GetMerchantTransactions?pageSize=${pageSize}&pageNumber=${pageNumber}`
    );
  }

  getDailyReport(date: string): Observable<ApiResponse<DailyReport>> {
    return this.http.get<ApiResponse<DailyReport>>(
      `${this.baseUrl}/Transaction/GetDailyReport?date=${date}`
    );
  }

  // QR Code pour paiements
  generatePaymentQR(amount: number, description: string): Observable<ApiResponse<string>> {
    return this.http.post<ApiResponse<string>>(`${this.baseUrl}/QRCode/GeneratePaymentRequest`, {
      amount,
      description
    });
  }

  // Cash-out
  requestCashout(amount: number): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/Cashout/Request`, { amount });
  }

  getCashoutHistory(): Observable<ApiResponse<Cashout[]>> {
    return this.http.get<ApiResponse<Cashout[]>>(`${this.baseUrl}/Cashout/GetHistory`);
  }

  getCashoutBalance(): Observable<ApiResponse<number>> {
    return this.http.get<ApiResponse<number>>(`${this.baseUrl}/Cashout/GetBalance`);
  }
}
```

## 📱 **MODÈLES TYPESCRIPT**

### **Modèles Core**
```typescript
// models/api-response.model.ts
export interface ApiResponse<T> {
  statusCode: number;
  objectValue: T;
  exceptionMessage?: string;
}

// models/user.model.ts
export interface Employee {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  companyId: string;
  groupIds: string[];
  wallets: Wallet[];
  isActive: boolean;
  createdDate: Date;
}

export interface EmployeeGroup {
  id: string;
  name: string;
  description: string;
  companyId: string;
  employeeIds: string[];
  createdDate: Date;
}

// models/wallet.model.ts
export interface Wallet {
  id: string;
  walletType: WalletType;
  assignedToName: string;
  assignedToId: string;
  balance: number;
  status: WalletStatus;
}

export enum WalletType {
  Restaurant = 0,
  Cadeaux = 1,
  Transport = 2,
  Culture = 3
}

export enum WalletStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Suspended = 'Suspended'
}

// models/ticket.model.ts
export interface TicketDistribution {
  id?: string;
  employeeId: string;
  walletType: WalletType;
  amount: number;
  description: string;
  distributedBy: string;
  distributedDate: Date;
}

export interface GroupTicketDistribution {
  id?: string;
  groupId: string;
  walletType: WalletType;
  amountPerEmployee: number;
  description: string;
  distributedBy: string;
  distributedDate: Date;
}

// models/transaction.model.ts
export interface Transaction {
  id: string;
  senderWalletId: string;
  receiverWalletId: string;
  amount: number;
  description: string;
  transactionDate: Date;
  status: TransactionStatus;
  transactionType: TransactionType;
}

export enum TransactionStatus {
  Pending = 'Pending',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled'
}

export enum TransactionType {
  Payment = 'Payment',
  Funding = 'Funding',
  Cashout = 'Cashout',
  TicketDistribution = 'TicketDistribution'
}

// models/company.model.ts
export interface Company {
  id: string;
  name: string;
  email: string;
  address: string;
  phoneNumber: string;
  globalWalletId: string;
  globalWalletBalance: number;
  isActive: boolean;
  createdDate: Date;
}

// models/merchant.model.ts
export interface Merchant {
  id: string;
  name: string;
  email: string;
  address: string;
  phoneNumber: string;
  walletId: string;
  walletBalance: number;
  isActive: boolean;
  createdDate: Date;
}
```

## 🎨 **COMPOSANTS PRINCIPAUX**

### **1. Dashboard SuperAdmin**
```typescript
// components/super-admin/dashboard/dashboard.component.ts
import { Component, OnInit } from '@angular/core';
import { SuperAdminService } from '../../../services/super-admin.service';

@Component({
  selector: 'app-super-admin-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class SuperAdminDashboardComponent implements OnInit {
  companies: Company[] = [];
  merchants: Merchant[] = [];
  totalFunding = 0;
  pendingFundings = 0;

  constructor(private superAdminService: SuperAdminService) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.superAdminService.getAllCompanies().subscribe(response => {
      if (response.statusCode === 200) {
        this.companies = response.objectValue;
      }
    });

    this.superAdminService.getFundingHistory().subscribe(response => {
      if (response.statusCode === 200) {
        this.calculateFundingStats(response.objectValue);
      }
    });
  }

  fundCompanyWallet(companyId: string, amount: number): void {
    this.superAdminService.fundCompanyWallet(companyId, amount).subscribe(response => {
      if (response.statusCode === 200) {
        this.loadDashboardData();
      }
    });
  }

  private calculateFundingStats(transactions: Transaction[]): void {
    this.totalFunding = transactions
      .filter(t => t.transactionType === TransactionType.Funding)
      .reduce((sum, t) => sum + t.amount, 0);
  }
}
```

### **2. Gestion Employés (Entreprise)**
```typescript
// components/company/employee-management/employee-management.component.ts
import { Component, OnInit } from '@angular/core';
import { CompanyService } from '../../../services/company.service';

@Component({
  selector: 'app-employee-management',
  templateUrl: './employee-management.component.html'
})
export class EmployeeManagementComponent implements OnInit {
  employees: Employee[] = [];
  groups: EmployeeGroup[] = [];
  selectedEmployees: Employee[] = [];
  showCreateDialog = false;
  showDistributeDialog = false;

  newEmployee: Partial<Employee> = {};
  ticketDistribution: Partial<TicketDistribution> = {};

  walletTypes = [
    { label: 'Restaurant', value: WalletType.Restaurant },
    { label: 'Cadeaux', value: WalletType.Cadeaux },
    { label: 'Transport', value: WalletType.Transport },
    { label: 'Culture', value: WalletType.Culture }
  ];

  constructor(private companyService: CompanyService) {}

  ngOnInit(): void {
    this.loadEmployees();
    this.loadGroups();
  }

  loadEmployees(): void {
    this.companyService.getEmployeesByCompany().subscribe(response => {
      if (response.statusCode === 200) {
        this.employees = response.objectValue;
      }
    });
  }

  createEmployee(): void {
    this.companyService.createEmployee(this.newEmployee as Employee).subscribe(response => {
      if (response.statusCode === 200) {
        this.loadEmployees();
        this.showCreateDialog = false;
        this.newEmployee = {};
      }
    });
  }

  distributeTickets(): void {
    if (this.selectedEmployees.length === 0) return;

    const distributions = this.selectedEmployees.map(employee => ({
      ...this.ticketDistribution,
      employeeId: employee.id
    }));

    distributions.forEach(distribution => {
      this.companyService.distributeTicketsToEmployee(distribution as TicketDistribution)
        .subscribe(response => {
          if (response.statusCode === 200) {
            console.log('Tickets distribués avec succès');
          }
        });
    });

    this.showDistributeDialog = false;
    this.selectedEmployees = [];
  }
}
```

## 🔧 **INTERCEPTEURS ET GUARDS**

### **Auth Interceptor**
```typescript
// core/interceptors/auth.interceptor.ts
import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    const token = this.authService.getToken();

    if (token) {
      const authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${token}`)
      });
      return next.handle(authReq);
    }

    return next.handle(req);
  }
}
```

### **Role Guard**
```typescript
// core/guards/role.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const requiredRoles = route.data['roles'] as string[];

    if (!requiredRoles) return true;

    const hasRole = requiredRoles.some(role => this.authService.hasRole(role));

    if (!hasRole) {
      this.router.navigate(['/unauthorized']);
      return false;
    }

    return true;
  }
}
```

## 📋 **RECOMMANDATIONS POUR LA RÉVISION**

### **🚨 Priorité Critique**
1. **Corriger l'authentification** - Utiliser AuthAdmin au lieu d'AuthClient
2. **Restructurer les services** - Séparer par domaine fonctionnel
3. **Implémenter les intercepteurs** - Auth, Error, Loading
4. **Corriger les environnements** - URLs cohérentes par environnement

### **⚡ Priorité Haute**
1. **Implémenter la gestion des tickets** - Distribution par type de wallet
2. **Ajouter la gestion des groupes** - Création et gestion d'employés
3. **Créer les dashboards** - SuperAdmin, Entreprise, Commerçant
4. **Implémenter les QR codes** - Génération pour commerçants

### **📊 Priorité Moyenne**
1. **Ajouter la pagination** - Sur toutes les listes
2. **Implémenter SignalR** - Notifications temps réel
3. **Optimiser les performances** - OnPush, lazy loading
4. **Ajouter les rapports** - Analytics et historiques

### **🎨 Priorité Basse**
1. **Améliorer l'UX/UI** - Cohérence avec PrimeNG
2. **Ajouter les tests** - Unitaires et e2e
3. **Internationalisation** - Support multi-langues
4. **Optimisation build** - Production ready

## 🏗️ **STRUCTURE RECOMMANDÉE**

```
src/app/
├── core/                           # Services singleton, guards, interceptors
│   ├── services/
│   │   ├── auth.service.ts
│   │   ├── api.service.ts
│   │   └── notification.service.ts
│   ├── guards/
│   │   ├── auth.guard.ts
│   │   └── role.guard.ts
│   ├── interceptors/
│   │   ├── auth.interceptor.ts
│   │   ├── error.interceptor.ts
│   │   └── loading.interceptor.ts
│   └── models/
│       ├── user.model.ts
│       ├── transaction.model.ts
│       └── api-response.model.ts
├── features/                       # Modules fonctionnels
│   ├── auth/
│   │   ├── login/
│   │   └── auth.module.ts
│   ├── super-admin/
│   │   ├── dashboard/
│   │   ├── company-management/
│   │   ├── merchant-management/
│   │   └── super-admin.module.ts
│   ├── company/
│   │   ├── dashboard/
│   │   ├── employee-management/
│   │   ├── ticket-distribution/
│   │   └── company.module.ts
│   ├── merchant/
│   │   ├── dashboard/
│   │   ├── transaction-history/
│   │   ├── cashout-management/
│   │   └── merchant.module.ts
│   └── shared/
│       ├── components/
│       ├── pipes/
│       └── directives/
├── layout/
│   ├── header/
│   ├── sidebar/
│   └── footer/
└── app.module.ts
```

## 🎯 **PLAN D'IMPLÉMENTATION**

### **Phase 1 : Corrections Critiques (1-2 jours)**
1. Corriger l'authentification (AuthAdmin)
2. Restructurer les services
3. Implémenter les intercepteurs
4. Corriger les environnements

### **Phase 2 : Fonctionnalités Core (3-5 jours)**
1. Dashboard SuperAdmin
2. Gestion employés et groupes
3. Distribution de tickets
4. Dashboard commerçant

### **Phase 3 : Fonctionnalités Avancées (5-7 jours)**
1. QR codes et paiements
2. Rapports et analytics
3. Notifications temps réel
4. Optimisations performance

### **Phase 4 : Finalisation (2-3 jours)**
1. Tests et validation
2. Optimisation UX/UI
3. Documentation
4. Déploiement

## ✅ **CHECKLIST DE VALIDATION**

### **Authentification :**
- [ ] Login avec email/password (AuthAdmin)
- [ ] Refresh token automatique
- [ ] Guards par rôle (SuperAdmin, Company, Merchant)
- [ ] Déconnexion sécurisée

### **SuperAdmin :**
- [ ] Créer/gérer entreprises
- [ ] Créer/gérer commerçants
- [ ] Alimenter wallets entreprises
- [ ] Monitoring global

### **Entreprise :**
- [ ] Gérer employés et groupes
- [ ] Distribuer tickets par type
- [ ] Consulter historiques
- [ ] Gestion des rôles

### **Commerçant :**
- [ ] Consulter transactions
- [ ] Générer QR codes
- [ ] Gérer cash-out
- [ ] Rapports quotidiens

**L'équipe frontend dispose maintenant de toutes les spécifications pour développer l'interface web Angular complète de Platform Dyno !**
```
