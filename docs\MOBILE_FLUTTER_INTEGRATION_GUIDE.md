# 📱 GUIDE D'INTÉGRATION MOBILE FLUTTER - PLATFORM DYNO

## 🎯 **POUR L'ÉQUIPE MOBILE FLUTTER**

Ce document fournit toutes les informations nécessaires pour intégrer l'application mobile Flutter avec les APIs Platform Dyno.

## 🏗️ **ARCHITECTURE GÉNÉRALE**

```
📱 FLUTTER APP (Mobile Employees)
    ↓ HTTPS/JSON
🌐 PLATFORM DYNO APIs
├── 🔐 AuthClient (Port 7274) - Authentification employés
├── 💰 Payment (Port 7018) - Transactions et wallets
├── 🔔 Notification (Port 7038) - Notifications temps réel
└── 📊 Company (Port 7274) - Données entreprises
```

## 🔐 **AUTHENTIFICATION (AuthClient)**

### **Base URL :** `https://api.dyno-motiva.com/api/AuthClient`
### **Développement :** `http://localhost:7274/api/AuthClient`

### **1. Inscription Employé**
```http
POST /Register
Content-Type: application/json

{
  "fullName": "<PERSON> Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+33123456789",
  "countryCode": "+33",
  "password": "SecurePassword123!",
  "companyId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": {
    "message": "Registration successful. Please verify your phone number."
  }
}
```

### **2. Connexion Employé**
```http
POST /login
Content-Type: application/json
acceptLanguage: en
acceptTermsAndConditions: true

{
  "phoneNumber": "+33123456789",
  "password": "SecurePassword123!",
  "countryCode": "+33"
}
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiredDate": "2024-01-15T15:30:00Z",
    "userProfile": {
      "id": "user-guid",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+33123456789",
      "userType": "Client",
      "companyId": "company-guid"
    }
  }
}
```

### **3. Connexion Biométrique**
```http
GET /BiometricLogin/{password}
Authorization: Bearer {token}
```

### **4. Refresh Token**
```http
POST /RefreshToken
Content-Type: application/json

{
  "token": "current_token",
  "refreshToken": "current_refresh_token",
  "expiredDate": "2024-01-15T15:30:00Z"
}
```

### **5. Mot de Passe Oublié**
```http
GET /ForgetPassword/{countryCode}/{phoneNumber}
```

### **6. Vérification OTP**
```http
POST /VerifyOTPCode
Content-Type: application/json

{
  "phoneNumber": "+33123456789",
  "otpCode": "123456",
  "countryCode": "+33"
}
```

### **7. Reset Mot de Passe**
```http
POST /ResetPassword
Content-Type: application/json

{
  "phoneNumber": "+33123456789",
  "newPassword": "NewSecurePassword123!",
  "otpCode": "123456",
  "countryCode": "+33"
}
```

## 💰 **TRANSACTIONS ET WALLETS (Payment)**

### **Base URL :** `https://payment-api.dyno-motiva.com/api`
### **Développement :** `http://localhost:7018/api`

### **Headers Requis :**
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
acceptLanguage: en
```

### **1. Mes Wallets**
```http
GET /Wallet/GetMyWallets
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": [
    {
      "id": "wallet-guid",
      "privateKey": "encrypted_private_key",
      "publicKey": "public_key",
      "walletType": "Personal",
      "assignedToType": "Client",
      "assignedToName": "John Doe",
      "assignedToId": "user-guid",
      "balance": 1250.50,
      "status": "Active"
    }
  ]
}
```

### **2. Solde Total**
```http
GET /Wallet/GetTotalBalance
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": 1250.50
}
```

### **3. Créer Transaction Standard**
```http
POST /Transaction/Create?pinCode=1234
Content-Type: application/json

{
  "senderWalletId": "sender-wallet-guid",
  "receiverWalletId": "receiver-wallet-guid",
  "amount": 100.00,
  "description": "Payment for services",
  "transactionDate": "2024-01-15T10:30:00Z"
}
```

### **4. 🔄 Transaction Distribuée Sécurisée (NOUVEAU)**
```http
POST /DistributedTransaction/ExecuteSecure?pinCode=1234
Content-Type: application/json

{
  "senderWalletId": "sender-wallet-guid",
  "receiverWalletId": "receiver-wallet-guid",
  "amount": 100.00,
  "description": "Secure distributed transaction",
  "transactionDate": "2024-01-15T10:30:00Z"
}
```

**Avantages de la Transaction Distribuée :**
- ✅ **Atomicité garantie** (tout ou rien)
- ✅ **Compensation automatique** en cas d'échec
- ✅ **Cohérence Neo4j/Blockchain**
- ✅ **Résilience aux pannes**

### **5. Mes Transactions (Paginées)**
```http
GET /Transaction/GetAllPaged?pageSize=20&pageNumber=1&order=DESC
```

### **6. Transactions par Wallet**
```http
GET /Transaction/GetWalletTransactions?walletId={guid}&pageSize=20&pageNumber=1
```

### **7. Statut Transaction Distribuée**
```http
GET /DistributedTransaction/Status/{transactionId}
```

## 🏢 **ENTREPRISES (Company)**

### **Base URL :** `https://api.dyno-motiva.com/api`
### **Développement :** `http://localhost:7274/api`

### **1. Entreprises Récentes**
```http
GET /Company/GetRecents?pageSize=10
Authorization: Bearer {jwt_token}
```

### **2. Liste Entreprises (Paginée)**
```http
GET /Company/GetAllPaged?pageSize=20&pageNumber=1&order=DESC
Authorization: Bearer {jwt_token}
```

### **3. Détails Entreprise**
```http
GET /Company/Get/{companyId}
Authorization: Bearer {jwt_token}
```

## 👤 **PROFIL UTILISATEUR**

### **1. Mon Profil**
```http
GET /User/GetMyProfile
Authorization: Bearer {jwt_token}
```

### **2. Mettre à Jour Profil**
```http
PUT /User/UpdateProfile
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "fullName": "John Doe Updated",
  "email": "<EMAIL>",
  "phoneNumber": "+33123456789"
}
```

## 🔔 **NOTIFICATIONS (SignalR)**

### **Base URL :** `https://notification-api.dyno-motiva.com`
### **Développement :** `http://localhost:7038`

### **Connexion SignalR :**
```dart
// Flutter/Dart
import 'package:signalr_netcore/signalr_netcore.dart';

final connection = HubConnectionBuilder()
    .withUrl('http://localhost:7038/notificationHub',
        options: HttpConnectionOptions(
          accessTokenFactory: () => Future.value(jwtToken),
        ))
    .build();

await connection.start();

// Rejoindre le groupe de notifications
await connection.invoke('JoinGroup', ['/Notify/$userId']);

// Écouter les notifications
connection.on('ReceiveNotification', (notification) {
  print('Nouvelle notification: ${notification[0]}');
});
```

## 📱 **MODÈLES DART POUR FLUTTER**

### **AuthResponse**
```dart
class AuthResponse {
  final String token;
  final String refreshToken;
  final DateTime expiredDate;
  final UserProfile userProfile;

  AuthResponse({
    required this.token,
    required this.refreshToken,
    required this.expiredDate,
    required this.userProfile,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      refreshToken: json['refreshToken'],
      expiredDate: DateTime.parse(json['expiredDate']),
      userProfile: UserProfile.fromJson(json['userProfile']),
    );
  }
}
```

### **UserProfile**
```dart
class UserProfile {
  final String id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String userType;
  final String? companyId;

  UserProfile({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.userType,
    this.companyId,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      fullName: json['fullName'],
      email: json['email'],
      phoneNumber: json['phoneNumber'],
      userType: json['userType'],
      companyId: json['companyId'],
    );
  }
}
```

### **Wallet**
```dart
class Wallet {
  final String id;
  final String walletType;
  final String assignedToName;
  final double balance;
  final String status;

  Wallet({
    required this.id,
    required this.walletType,
    required this.assignedToName,
    required this.balance,
    required this.status,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      id: json['id'],
      walletType: json['walletType'],
      assignedToName: json['assignedToName'],
      balance: json['balance'].toDouble(),
      status: json['status'],
    );
  }
}
```
