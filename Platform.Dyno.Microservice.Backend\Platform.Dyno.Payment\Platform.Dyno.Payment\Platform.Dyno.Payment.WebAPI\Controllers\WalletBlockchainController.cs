﻿using Application.Models.BMs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Paiement.WebAPI.Controllers;
using Platform.Dyno.Paiement.WebAPI;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using Application.IServices;
using Newtonsoft.Json;
using Platform.Dyno.Shared.Pagination;

namespace Platform.Dyno.Payment.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class WalletBlockchainController : ControllerBase
    {
        private readonly IWalletBlockchainService _walletService;
        private readonly ILogger<WalletBlockchainController> _logger;

        public WalletBlockchainController(IWalletBlockchainService walletService,
            ILogger<WalletBlockchainController> logger)
        {
            _walletService = walletService;
            _logger = logger;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                _logger.LogInformation("la wallet Service : " + _walletService.ToString());
                ResponseAPI<List<WalletDTO>> wallets = _walletService.GetAll();
                return Ok(wallets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<WalletDTO>> wallets = _walletService.GetAll(pagedParameters);

                if (wallets.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = wallets.ObjectValue.TotalCount,
                        PageSize = wallets.ObjectValue.PageSize,
                        CurrentPage = wallets.ObjectValue.CurrentPage,
                        TotalPages = wallets.ObjectValue.TotalPages,
                        HasNext = wallets.ObjectValue.HasNext,
                        HasPrevious = wallets.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(wallets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<WalletDTO> wallet = _walletService.Get(id);
                return Ok(wallet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetUserWallets")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserWallets(Guid userId)
        {
            try
            {
                ResponseAPI<List<WalletDTO>> response = _walletService.GetUserWallets(userId);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Create 

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] WalletDTO walletDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<WalletDTO> response = _walletService.Create(walletDTO, new Guid(), pinCode);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("CreateDefaultWallets")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateDefaultWallets([FromBody] List<WalletDTO> walletDTOList, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                var walletDTO = walletDTOList.FirstOrDefault();
                if (walletDTO != null)
                {
                    ResponseAPI<List<WalletDTO>> response = _walletService.CreateDefaultWallets(walletDTO, new Guid(), pinCode);
                    return Ok(response);
                }
                else
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Update
        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] WalletDTO walletDTO)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                //string? updateUserId = HttpContext.User.FindFirstValue("Id");

                //if (updateUserId == null)
                //{
                //    return Unauthorized(new ResponseAPI<WalletBM>
                //    {
                //        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                //        ExceptionMessage = "User unauthorized !"
                //    });
                //}
                ResponseAPI<WalletDTO> response = _walletService.Update(walletDTO, Guid.NewGuid());
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Delete

        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletBM>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                //string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                //if (deleteUserId == null)
                //{
                //    return Unauthorized(new ResponseAPI<WalletBM>
                //    {
                //        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                //        ExceptionMessage = "User unauthorized !"
                //    });
                //}
                ResponseAPI<WalletDTO> response = _walletService.Delete(id, Guid.NewGuid());
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
