﻿using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.Notification
{
    public class SignalRNotificationBM : ReferentialData
    { 
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsSeen { get; set; } = false;
        public List<CompanyBM>? SendToId { get; set; }
        public UserType CreatorType { get; set; }
    }
}
