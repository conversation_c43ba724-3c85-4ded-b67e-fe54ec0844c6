﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Neo4j.Driver" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
  </ItemGroup>
</Project>