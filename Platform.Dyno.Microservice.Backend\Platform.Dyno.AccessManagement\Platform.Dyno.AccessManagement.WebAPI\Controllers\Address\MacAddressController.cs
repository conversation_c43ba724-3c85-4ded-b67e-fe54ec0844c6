﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Net.Mail;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Address
{
    [Route("api/[controller]")]
    [ApiController]
    public class MacAddressController : ControllerBase
    {
        private readonly IMacAddressService _macAddressService;
        private readonly ILogger<MacAddressController> _logger;

        public MacAddressController(IMacAddressService macAddressService, 
            ILogger<MacAddressController> logger)
        {
            _macAddressService = macAddressService;
            _logger = logger;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                ResponseAPI<List<MacAddressDTO>> macAddresses = _macAddressService.GetAll();
                return Ok(macAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPagedMacAddresses")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<MacAddressDTO>> macAddresses = _macAddressService.GetAll(pagedParameters);

                if (macAddresses.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = macAddresses.ObjectValue.TotalCount,
                        PageSize = macAddresses.ObjectValue.PageSize,
                        CurrentPage = macAddresses.ObjectValue.CurrentPage,
                        TotalPages = macAddresses.ObjectValue.TotalPages,
                        HasNext = macAddresses.ObjectValue.HasNext,
                        HasPrevious = macAddresses.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(macAddresses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<MacAddressDTO> user = _macAddressService.Get(id);
                return Ok(user);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Create 
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] MacAddressDTO macAddress)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<MacAddressDTO> response = _macAddressService.Create(macAddress);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Update

        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] MacAddressDTO macAddress)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<MacAddressDTO> response = _macAddressService.Update(macAddress);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<MacAddressDTO> response = _macAddressService.Delete(id);
                return Ok(response);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                ResponseAPI<List<MacAddressDTO>> response = new ResponseAPI<List<MacAddressDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
