﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Notification.DTO.Emailing;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.Notification.WebAPI.Controllers
{
    [Route("API/[controller]")]
    [ApiController]
    public class EmailingController : ControllerBase
    {
        private readonly ILogger<EmailingController> _logger;
        private readonly IEmailSender _emailSender;

        public EmailingController(ILogger<EmailingController> logger,
            IEmailSender emailSender)
        {
            _logger = logger;
            _emailSender = emailSender;
        }

        [Route("Send")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] MessageEmailDTO message)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<MessageEmailDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                

                _emailSender.SendEmail(message);
                return Ok(new ResponseAPI<MessageEmailDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<MessageEmailDTO> response = new ResponseAPI<MessageEmailDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
    }
}
