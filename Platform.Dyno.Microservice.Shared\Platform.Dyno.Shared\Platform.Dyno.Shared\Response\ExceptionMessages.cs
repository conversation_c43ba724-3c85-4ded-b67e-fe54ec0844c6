﻿using Enyim.Caching.Memcached.Protocol;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Response
{
    public class ExceptionMessages
    {
        public string? Object { get; set; } = string.Empty;

        public string? ReadError;

        public string? CreateError;

        public string? UpdateError;

        public string? DeleteError;

        public string? UnAuthorized;

        public ExceptionMessages(string _object) 
        {
            Object = _object;
            ReadError = $"Unable to retrieve {Object}. The specified Object ID does not exist or is invalid.";
            CreateError = $"Failed to create {Object}. Please ensure all required fields are provided and valid.";
            UpdateError = $"Failed to update {Object}. The provided Object ID is missing or invalid.";
            DeleteError = $"Failed to delete {Object}. The specified Object ID does not exist or is invalid.";
            UnAuthorized = $"Access denied.You do not have the required permissions to perform this operation on {Object}. Please contact your administrator for assistance.";
        }  

    }
}
