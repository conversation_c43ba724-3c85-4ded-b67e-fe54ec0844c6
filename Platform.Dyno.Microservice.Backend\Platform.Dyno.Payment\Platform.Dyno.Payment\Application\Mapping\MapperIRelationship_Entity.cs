﻿using AutoMapper;
using Domain.Entities;
using Neo4j.Driver;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Mapping
{
    public class MapperIRelationship_Entity:Profile
    {
        public MapperIRelationship_Entity()
        {
            CreateMap<IRelationship, TransactionEntity>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.Parse(src["Id"].As<string>())))
            .ForMember(dest => dest.ReceiverWalletId, opt => opt.MapFrom(src => Guid.Parse(src["ReceiverWalletId"].As<string>())))
            .ForMember(dest => dest.SenderWalletId, opt => opt.MapFrom(src => Guid.Parse(src["SenderWalletId"].As<string>())))
            .ForMember(dest => dest.QrCodeId, opt => opt.MapFrom(src => Guid.Parse(src["QrCodeId"].As<string>())))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => double.Parse(src["Amount"].As<string>())))
            .ForMember(dest => dest.TransactionDate, opt => opt.MapFrom(src => (src["TransactionDate"].As<ZonedDateTime>()).UtcDateTime))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse(typeof(TransactionStatus), src["Status"].As<string>(), true)))
            .ForMember(dest => dest.RefundStatus, opt => opt.MapFrom(src => Enum.Parse(typeof(RefundStatus), src["RefundStatus"].As<string>(), true)))
            .ReverseMap();

            CreateMap<IRelationship, RefundTransactionEntity>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.Parse(src["Id"].As<string>())))
            .ForMember(dest => dest.ReceiverWalletId, opt => opt.MapFrom(src => Guid.Parse(src["ReceiverWalletId"].As<string>())))
            .ForMember(dest => dest.SenderWalletId, opt => opt.MapFrom(src => Guid.Parse(src["SenderWalletId"].As<string>())))
            .ForMember(dest => dest.QrCodeId, opt => opt.MapFrom(src => Guid.Parse(src["QrCodeId"].As<string>())))
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => double.Parse(src["Amount"].As<string>())))
            .ForMember(dest => dest.TransactionDate, opt => opt.MapFrom(src => (src["TransactionDate"].As<ZonedDateTime>()).UtcDateTime))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse(typeof(TransactionStatus), src["Status"].As<string>(), true)))
            .ForMember(dest => dest.RefundStatus, opt => opt.MapFrom(src => Enum.Parse(typeof(RefundStatus), src["RefundStatus"].As<string>(), true)))
            .ReverseMap();
        }
    }
}
