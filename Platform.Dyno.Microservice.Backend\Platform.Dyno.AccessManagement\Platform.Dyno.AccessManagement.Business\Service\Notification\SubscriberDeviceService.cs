﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Notification
{
    public class SubscriberDeviceService : ISubscriberDeviceService
    {
        private readonly IUnitOfWork<SubscriberDeviceEntity> _subscriberDeviceRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _subscriberDeviceCacheKey = RedisCacheKey.SubscriberDeviceCacheKey;

        public SubscriberDeviceService(IUnitOfWork<SubscriberDeviceEntity> subscriberDeviceRepository,
            IMapper mapper,
            IRedisCacheService cache)
        {
            _subscriberDeviceRepository = subscriberDeviceRepository;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<SubscriberDeviceDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            List<SubscriberDeviceDTO>? subscriberDevicesDTO = _cache.GetData<List<SubscriberDeviceDTO>>(_subscriberDeviceCacheKey);
            if (subscriberDevicesDTO == null || subscriberDevicesDTO.Count == 0)
            {
                List<SubscriberDeviceEntity> macAddressEntities = (List<SubscriberDeviceEntity>)_subscriberDeviceRepository.Repository.GetAll();
                List<SubscriberDeviceBM> macAddressess = _mapper.Map<List<SubscriberDeviceBM>>(macAddressEntities);
                subscriberDevicesDTO = _mapper.Map<List<SubscriberDeviceDTO>>(macAddressess);

            }

            ResponseAPI<List<SubscriberDeviceDTO>> response = new ResponseAPI<List<SubscriberDeviceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = subscriberDevicesDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<SubscriberDeviceDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SubscriberDeviceDTO>> subscriberDeviceDTO = GetAll(companyId, userType);
            PagedList<SubscriberDeviceDTO>? pagedList = null;
            if (subscriberDeviceDTO.ObjectValue != null)
            {
                pagedList = PagedList<SubscriberDeviceDTO>.ToGenericPagedList(subscriberDeviceDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<SubscriberDeviceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<SubscriberDeviceDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<SubscriberDeviceDTO>> subscriberDeviceDTO = GetAll(companyId, userType);
            PagedList<SubscriberDeviceDTO>? pagedList = null;
            if (subscriberDeviceDTO.ObjectValue != null)
            {
                pagedList = PagedList<SubscriberDeviceDTO>.ToGenericPagedList(subscriberDeviceDTO.ObjectValue.Where(subscriberDevice => subscriberDevice.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<SubscriberDeviceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<SubscriberDeviceDTO> Get(Guid id)
        {
            SubscriberDeviceDTO? subscriberDeviceDTO = GetAll().ObjectValue?.Where(macAddress => macAddress.Id == id).FirstOrDefault();
            return new ResponseAPI<SubscriberDeviceDTO>()
            {
                StatusCode = subscriberDeviceDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = subscriberDeviceDTO == null ? $"Device with id {id} Not Found !" : null,
                ObjectValue = subscriberDeviceDTO
            };
        }

        public ResponseAPI<List<SubscriberDeviceDTO>> Get(Func<SubscriberDeviceDTO, bool> expression)
        {
            List<SubscriberDeviceDTO>? subscriberDeviceDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<SubscriberDeviceDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = subscriberDeviceDTO
            };
        }
        #endregion

        #region Create
        public ResponseAPI<SubscriberDeviceDTO> Create(SubscriberDeviceDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            SubscriberDeviceBM subscriberDevice = _mapper.Map<SubscriberDeviceBM>(dtoObject);

            #region changed data
            subscriberDevice.Id = Guid.NewGuid();
            subscriberDevice.UserEntityId = creatorUserId;
            RefDataService<SubscriberDeviceBM>.CreateRefData(subscriberDevice, subscriberDevice.Id);
            #endregion

            SubscriberDeviceEntity subscriberDeviceEntity = _mapper.Map<SubscriberDeviceEntity>(subscriberDevice);
            _subscriberDeviceRepository.Repository.Insert(subscriberDeviceEntity);
            _subscriberDeviceRepository.Save();

            dtoObject.Id = subscriberDeviceEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_subscriberDeviceCacheKey);
            }

            return new ResponseAPI<SubscriberDeviceDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public ResponseAPI<SubscriberDeviceDTO> RegistrationDevice(SubscriberDeviceDTO dtoObject)
        {
            SubscriberDeviceDTO? deviceExist = Get(dtoObject.Id).ObjectValue;
            if(deviceExist != null)
            {
                //Update User FCM token
                ResponseAPI<SubscriberDeviceDTO> updateDevice = Update(dtoObject);
                return updateDevice;
            }

            ResponseAPI<SubscriberDeviceDTO> createDevice = Create(dtoObject);
            return createDevice;
        }
        #endregion

        #region Update
        public ResponseAPI<SubscriberDeviceDTO> Update(SubscriberDeviceDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (Get(dtoObject.Id).ObjectValue != null)
            {

                SubscriberDeviceBM subscriberDevice = _mapper.Map<SubscriberDeviceBM>(dtoObject);

                #region RefData
                RefDataService<SubscriberDeviceBM>.UpdateRefData(subscriberDevice, subscriberDevice.Id);
                #endregion

                SubscriberDeviceEntity subscriberDeviceEntity = _mapper.Map<SubscriberDeviceEntity>(subscriberDevice);
                _subscriberDeviceRepository.Repository.Update(subscriberDeviceEntity);
                _subscriberDeviceRepository.Save();
                dtoObject.Id = subscriberDeviceEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_subscriberDeviceCacheKey);
                }

                return new ResponseAPI<SubscriberDeviceDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<SubscriberDeviceDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Device with id {dtoObject.Id} Not Found !"
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<SubscriberDeviceDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            throw new NotImplementedException();
        }

        #endregion

    }
}
