﻿using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.KafkaMessage;
using Server.Kafka;

namespace WatchDog
{
    public class MicroserviceWatcher
    {
        public State State { get; set; }

        public Stack<HeartBeatMessage> lastHeartBeatMessage = new Stack<HeartBeatMessage>();

        public volatile int missingMessageNumber = 0;
        public bool messageChecked = true;
        private ILogger Logger { get => StaticLoggerFactory.GetStaticLogger<MicroserviceWatcher>(); }

        private readonly IProducer<WatchDogNotifDTO> _notification;

        private CancellationToken _token;


        public MicroserviceWatcher(HeartBeatMessage heartBeat,
            IProducer<WatchDogNotifDTO> notification)
        {
            _notification = notification;

            heartBeat.MicroserviceInfo.GenerateGuid();
            lastHeartBeatMessage.Push(heartBeat);
            messageChecked = false;

            CheckMicroservice();

        }

        public void ManageMessage(HeartBeatMessage heartBeat)
        {
            if (!messageChecked)
            {
                lastHeartBeatMessage.Push(heartBeat);
                missingMessageNumber++;
            }
            else
            {
                lastHeartBeatMessage.Clear();
                lastHeartBeatMessage.Push(heartBeat);
                missingMessageNumber = 1;
                messageChecked = false;
            }
        }

        private void CheckMicroservice()
        {
            Task task = Task.Run(() =>
            {
                WatchDogNotifDTO watchDogNotifDTO = new WatchDogNotifDTO
                {
                    MicroserviceId = lastHeartBeatMessage.Peek().MicroserviceInfo.Guid,
                    MicroserviceName = lastHeartBeatMessage.Peek().MicroserviceInfo.Name,
                    Type = lastHeartBeatMessage.Peek().MicroserviceInfo.Type,
                    LastUpDate = lastHeartBeatMessage.Peek().MicroserviceInfo.LastUpDate
                };

                while (State != State.Red)
                {
                    if (!messageChecked)
                    {
                        State = State.Green;
                        messageChecked = true;
                        Logger.LogInformation($"watchDog {lastHeartBeatMessage.Peek().MicroserviceInfo.Guid} {lastHeartBeatMessage.Peek().MicroserviceInfo.Name} {lastHeartBeatMessage.Peek().MicroserviceInfo.Type} {this.State} {lastHeartBeatMessage.Peek().MicroserviceInfo.LastUpDate}");

                        //Send Notification Signal R
                        watchDogNotifDTO.State = this.State;
                        _notification.sendMessage(watchDogNotifDTO, _token);
                    }

                    TimeSpan timeInterval = DateTime.Now - lastHeartBeatMessage.Peek().DateMessage;
                    if (timeInterval > lastHeartBeatMessage.Peek().WaitTime + TimeSpan.FromSeconds(2))
                    {
                        ChangeStateToBad();
                        Logger.LogWarning($"watchDog {lastHeartBeatMessage.Peek().MicroserviceInfo.Guid} {lastHeartBeatMessage.Peek().MicroserviceInfo.Name} {lastHeartBeatMessage.Peek().MicroserviceInfo.Type} {this.State} {LastUpDate.Error}");
                        //Send Notification Signal R
                        watchDogNotifDTO.State = this.State;
                        _notification.sendMessage(watchDogNotifDTO, _token);
                    }


                    Thread.Sleep(lastHeartBeatMessage.Peek().WaitTime);
                }
                Logger.LogError($"watchDog {lastHeartBeatMessage.Peek().MicroserviceInfo.Guid} {lastHeartBeatMessage.Peek().MicroserviceInfo.Name} {lastHeartBeatMessage.Peek().MicroserviceInfo.Type} {this.State} {LastUpDate.Stopped}");
                //Send Notification Signal R
                watchDogNotifDTO.State = this.State;
                _notification.sendMessage(watchDogNotifDTO, _token);
            });
        }

        private void ChangeStateToBad()
        {
            if (State == State.Green)
            {
                State = State.Orange;
            }
            else if (State == State.Orange)
            {
                State = State.Red;
            }
        }
    }
}
