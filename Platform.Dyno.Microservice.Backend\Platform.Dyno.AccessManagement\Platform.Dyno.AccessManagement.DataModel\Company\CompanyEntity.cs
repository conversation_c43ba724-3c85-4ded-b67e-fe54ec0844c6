﻿using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Company;
[Table("Company")]
public class CompanyEntity : ReferentialData
{
    #region Data
    public Guid Id { get; set; }
    public string RNECode { get; set; } = string.Empty;
    public string TaxCode { get; set; } = string.Empty;
    public string Name { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string? CountryCode { get; set; }
    public string? Picture { get; set; }
    public double? ClientFeePercentage { get; set; }
    public EnterpiseType EntrepriseType { get; set; }
    public ServiceType ServiceType { get; set; }
    public CategoryType? CategoryType { get; set; }
    
    #endregion

    #region Sturcture
    public IList<AddressEntity>? Addresses { get; set; }
    public IList<UserEntity>? Users { get; set; }
    public IList<EmployeeEntity>? Employees { get; set; }
    public IList<TicketEntity>? Tickets { get; set; }
    public IList<SalesOrderEntity>? SalesOrders { get; set; }
    public IList<SalesInvoiceEntity>? SalesInvoices { get; set; }
    public IList<PaymentDetailsEntity>? PaymentDetails { get; set; }
    public ICollection<CompanyNotifEntity>? CompanyNotifs { get; set; } = new List<CompanyNotifEntity>();
    public Guid WalletId { get; set; }
    #endregion

}
