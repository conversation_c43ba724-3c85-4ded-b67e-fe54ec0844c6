﻿using Application.IServices;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Paiement.WebAPI;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.Payment.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class TransactionBlockchainController : ControllerBase
    {
        private readonly ITransactionBlockchainService _transactionBlockchainService;
        private readonly ITransactionService _transactionService;
        private readonly ILogger<TransactionBlockchainController> _logger;

        public TransactionBlockchainController(ITransactionBlockchainService transactionBlockchainService,
            ITransactionService transactionService,
            ILogger<TransactionBlockchainController> logger)
        {
            _transactionBlockchainService = transactionBlockchainService;
            _transactionService = transactionService;
            _logger = logger;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {

                var transactions = _transactionBlockchainService.GetAll();
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.Get(id);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        #region get by user
        [Route("GetUserReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserReceivedTransactions(Guid userId)
        {
            try
            {
                var response = _transactionBlockchainService.GetUserReceivedTransactions(userId);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }


        [Route("GetUserSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserSentTransactions(Guid userId)
        {
            try
            {
                var response = _transactionBlockchainService.GetUserSentTransactions(userId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        [Route("GetUserTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserTransactions(Guid userId)
        {
            try
            {
                var response = _transactionBlockchainService.GetUserTransactions(userId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion


        #region get by wallet
        [Route("GetWalletReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetReceivedTransactions(Guid walletId)
        {
            try
            {
                var response = _transactionBlockchainService.GetWalletReceivedTransactions(walletId);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("GetWalletSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletSentTransactions(Guid walletId)
        {
            try
            {
                var response = _transactionBlockchainService.GetWalletSentTransactions(walletId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("GetWalletTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletTransactions(Guid walletId)
        {
            try
            {
                var response = _transactionBlockchainService.GetWalletTransactions(walletId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion


        #endregion

        #region Create 

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.Create(transactionDTO, new Guid(), pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("CreateTransactionFromCompanyToCompany")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateTransactionFromCompanyToCompany([FromBody] TransactionToCompanyDTO transactionToCompanyDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.CreateTransactionToCompany(transactionToCompanyDTO, new Guid(), pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("TransactionClientShop")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult TransactionClientShop([FromBody] TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.TransactionClientShop(transactionDTO, new Guid(), pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("CreatePreTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreatePreTransaction([FromBody] TransactionDTO TransactionDTO)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<TransactionDTO> response = _transactionService.CreatePreTransaction(TransactionDTO);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CreatePreTransaction)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("CreateUniqueQrCodeTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<UniqueQRCodeTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.CreateUniqueQrCodeTransaction(transactionDTO, pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CreateUniqueQrCodeTransaction)}");
                ResponseAPI<List<UniqueQRCodeTransactionDTO>> response = new ResponseAPI<List<UniqueQRCodeTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("CancelTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CancelTransaction(Guid transactionId, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.CancelTransaction(transactionId, pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CancelTransaction)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("CreateTransactionsForGroup")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateTransactionsForGroup([FromBody] TransactionGroupDTO transactionGroup, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<List<TransactionUserDTO>> response = _transactionBlockchainService.CreateTransactionsForGroup(transactionGroup, pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
        #endregion

        #region Update 
        [Route("UpdateRefundStatus/{transactionId}/{refundStatus}")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateRefundStatus(Guid transactionId, RefundStatus refundStatus)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<TransactionDTO> response = _transactionBlockchainService.UpdateRefundStatus(transactionId, refundStatus);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(UpdateRefundStatus)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

    }
}
