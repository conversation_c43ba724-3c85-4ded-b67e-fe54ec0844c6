﻿using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.Reporting
{
    public class DocumentsBM : ReferentialData
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public Guid? CompanyId { get; set; }
        public DocumentType Type { get; set; }
    }
}
