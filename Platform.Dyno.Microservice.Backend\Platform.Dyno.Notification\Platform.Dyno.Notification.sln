﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33403.182
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.WebAPI", "Platform.Dyno.Notification\Platform.Dyno.Notification.WebAPI.csproj", "{548BA157-B550-435E-B276-834E9A03E2E9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.DTO", "Platform.Dyno.Notification.DTO\Platform.Dyno.Notification.DTO.csproj", "{2CD182D7-4BBF-4655-8957-20B4FD9FA63D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.Business", "Platform.Dyno.Notification.Business\Platform.Dyno.Notification.Business.csproj", "{2DA08581-9E89-4A41-9AAC-65A4C3B93FBA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Shared", "..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj", "{3C6AACF0-69DC-4BE4-A69E-7716E6AA8E78}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Server.Kafka", "..\Server.Kafka\Server.Kafka\Server.Kafka.csproj", "{9FB05D81-DEA7-474A-8E8C-************}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{548BA157-B550-435E-B276-834E9A03E2E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{548BA157-B550-435E-B276-834E9A03E2E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{548BA157-B550-435E-B276-834E9A03E2E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{548BA157-B550-435E-B276-834E9A03E2E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{2CD182D7-4BBF-4655-8957-20B4FD9FA63D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2CD182D7-4BBF-4655-8957-20B4FD9FA63D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2CD182D7-4BBF-4655-8957-20B4FD9FA63D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2CD182D7-4BBF-4655-8957-20B4FD9FA63D}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DA08581-9E89-4A41-9AAC-65A4C3B93FBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DA08581-9E89-4A41-9AAC-65A4C3B93FBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DA08581-9E89-4A41-9AAC-65A4C3B93FBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DA08581-9E89-4A41-9AAC-65A4C3B93FBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C6AACF0-69DC-4BE4-A69E-7716E6AA8E78}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C6AACF0-69DC-4BE4-A69E-7716E6AA8E78}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C6AACF0-69DC-4BE4-A69E-7716E6AA8E78}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C6AACF0-69DC-4BE4-A69E-7716E6AA8E78}.Release|Any CPU.Build.0 = Release|Any CPU
		{9FB05D81-DEA7-474A-8E8C-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9FB05D81-DEA7-474A-8E8C-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9FB05D81-DEA7-474A-8E8C-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9FB05D81-DEA7-474A-8E8C-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8463B7B3-DB82-44DF-9F2C-AE0006A500B6}
	EndGlobalSection
EndGlobal
