﻿using Platform.Dyno.AccessManagement.DTO.User;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse
{
    public class AuthResponseDTO
    {
        public string? Token { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiredDate { get; set; }

        #region UserData
        public UserProfileDTO? UserProfile { get; set; }
        public Guid? CompanyId { get; set; }
        #endregion
    }
}
