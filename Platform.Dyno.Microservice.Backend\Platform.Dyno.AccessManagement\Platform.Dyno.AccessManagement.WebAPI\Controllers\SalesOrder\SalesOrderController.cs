﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.Service.SalesOrder;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Logger;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.WebAPI.Controllers.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.Request;
using Platform.Dyno.Shared.ResponseAPI;
using StackExchange.Redis;
using System.Net;
using System.Security.Claims;
using static Camunda.Api.Client.Filter.FilterInfo;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.SalesOrder
{
    [Route("Api/[controller]")]
    [ApiController]
    public class SalesOrderController : Controller
    {
        private readonly ISalesOrderService _salesOrderService;
        private readonly ISalesInvoiceService _salesInvoiceService;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;
        private readonly ILogger<SalesOrderController> _logger;
        private readonly ILogErrorService _logErrorService;
        private readonly ISortingUtility _sortingUtility;
        public SalesOrderController(ISalesOrderService salesOrderService, ISalesInvoiceService salesInvoiceService,
            IUserService userService,
            ILogger<SalesOrderController> logger,
            ILogErrorService logErrorService, ISortingUtility sortingUtility, ICompanyService companyService)
        {
            _salesOrderService = salesOrderService;
            _salesInvoiceService = salesInvoiceService;
            _userService = userService;
            _logger = logger;
            _logErrorService = logErrorService;
            _sortingUtility = sortingUtility;
            _companyService = companyService;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK) 
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<SalesOrderDTO>> salesOrders = _salesOrderService.GetAll(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(salesOrders);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<SalesOrderDTO>> response = new ResponseAPI<List<SalesOrderDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPerPeriod")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPerPeriod()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<int>> salesOrders = _salesOrderService.GetByPeriod(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(salesOrders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPerPeriod)}");
                ResponseAPI<List<SalesOrderDTO>> response = new ResponseAPI<List<SalesOrderDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<PagedList<SalesOrderDTO>> salesOrdersDTO = _salesOrderService.GetAll(companyId, isSuperAdminResponse.ObjectValue, pagedParameters);
                if (salesOrdersDTO.ObjectValue != null)
                {
                    salesOrdersDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, salesOrdersDTO);
                    salesOrdersDTO.ObjectValue = _sortingUtility.FilterData(pagedParameters, salesOrdersDTO);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = salesOrdersDTO.ObjectValue.TotalCount,
                        PageSize = salesOrdersDTO.ObjectValue.PageSize,
                        CurrentPage = salesOrdersDTO.ObjectValue.CurrentPage,
                        TotalPages = salesOrdersDTO.ObjectValue.TotalPages,
                        HasNext = salesOrdersDTO.ObjectValue.HasNext,
                        HasPrevious = salesOrdersDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(salesOrdersDTO);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllByStatus")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //public IActionResult GetAllByStatus([FromQuery] PagedParameters pagedParameters, [FromBody]List<SalesOrderStatus> salesOrderStatus)
        public IActionResult GetAllByStatus([FromBody] SalesOrderStatusRequest request )
        {
            try
            {
                if (request == null || request.SalesOrderStatus == null || request.PagedParameters == null || !request.SalesOrderStatus.Any())
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Paged parameters and sales order status list are required"
                    };
                    return BadRequest(response);
                }
                PagedParameters pagedParameters = request.PagedParameters;
                List<SalesOrderStatus> salesOrderStatus = request.SalesOrderStatus;
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<PagedList<SalesOrderDTO>> salesOrdersDTOResponse = _salesOrderService.GetAllByStatus(companyId, isSuperAdminResponse.ObjectValue, salesOrderStatus, pagedParameters);
                if (salesOrdersDTOResponse.ObjectValue != null)
                {
                    salesOrdersDTOResponse.ObjectValue = _sortingUtility.SortData(pagedParameters, salesOrdersDTOResponse);
                    salesOrdersDTOResponse.ObjectValue = _sortingUtility.FilterData(pagedParameters, salesOrdersDTOResponse);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = salesOrdersDTOResponse.ObjectValue.TotalCount,
                        PageSize = salesOrdersDTOResponse.ObjectValue.PageSize,
                        CurrentPage = salesOrdersDTOResponse.ObjectValue.CurrentPage,
                        TotalPages = salesOrdersDTOResponse.ObjectValue.TotalPages,
                        HasNext = salesOrdersDTOResponse.ObjectValue.HasNext,
                        HasPrevious = salesOrdersDTOResponse.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(salesOrdersDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllByStatus)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }
        [Route("GetSalesOrdersByType")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetSalesOrdersByType([FromQuery] PagedParameters pagedParameters, ProductType productType)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<PagedList<SalesOrderDTO>> salesOrdersDTOResponse = _salesOrderService.GetAllByType(companyId, isSuperAdminResponse.ObjectValue, productType, pagedParameters);
                return Ok(salesOrdersDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetSalesOrdersByType)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }
        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesOrderDTO> salesOrderDTOResponse = _salesOrderService.Get(companyId, isSuperAdminResponse.ObjectValue, id);
                if (salesOrderDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesOrderDTOResponse);
                }
                return Ok(salesOrderDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }

        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Create([FromBody] SalesOrderDTO salesOrderDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }

                if(salesOrderDTO.TotalAmount > 999999)
                {
                    return BadRequest(
                        new ResponseAPI<SalesOrderDTO>()
                        {
                            StatusCode = System.Net.HttpStatusCode.BadRequest,
                            ExceptionMessage = "Total Amount is too high !"
                        });
                }

                CompanyDTO? company = _companyService.Get(companyId).ObjectValue;
                if(company == null)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }

                ResponseAPI<SalesOrderDTO> response = _salesOrderService.Create(companyId, salesOrderDTO, userId);
                if (response.ObjectValue == null)
                {
                    return BadRequest(response);
                }
                company.Addresses = null;
                company.Users= null;
                response.ObjectValue.Company = company;
                ResponseAPI<string> StartProcessResponse = await _salesOrderService.StartProcess(response.ObjectValue);
                if (StartProcessResponse.StatusCode != HttpStatusCode.OK)
                {
                    LogErrorDTO logErrorDTO = new LogErrorDTO()
                    {
                        Id = Guid.NewGuid(),
                        Microservice = MicroserviceName.AccessManagement,
                        API = $"SalesOrder/Create/{salesOrderDTO.Id}",
                        Error = StartProcessResponse.ExceptionMessage==null ? "unknown error ": StartProcessResponse.ExceptionMessage,
                        Type = ErrorType.SO_Start_ProcessFailed,
                        CreationDate = DateTime.UtcNow
                    };

                    var logErrorResponse = _logErrorService.Create(logErrorDTO);
                }
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

        #region Update
        [Route("Update")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] SalesOrderDTO salesOrderDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesOrderDTO> salesOrderDTOResponse = _salesOrderService.Update(companyId, isSuperAdminResponse.ObjectValue, salesOrderDTO, userId);
                if (salesOrderDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesOrderDTOResponse);
                }
                return Ok(salesOrderDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region validate SO
        [Route("ValidateTransfertRequest")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ValidateTransfertRequest(Guid salesOrderId)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesInvoiceDTO> salesInvoiceDTOResponse = _salesOrderService.ValidateTransfertRequest(companyId, isSuperAdminResponse.ObjectValue, salesOrderId, userId);
                if (salesInvoiceDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesInvoiceDTOResponse);
                }
                ResponseAPI<string> StartProcessResponse = await _salesInvoiceService.StartProcess(salesInvoiceDTOResponse.ObjectValue);
                if (StartProcessResponse.StatusCode != HttpStatusCode.OK)
                {
                    LogErrorDTO logErrorDTO = new LogErrorDTO()
                    {
                        Id = Guid.NewGuid(),
                        Microservice = MicroserviceName.AccessManagement,
                        API = $"SalesOrder/ValidateRequest/{salesInvoiceDTOResponse.ObjectValue.Id}",
                        Error = StartProcessResponse.ExceptionMessage == null ? "unknown error " : StartProcessResponse.ExceptionMessage,
                        Type = ErrorType.SO_Start_ProcessFailed,
                        CreationDate = DateTime.UtcNow
                    };

                    var logErrorResponse = _logErrorService.Create(logErrorDTO);
                    
                    return BadRequest(new ResponseAPI<SalesOrderDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "process failed to start"
                    });
                }
                return Ok(salesInvoiceDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesOrderDTO> salesOrderDTOResponse = _salesOrderService.Delete(companyId, isSuperAdminResponse.ObjectValue, id, userId);
                return Ok(salesOrderDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

        #region reprint pdf
        [Route("ReprintPDF/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult ReprintPDF(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesOrderDTO> salesOrderDTOResponse = _salesOrderService.ReprintPDF(companyId, isSuperAdminResponse.ObjectValue, id);
                if (salesOrderDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesOrderDTOResponse);
                }
                return Ok(salesOrderDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion

        #region resend mail pdf
        [Route("ResendMail/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult ResendMail(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<SalesOrderDTO> salesOrderDTOResponse = _salesOrderService.ResendMail(companyId, isSuperAdminResponse.ObjectValue, id);
                if (salesOrderDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesOrderDTOResponse);
                }
                return Ok(salesOrderDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion
    }
}
