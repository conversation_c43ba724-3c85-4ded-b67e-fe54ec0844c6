﻿using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IUserTokenService : IGenericService<UserTokenDTO>
    {
        ResponseAPI<UserTokenDTO> Remove(Guid id, Guid? deletorUserId = null, bool updateCache = true);
    }
}
