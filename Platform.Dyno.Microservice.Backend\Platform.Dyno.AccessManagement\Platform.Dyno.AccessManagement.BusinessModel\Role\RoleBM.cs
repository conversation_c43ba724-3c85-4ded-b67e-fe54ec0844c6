﻿using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.Role
{
    public class RoleBM : ReferentialData
    {
        #region Role Identity Data
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public UserType UserType { get; set; }
        #endregion

        #region Structure
        public IList<UserBM>? Users { get; set; }

        public ICollection<PermissionBM>? Permissions { get; set; }

        public Guid? CompanyId { get; set; }
        #endregion
    }
}
