﻿using Microsoft.AspNetCore.Identity;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.IService.CashBack;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Group;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.Ticket;
using Platform.Dyno.AccessManagement.Business.IService.Transaction;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.AccessManagement.Business.Service.Address;
using Platform.Dyno.AccessManagement.Business.Service.CashBack;
using Platform.Dyno.AccessManagement.Business.Service.Company;
using Platform.Dyno.AccessManagement.Business.Service.Group;
using Platform.Dyno.AccessManagement.Business.Service.Notification;
using Platform.Dyno.AccessManagement.Business.Service.RoleManagement;
using Platform.Dyno.AccessManagement.Business.Service.SalesOrder;
using Platform.Dyno.AccessManagement.Business.Service.Ticket;
using Platform.Dyno.AccessManagement.Business.Service.Transaction;
using Platform.Dyno.AccessManagement.Business.Service.UserManagement;
using Platform.Dyno.AccessManagement.Business.Service.Wallet;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.CashBack;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.EF;
using Platform.Dyno.Notification.DTO.Emailing;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.Business.Service.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.AccessManagement.DataModel.Logger;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.Business.Service.Logger;

namespace Platform.Dyno.AccessManagement.WebAPI
{
    public static class DependencyLifeTime
    {
        public static void ScoppedService(this IServiceCollection services)
        {
            #region User
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAuthAdminService, AuthAdminService>();
            services.AddScoped<IAuthClientService, AuthClientService>();
            services.AddScoped<IAuthCashierService, AuthCashierService>();
            services.AddScoped<IUserTokenService, UserTokenService>();
            services.AddScoped<IUserOTPService, UserOTPService>();
            services.AddScoped<IBlackListedUserService, BlackListedUserService>();
            #endregion

            #region Role
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IPermissionService, PermissionService>();
            #endregion

            #region Company
            services.AddScoped<ICompanyService, CompanyService>();
            #endregion

            #region CashBack
            services.AddScoped<ICashBackService, CashBackService>();
            services.AddScoped<IFailedCashBackService, FailedCashBackService>();
            #endregion

            #region Group
            services.AddScoped<IGroupService, GroupService>();
            #endregion

            #region Address
            services.AddScoped<IMacAddressService, MacAddressService>();
            services.AddScoped<IAddressService, AddressService>();
            #endregion

            #region Notification
            services.AddScoped<IEmailingService, EmailingService>();    
            services.AddScoped<ISubscriberDeviceService, SubscriberDeviceService>();
            services.AddScoped<ISignalRNotificationService, SignalRNotificationService>();
            #endregion

            #region wallet
            services.AddScoped<IWalletService, WalletService>();
            #endregion

            #region sales order
            services.AddScoped<ISalesOrderService, SalesOrderService>();
            services.AddScoped<IFailedSalesOrderService, FailedSalesOrderService>();
            services.AddScoped<ISalesInvoiceService, SalesInvoiceService>();

            #endregion

            #region Ticket
            services.AddScoped<ITicketService, TicketService>();
            #endregion

            #region Reporting
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
            #endregion

            #region Transaction
            services.AddScoped<ITransactionService, TransactionService>();
            services.AddScoped<IRefundTransactionService, RefundTransactionService>();
            #endregion

            #region Logger
            services.AddScoped<ILogErrorService, LogErrorService>();
            #endregion

        }

        public static void ScoppedUnitOfWork(this IServiceCollection services)
        {
            #region User
            services.AddScoped<IUnitOfWork<UserEntity>, UnitOfWork<ContextDB, UserEntity>>();
            services.AddScoped<IUnitOfWork<UserTokenEntity>, UnitOfWork<ContextDB, UserTokenEntity>>();
            services.AddScoped<IUnitOfWork<UserOTPEntity>, UnitOfWork<ContextDB, UserOTPEntity>>();
            services.AddScoped<IUnitOfWork<BlackListedUserEntity>, UnitOfWork<ContextDB, BlackListedUserEntity>>();
            #endregion

            #region Role
            services.AddScoped<IUnitOfWork<RoleEntity>, UnitOfWork<ContextDB, RoleEntity>>();
            services.AddScoped<IUnitOfWork<PermissionEntity>, UnitOfWork<ContextDB, PermissionEntity>>();
            #endregion

            #region Company
            services.AddScoped<IUnitOfWork<CompanyEntity>, UnitOfWork<ContextDB, CompanyEntity>>();
            services.AddScoped<IUnitOfWork<PaymentDetailsEntity>, UnitOfWork<ContextDB, PaymentDetailsEntity>>();
            #endregion

            #region Cash Back
            services.AddScoped<IUnitOfWork<CashBackEntity>, UnitOfWork<ContextDB, CashBackEntity>>();
            services.AddScoped<IUnitOfWork<FailedCashBackEntity>, UnitOfWork<ContextDB, FailedCashBackEntity>>();
            #endregion

            #region Group
            services.AddScoped<IUnitOfWork<GroupEntity>, UnitOfWork<ContextDB, GroupEntity>>();
            services.AddScoped<IUnitOfWork<GroupTicketEntity>, UnitOfWork<ContextDB, GroupTicketEntity>>();
            #endregion

            #region Employee
            services.AddScoped<IUnitOfWork<EmployeeEntity>, UnitOfWork<ContextDB, EmployeeEntity>>();
            #endregion

            #region Address
            services.AddScoped<IUnitOfWork<MacAddressEntity>, UnitOfWork<ContextDB, MacAddressEntity>>();
            services.AddScoped<IUnitOfWork<AddressEntity>, UnitOfWork<ContextDB, AddressEntity>>();
            #endregion

            #region Notification
            services.AddScoped<IUnitOfWork<SubscriberDeviceEntity>, UnitOfWork<ContextDB, SubscriberDeviceEntity>>();
            services.AddScoped<IUnitOfWork<SignalRNotificationEntity>, UnitOfWork<ContextDB, SignalRNotificationEntity>>();
            #endregion

            #region Sales Order
            services.AddScoped<IUnitOfWork<SalesOrderEntity>, UnitOfWork<ContextDB, SalesOrderEntity>>();
            services.AddScoped<IUnitOfWork<FailedSalesOrderEntity>, UnitOfWork<ContextDB, FailedSalesOrderEntity>>();
            services.AddScoped<IUnitOfWork<SalesInvoiceEntity>, UnitOfWork<ContextDB, SalesInvoiceEntity>>();

            #endregion

            #region Ticket
            services.AddScoped<IUnitOfWork<TicketEntity>, UnitOfWork<ContextDB, TicketEntity>>();
            #endregion

            #region Reporting
            services.AddScoped<IUnitOfWork<DocumentsEntity>, UnitOfWork<ContextDB, DocumentsEntity>>();
            #endregion

            #region Logger
            services.AddScoped<IUnitOfWork<LogErrorEntity>, UnitOfWork<ContextDB, LogErrorEntity>>();
            #endregion

        }

        public static void ScoppedHelper(this IServiceCollection services)
        {
            #region Notification

            #region Email
            services.AddScoped<IHelper<MessageEmailDTO>, Helper<MessageEmailDTO>>();
            #endregion

            #region SignalRNotification
            services.AddScoped<IHelper<NotificationDTO>, Helper<NotificationDTO>>();
            services.AddScoped<IHelper<BalanceNotificationDTO>, Helper<BalanceNotificationDTO>>();
            #endregion

            #endregion

            #region Payment
            services.AddScoped<IHelper<WalletDTO>, Helper<WalletDTO>>();
            services.AddScoped<IHelper<TransactionDTO>, Helper<TransactionDTO>>();
            services.AddScoped<IHelper<TransactionUserDTO>, Helper<TransactionUserDTO>>();
            services.AddScoped<IHelper<TransactionGroupDTO>, Helper<TransactionGroupDTO>>();
            services.AddScoped<IHelper<TransactionToCompanyDTO>, Helper<TransactionToCompanyDTO>>();
            services.AddScoped<IHelper<RefundTransactionDTO>, Helper<RefundTransactionDTO>>();
            services.AddScoped<IHelper<double>, Helper<double>>();
            services.AddScoped<IHelper<bool>, Helper<bool>>();
            #endregion
        }

        public static void TransientService(this IServiceCollection services)
        {

        }

        public static void ConfigurationIdentity(this IServiceCollection services)
        {
            var builder = services.AddIdentity<UserEntity, RoleEntity>(option =>
            {
                option.Lockout.MaxFailedAccessAttempts = 5;
                option.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(6);
            });

            builder = new IdentityBuilder(builder.UserType, typeof(RoleEntity), services);

            builder.AddEntityFrameworkStores<ContextDB>()
                .AddDefaultTokenProviders();
        }
    }
}
