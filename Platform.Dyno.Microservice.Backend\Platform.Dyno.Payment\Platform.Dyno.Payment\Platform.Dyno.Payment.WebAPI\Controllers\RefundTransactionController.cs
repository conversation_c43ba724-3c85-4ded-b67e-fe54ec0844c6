﻿using Application.IServices;
using Application.Services;
using Domain.Enums.Query;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Paiement.WebAPI;
using Platform.Dyno.Paiement.WebAPI.Controllers;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.Payment.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class RefundTransactionController : ControllerBase
    {
        private readonly IRefundTransactionService _refundTransactionService;
        private readonly ILogger<TransactionController> _logger;

        public RefundTransactionController(IRefundTransactionService refundTransactionService,
            ILogger<TransactionController> logger)
        {
            _refundTransactionService= refundTransactionService;
            _logger = logger;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {

                var transactions = _refundTransactionService.GetAll();
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.Get(id);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #region Get by User Id

        [Route("GetUserReceivedRefundTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserReceivedRefundTransactions(Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);
                ResponseAPI<List<RefundTransactionDTO>> response = _refundTransactionService.GetUserReceivedTransactions(userId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUserReceivedRefundTransactions)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("GetUserSentRefundTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserSentRefundTransactions(Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);
                ResponseAPI<List<RefundTransactionDTO>> response = _refundTransactionService.GetUserSentTransactions(userId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUserSentRefundTransactions)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("GetUserRefundTransactions/{userId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserRefundTransactions(Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);

                ResponseAPI<List<RefundTransactionDTO>> response = _refundTransactionService.GetUserTransactions(userId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUserRefundTransactions)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] RefundTransactionDTO transactionDTO)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.Create(transactionDTO);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Update
        [Route("UpdateRefundStatus/{transactionId}/{status}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateRefundStatus(Guid transactionId, RefundStatus status)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.UpdateRefundStatus(transactionId, status);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(UpdateRefundStatus)}");
                ResponseAPI<List<RefundTransactionDTO>> response = new ResponseAPI<List<RefundTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
