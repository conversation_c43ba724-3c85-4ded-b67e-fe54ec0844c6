﻿using Amazon.S3.Model;
using Amazon.S3;
using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MimeKit.Encodings;
using OfficeOpenXml;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.EF;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Image;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.Security;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;
using System.Security.Cryptography.Xml;
using System.Text;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork<UserEntity> _userRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _userCacheKey = RedisCacheKey.UserCacheKey;
        private readonly ContextDB _contextDB;
        private readonly IRoleService _roleService;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("user");
        private readonly Configuration _configuration;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly IHelper<WalletDTO> _walletHelper;
        public UserService(IUnitOfWork<UserEntity> userRepository,
            IMapper mapper,
            IRedisCacheService cache,
            ContextDB contextDB,
            IRoleService roleService,
            Configuration configuration,
            IHelper<WalletDTO> walletHelper,
            ConfigurationDefaultId configurationDefaultId
            )
        {
            _userRepository = userRepository;
            _roleService = roleService;
            _mapper = mapper;
            _cache = cache;
            _contextDB = contextDB;
            _configuration = configuration;
            _walletHelper = walletHelper;
            _configurationDefaultId = configurationDefaultId;
        }

        #region Get
        public ResponseAPI<List<UserDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if (userType == UserType.Client)
            {
                return new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            List<UserDTO>? usersDTO = _cache.GetData<List<UserDTO>>(_userCacheKey);
            if (usersDTO == null || usersDTO.Count == 0)
            {
                List<UserEntity> userEntities = (List<UserEntity>)_userRepository.Repository.GetAll(includes: new List<string> { "RoleUsers", "Addresses", "MacAddresses", "RoleUsers.Role" }, orderBy: user => user.OrderByDescending(user => user.LastModificationTime));
                if (companyId != null)
                {
                    userEntities = userEntities.FindAll(u => u.CompanyId == companyId);
                }
                List<UserBM> users = _mapper.Map<List<UserBM>>(userEntities);
                usersDTO = _mapper.Map<List<UserDTO>>(users);
                foreach (var user in usersDTO)
                {
                    user.Picture = $"{_configuration.AWSS3URL}/Images/{user.Picture}";
                }

                _cache.SetData(_userCacheKey, usersDTO, DateTimeOffset.UtcNow.AddDays(1));

            }
            if (userType == UserType.Company || userType == UserType.ShopOwner)
            {
                usersDTO = usersDTO.Where(user => user.CompanyId == companyId).ToList();
            }
            ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = usersDTO,
            };
            return response;
        }
        public ResponseAPI<UserDTO> GetUserByPhoneNumber(string phoneNumber)
        {
            try
            {
                UserDTO? user = GetAll().ObjectValue?.FirstOrDefault(u => u.PhoneNumber == phoneNumber && u.UserType == UserType.Client);

                // Check if the user is found
                if (user != null)
                {
                    return new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.OK,
                        ObjectValue = user,
                    };
                }
                else
                {
                    return new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.NotFound,
                        ExceptionMessage = "User not found for the provided phone number.",
                    };
                }
            }
            catch (Exception ex)
            {
                return new ResponseAPI<UserDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred while retrieving the user by phone number.",
                };
            }
        }

        public ResponseAPI<List<UserDTO>> GetAllActive(Guid? companyId = null, UserType? userType = null)
        {
            List<UserDTO>? usersDTO = GetAll(companyId, userType).ObjectValue?.Where(u => u.Status == Status.Active).ToList();
            ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = usersDTO,
            };
            return response;
        }
        public ResponseAPI<List<UserDTO>> GetAllByRole(Guid roleId, Guid? companyId = null, UserType? userType = null)
        {
            List<UserDTO>? usersDTO = GetAll(companyId, userType).ObjectValue?.Where(u => u.Roles.Any(r => r.Id == roleId)).ToList();
            ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = usersDTO,
            };
            return response;
        }
        public ResponseAPI<List<UserDTO>> GetAllByDefaultRole(string defaultRole, Guid? companyId = null)
        {
            List<UserDTO>? usersDTO = GetAll().ObjectValue?.Where(u => u.Roles.Any(r => r.Name == defaultRole.ToString())).ToList();
            ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = usersDTO,
            };
            return response;
        }
        public ResponseAPI<PagedList<UserDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            if (userType == UserType.Client || userType == UserType.Cashier)
            {
                return new ResponseAPI<PagedList<UserDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            List<UserDTO>? usersDTO = _cache.GetData<List<UserDTO>>(_userCacheKey);
            if (usersDTO == null || usersDTO.Count == 0)
            {
                List<UserEntity> userEntities = (List<UserEntity>)_userRepository.Repository.GetAll(includes: new List<string> { "RoleUsers", "Addresses", "MacAddresses", "RoleUsers.Role" }, orderBy: user => user.OrderByDescending(user => user.LastModificationTime), expression: userEntities => userType != UserType.SuperAdmin ? userEntities.CompanyId == companyId : userEntities.CreatorUserId == _configurationDefaultId.UserId);
                List<UserBM> users = _mapper.Map<List<UserBM>>(userEntities);
                usersDTO = _mapper.Map<List<UserDTO>>(users);
                foreach (var user in usersDTO)
                {
                    if(user.Picture != null)
                        user.Picture = $"{_configuration.AWSS3URL}/Images/{user.Picture}";
                }

                _cache.SetData(_userCacheKey, usersDTO, DateTimeOffset.UtcNow.AddDays(1));

            }
            PagedList<UserDTO>? pagedList = null;
            if (usersDTO != null)
            {
                pagedList = PagedList<UserDTO>.ToGenericPagedList(usersDTO, pagedParameters);
            }
            return new ResponseAPI<PagedList<UserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<UserDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<UserDTO>> usersDTO = GetAll(companyId, userType);
            PagedList<UserDTO>? pagedList = null;
            if (usersDTO.ObjectValue != null)
            {
                pagedList = PagedList<UserDTO>.ToGenericPagedList(usersDTO.ObjectValue.Where(user => user.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<UserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<UserDTO>> GetUsersCreatedByAdmin(PagedParameters pagedParameters, Guid id, Guid? companyId, UserType userType)
        {
            ResponseAPI<PagedList<UserDTO>> response = new ResponseAPI<PagedList<UserDTO>>();
            var usersDTO = _cache.GetData<List<UserDTO>>(_userCacheKey);
            try
            {
                if (usersDTO == null || usersDTO.Count == 0)
                {
                    usersDTO = GetAllActive(companyId, userType).ObjectValue?.Where(user => user.CreatorUserId == id).ToList();
                    response.StatusCode = usersDTO == null || usersDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = PagedList<UserDTO>.ToGenericPagedList(usersDTO, pagedParameters);
                    _cache.SetData(_userCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }

            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<UserDTO> Get(Guid id)
        {
            UserDTO? userDTO = GetAll().ObjectValue?.Where(user => user.Id == id).FirstOrDefault();
            return new ResponseAPI<UserDTO>()
            {
                StatusCode = userDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = userDTO == null ? $"User with id {id} Not Found !" : null,
                ObjectValue = userDTO
            };
        }

        public ResponseAPI<List<UserDTO>> Get(Func<UserDTO, bool> expression)
        {
            List<UserDTO>? usersDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<UserDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = usersDTO
            };
        }

        public ResponseAPI<UserProfileDTO> GetUserProfile(Guid id)
        {
            UserDTO? userDTO = _cache.GetData<List<UserDTO>>(_userCacheKey)?.Where(user => user.Id == id).FirstOrDefault();
            if (userDTO == null)
            {
                UserEntity? userEntity = _userRepository.Repository.Get(user => user.Id == id);
                if (userEntity == null)
                {
                    return new ResponseAPI<UserProfileDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = exceptionMessages.UnAuthorized
                    };
                }
                UserBM user = _mapper.Map<UserBM>(userEntity);
                userDTO = _mapper.Map<UserDTO>(user);
            }
            
            if(userDTO.UserType != UserType.Client)
            {
                CompanyEntity? company = _contextDB.Company.Find(userDTO.CompanyId);
                userDTO.Picture = company?.Picture;
            }
            UserProfileDTO userprofile = _mapper.Map<UserProfileDTO>(userDTO);
            if (userprofile.Picture != null)
            {
                userprofile.Picture = $"{_configuration.AWSS3URL}/Images/{userprofile.Picture}";
            }
            
            return new ResponseAPI<UserProfileDTO>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = userprofile
            };
        }
        public ResponseAPI<bool> CheckCashierCode(string code)
        {
            var codeChecked = _userRepository.Repository.GetAll().ToList().Find(c => c.UserName == code);
            if (codeChecked != null)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = true
                };
            }
            else
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "wrong Cashier Code"
                };
            }
        }

        public ResponseAPI<bool> CheckUserPinCode(Guid userId, string pinCode)
        {
            UserDTO? userDTO = _cache.GetData<List<UserDTO>>(_userCacheKey)?.Where(user => user.Id == userId).FirstOrDefault();
            if (userDTO == null)
            {
                UserEntity? userEntity = _userRepository.Repository.Get(user => user.Id == userId);
                if (userEntity == null)
                {
                    return new ResponseAPI<bool>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = exceptionMessages.UnAuthorized
                    };
                }
                UserBM user = _mapper.Map<UserBM>(userEntity);
                userDTO = _mapper.Map<UserDTO>(user);
            }

            var hashedPinCode = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(pinCode)));


            if (hashedPinCode == userDTO.PinCode)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = true
                };
            }
            else
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wrong PinCode"
                };
            }

        }
        public ResponseAPI<bool> CheckUserIsSuperAdmin(Guid userId)
        {
            UserDTO? userDTO = _cache.GetData<List<UserDTO>>(_userCacheKey)?.Where(user => user.Id == userId).FirstOrDefault();
            if (userDTO == null)
            {
                UserEntity? userEntity = _userRepository.Repository.Get(user => user.Id == userId);
                if (userEntity == null)
                {
                    return new ResponseAPI<bool>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = exceptionMessages.UnAuthorized,
                        ObjectValue = false
                    };
                }
                UserBM user = _mapper.Map<UserBM>(userEntity);
                userDTO = _mapper.Map<UserDTO>(user);
            }
            if (userDTO.UserType == UserType.SuperAdmin)
            {
                return new ResponseAPI<bool>()
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = true
                };
            }
            return new ResponseAPI<bool>()
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = false
            };
        }
        #endregion

        #region Create
        public ResponseAPI<UserDTO> Create(UserDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            UserBM user = _mapper.Map<UserBM>(dtoObject);


            #region changed data
            user.Id = Guid.NewGuid();
            user.SecurityStamp = Guid.NewGuid().ToString();
            user.ConcurrencyStamp = Guid.NewGuid().ToString();
            user.CompanyId = userType == UserType.SuperAdmin ? dtoObject.CompanyId : companyId ?? Guid.Empty;

            if (dtoObject.DateOfBirth != null)
                user.DateOfBirth = DateTime.SpecifyKind((DateTime)dtoObject.DateOfBirth, DateTimeKind.Utc);

            user.NormalizedEmail = dtoObject.Email.ToUpper();
            RefDataService<UserBM>.CreateRefData(user, creatorUserId);
            if (user.UserType == UserType.SuperAdmin || user.UserType == UserType.Company || user.UserType == UserType.ShopOwner || user.UserType == UserType.Cashier)
            {
                user.PinCode = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(user.CompanyId.ToString())));
            }
            else
            {
                user.PinCode = Convert.ToBase64String(SecurityProvider.HashKeyTo256Bits(Encoding.UTF8.GetBytes(user.PinCode)));
            };
            if (user.Picture != null)
            {
                string imagePath = ImageHelper.SaveImageAsync(dtoObject.Picture ?? string.Empty).Result;
                user.Picture = imagePath;
            }
            #endregion

            UserEntity userEntity = _mapper.Map<UserEntity>(user);
            if (!string.IsNullOrEmpty(user.Password))
            {
                var passwordHasher = new PasswordHasher<UserEntity>();
                userEntity.PasswordHash = passwordHasher.HashPassword(userEntity, user.Password);
            }

            userEntity.setRoles(true);

            _userRepository.Repository.Insert(userEntity);
            _userRepository.Save();

            if (dtoObject.Roles != null)
            {
                foreach (var role in dtoObject.Roles)
                {
                    ResponseAPI<RoleDTO> response = _roleService.Get(role.Id);

                    if (response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        RoleBM roleBM = _mapper.Map<RoleBM>(response.ObjectValue);
                        RoleEntity roleEntity = _mapper.Map<RoleEntity>(roleBM);
                        userEntity.RoleUsers.Add(new RoleUserEntity { Role = roleEntity, User = userEntity });
                    }
                    else
                    {
                        return new ResponseAPI<UserDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.NotFound,
                            ExceptionMessage = $"Role with id : {role.Id} not found !"
                        };
                    }
                };

            }
            _userRepository.Save();

            dtoObject.Id = userEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_userCacheKey);
            }

            return new ResponseAPI<UserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        #endregion

        #region Update
        public ResponseAPI<UserDTO> Update(UserDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            UserEntity? userEntity = _contextDB.User.Include(user => user.RoleUsers).AsTracking().FirstOrDefault(user => user.Id == dtoObject.Id);
            if (userEntity != null)
            {
                if (userEntity.Status == Status.Deleted && dtoObject.Status == Status.Active)
                {
                    List<UserEntity>? userEmails = _contextDB.User.Include(user => user.RoleUsers).AsTracking().Where(user => user.Email == dtoObject.Email
                    && user.Status == Status.Active
                    && user.Id != dtoObject.Id).ToList();

                    if (userEmails.Count > 0)
                    {
                        return new ResponseAPI<UserDTO>
                        {
                            StatusCode = HttpStatusCode.Found,
                            ExceptionMessage = "This Email is used with another active user !"
                        };
                    }
                }
                List<RoleUserEntity> rolesUser = userEntity.RoleUsers.ToList();

                //Update User Data
                userEntity.FullName = dtoObject.FullName ?? userEntity.FullName;
                userEntity.Email = !string.IsNullOrEmpty(dtoObject.Email) ? dtoObject.Email : userEntity.Email;
                userEntity.NormalizedEmail = !string.IsNullOrEmpty(dtoObject.Email) ? dtoObject.Email.ToUpper() : userEntity.NormalizedEmail;
                userEntity.Gender = dtoObject.Gender ?? userEntity.Gender;
                userEntity.PhoneNumberConfirmed = dtoObject.PhoneNumberConfirmed;
                userEntity.LastModifierUserId = updateUserId;
                userEntity.LastModificationTime = DateTime.UtcNow;
                userEntity.UserType = dtoObject.UserType;
                userEntity.Picture = dtoObject.Picture ?? userEntity.Picture;
                userEntity.PasswordHash = !string.IsNullOrEmpty(dtoObject.Password) ? dtoObject.Password : userEntity.PasswordHash;
                userEntity.CountryCode = dtoObject.CountryCode;
                userEntity.PhoneNumber = dtoObject.PhoneNumber;
                userEntity.Status = dtoObject.Status;
                userEntity.CountryCode = dtoObject.CountryCode;
                userEntity.CompanyId = dtoObject.CompanyId;
                if (dtoObject.DateOfBirth != null)
                    userEntity.DateOfBirth = DateTime.SpecifyKind((DateTime)dtoObject.DateOfBirth, DateTimeKind.Utc);
                if (dtoObject.Picture != null && dtoObject.Picture != userEntity.Picture)
                {
                    string imagePath = ImageHelper.SaveImageAsync(dtoObject.Picture ?? string.Empty).Result;
                    userEntity.Picture = imagePath;

                }
                userEntity.setRoles(true);
                foreach (var roleUser in rolesUser)
                {
                    userEntity.RoleUsers.Remove(roleUser);
                }

                _userRepository.Save();

                _userRepository.Repository.Update(userEntity);
                _userRepository.Save();


                if (dtoObject.Roles != null)
                {

                    foreach (var role in dtoObject.Roles)
                    {
                        ResponseAPI<RoleDTO> response = _roleService.Get(role.Id);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            RoleBM roleBM = _mapper.Map<RoleBM>(response.ObjectValue);
                            RoleEntity roleEntity = _mapper.Map<RoleEntity>(roleBM);

                            userEntity.RoleUsers.Add(new RoleUserEntity { Role = roleEntity, User = userEntity });
                        }
                        else
                        {
                            return new ResponseAPI<UserDTO>
                            {
                                StatusCode = System.Net.HttpStatusCode.NotFound,
                                ExceptionMessage = $"Role with id : {role.Id} not found !"
                            };
                        }
                    };
                }

                _userRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_userCacheKey);
                }

                return new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = dtoObject
                };
            }
            return new ResponseAPI<UserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {dtoObject.Id} Not Found !"
            };
        }

        public ResponseAPI<UserProfileDTO> UpdateUserProfile(UserProfileDTO userProfile)
        {
            UserEntity? userEntity = _contextDB.User.Include(user => user.RoleUsers).AsTracking().FirstOrDefault(user => user.Id == userProfile.Id);
            //Update user Profile
            if (userEntity != null)
            {
                userEntity.FullName = userProfile.FullName ?? userEntity.FullName;
                userEntity.Gender = userProfile.Gender ?? userEntity.Gender;             
                if (userProfile.DateOfBirth != null)
                    userEntity.DateOfBirth = DateTime.SpecifyKind((DateTime)userProfile.DateOfBirth, DateTimeKind.Utc);
                userEntity.Email = userProfile.Email ?? userEntity.Email;
                if (userProfile.Picture != null && userProfile.Picture != userEntity.Picture)
                {
                    string imagePath = ImageHelper.SaveImageAsync(userProfile.Picture ?? string.Empty).Result;
                    userEntity.Picture = imagePath;
                }
                else
                {
                    string url = $"{_configuration.AWSS3URL}/Images/";

                    // Find the index where string2 ends in string1
                    int? index = userEntity.Picture?.IndexOf(url);

                    if (index != null && index != -1)
                    {
                        // Get the substring starting from the index where string2 ends to the end of string1
                        userEntity.Picture = userEntity.Picture?.Substring((int)(index + url.Length));
                    };
                }
                _userRepository.Repository.Update(userEntity);
                _userRepository.Save();

                return new ResponseAPI<UserProfileDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userProfile
                };
            }
            return new ResponseAPI<UserProfileDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = ""
            };
        }

        #endregion

        #region Delete
        public ResponseAPI<UserDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            UserDTO? userDTO = Get(id).ObjectValue;
            if (userDTO != null)
            {
                UserBM user = _mapper.Map<UserBM>(userDTO);

                #region Refdata
                RefDataService<UserBM>.DeleteRefData(user, deletorUserId);
                #endregion

                UserEntity userEntity = _mapper.Map<UserEntity>(user);
                _userRepository.Repository.Update(userEntity);
                _userRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_userCacheKey);
                }

                return new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userDTO
                };
            }

            return new ResponseAPI<UserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {id} Not Found !"
            };
        }
        public async Task<ResponseAPI<UserDTO>> Delete(Guid id)
        {
            UserDTO? userDTO = Get(id).ObjectValue;
            if (userDTO != null)
            {
                await UploadExcelInAWSAsync(userDTO);
                _userRepository.Repository.Delete(id);
                _userRepository.Save();

                _cache.RemoveData(_userCacheKey);

                return new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userDTO
                };
            }

            return new ResponseAPI<UserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {id} Not Found !"
            };
        }

        public ResponseAPI<UserDTO> Remove(Guid id, bool updateCache = true)
        {
            UserDTO? userDTO = Get(id).ObjectValue;
            if (userDTO != null)
            {

                _userRepository.Repository.Delete(id);
                _userRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_userCacheKey);
                }

                return new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userDTO
                };
            }

            return new ResponseAPI<UserDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {id} Not Found !"
            };
        }

        private async Task UploadExcelInAWSAsync(UserDTO user)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("Configuration/config.json", optional: false, reloadOnChange: true)
                .Build();
            var client = new AmazonS3Client(configuration["AWSAccessKey"], configuration["AWSPrivateKey"], Amazon.RegionEndpoint.USEast1);

            GetObjectRequest request = new GetObjectRequest
            {
                BucketName = configuration["AWSBucketName"],
                Key = "UserDeleted.xlsx"
            };
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            try
            {
                using (GetObjectResponse response = await client.GetObjectAsync(request))
                using (Stream excelStream = response.ResponseStream)
                {   

                    // Modify Excel file using EPPlus
                    using (ExcelPackage package = new ExcelPackage(excelStream))
                    {
                        var worksheet = package.Workbook.Worksheets.FirstOrDefault(ws => ws.Name == "Sheet1");
                        if (worksheet == null)
                        {
                            // If the worksheet doesn't exist, create it
                            worksheet = package.Workbook.Worksheets.Add("Sheet1");
                        }
                        worksheet.Cells[1, 1].Value = user.Id;
                        worksheet.Cells[1, 2].Value = user.FullName;
                        worksheet.Cells[1, 3].Value = user.Email;
                        worksheet.Cells[1, 4].Value = user.PhoneNumber;

                        // Save modified Excel file to a memory stream
                        using (MemoryStream memoryStream = new MemoryStream())
                        {
                            package.SaveAs(memoryStream);

                            // Upload modified Excel file back to S3
                            PutObjectRequest uploadRequest = new PutObjectRequest
                            {
                                BucketName = configuration["AWSBucketName"],
                                Key = "UserDeleted.xlsx",
                                InputStream = memoryStream,
                                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            };
                            await client.PutObjectAsync(uploadRequest);
                        }
                    }
                }
            }
            catch (AmazonS3Exception amazonS3Exception)
            {
                if (amazonS3Exception.ErrorCode != null &&
                    (amazonS3Exception.ErrorCode.Equals("InvalidAccessKeyId")
                    ||
                    amazonS3Exception.ErrorCode.Equals("InvalidSecurity")))
                {
                    throw new Exception("Check the provided AWS Credentials.");
                }
                else
                {
                    throw new Exception("Error occurred: " + amazonS3Exception.Message);
                }
            }
            catch(Exception e)
            {
                throw new Exception(exceptionMessages.ToString(), e);
            }
        }

        #endregion
    }

}
