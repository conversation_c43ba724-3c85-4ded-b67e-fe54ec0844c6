﻿using Application.IServices;
using Application.Services;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.Payment.WebAPI.Controllers
{
    public class QrCodeController : Controller
    {
        private readonly IQrCodeService _qrCodeService;
        private readonly ILogger<QrCodeController> _logger;

        public QrCodeController(IQrCodeService qrCodeService,
            ILogger<QrCodeController> logger)
        {
            _qrCodeService = qrCodeService;
            _logger = logger;
        }
        [Route("GenerateTransactionQrCode")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GenerateTransactionQrCode([FromBody] TransactionQrCodeDTO transactionQrCodeDTO)
        {
            try
            {
                ResponseAPI<string> transactionQrCode = _qrCodeService.GenerateTransactionQrCode(transactionQrCodeDTO);
                if (transactionQrCode.StatusCode== System.Net.HttpStatusCode.OK)
                {
                    return Ok (transactionQrCode);
                }
                return BadRequest(transactionQrCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GenerateTransactionQrCode)}");
                ResponseAPI<List<TransactionQrCodeDTO>> response = new ResponseAPI<List<TransactionQrCodeDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GenerateWalletQrCode")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GenerateWalletQrCode([FromBody] WalletQrCodeDTO WalletQrCodeDTO)
        {
            try
            {
                ResponseAPI<string> walletQrCode = _qrCodeService.GenerateWalletQrCode(WalletQrCodeDTO);
                if (walletQrCode.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(walletQrCode);
                }
                return BadRequest(walletQrCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GenerateTransactionQrCode)}");
                ResponseAPI<List<TransactionQrCodeDTO>> response = new ResponseAPI<List<TransactionQrCodeDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
    }
}
