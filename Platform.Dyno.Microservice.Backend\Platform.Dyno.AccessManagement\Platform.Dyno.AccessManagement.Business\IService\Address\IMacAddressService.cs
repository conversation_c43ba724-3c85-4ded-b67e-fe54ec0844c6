﻿using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Address
{
    public interface IMacAddressService : IGenericService<MacAddressDTO>
    {
        ResponseAPI<MacAddressDTO> SaveDevice(Guid? Id, string macAddress, bool isSaved);
        ResponseAPI<MacAddressDTO> Remove(string macAddress);
    }
}
