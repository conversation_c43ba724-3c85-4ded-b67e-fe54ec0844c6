﻿using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Transaction
{
    public interface IRefundTransactionService
    {
        ResponseAPI<RefundTransactionDTO> DemandeCancledTransaction(Guid transactionId, Guid cashierId);
        ResponseAPI<List<CashierRefundTransactionsDTO>> GetRefundDemands(Guid companyId);
        ResponseAPI<RefundTransactionDTO> ValidateCancledTransaction(Guid transactionId, Guid shopOwnerId);
        ResponseAPI<RefundTransactionDTO> RejectCancledTransaction(Guid transactionId, Guid shopOwnerId);

    }
}
