using Application.IServices;
using Platform.Dyno.Payment.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.Payment.WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DistributedTransactionController : ControllerBase
    {
        private readonly IDistributedTransactionService _distributedTransactionService;
        private readonly ILogger<DistributedTransactionController> _logger;

        public DistributedTransactionController(
            IDistributedTransactionService distributedTransactionService,
            ILogger<DistributedTransactionController> logger)
        {
            _distributedTransactionService = distributedTransactionService;
            _logger = logger;
        }

        /// <summary>
        /// Exécute une transaction distribuée sécurisée entre Neo4j et Blockchain
        /// </summary>
        /// <param name="transactionDTO">Données de la transaction</param>
        /// <param name="pinCode">Code PIN pour validation</param>
        /// <returns>Résultat de la transaction distribuée</returns>
        [HttpPost("ExecuteSecure")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ExecuteSecureTransaction(
            [FromBody] TransactionDTO transactionDTO, 
            [FromQuery] string pinCode)
        {
            try
            {
                // Validation du modèle
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Invalid transaction data"
                    });
                }

                // Récupération de l'utilisateur depuis le token JWT
                string? userIdClaim = HttpContext.User.FindFirstValue("Id");
                if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out Guid userId))
                {
                    return Unauthorized(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized"
                    });
                }

                // Validation du PIN code
                if (string.IsNullOrEmpty(pinCode))
                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "PIN code is required"
                    });
                }

                // Log de la tentative de transaction
                _logger.LogInformation("Starting distributed transaction for user {UserId}, amount {Amount}", 
                    userId, transactionDTO.Amount);

                // Exécution de la transaction distribuée
                var result = await _distributedTransactionService.ExecuteDistributedTransaction(
                    transactionDTO, userId, pinCode);

                // Log du résultat
                if (result.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    _logger.LogInformation("Distributed transaction completed successfully for user {UserId}", userId);
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning("Distributed transaction failed for user {UserId}: {Error}", 
                        userId, result.ExceptionMessage);
                    return StatusCode((int)result.StatusCode, result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in distributed transaction for user {UserId}", 
                    HttpContext.User.FindFirstValue("Id"));
                
                return StatusCode(500, new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An internal error occurred during transaction processing"
                });
            }
        }

        /// <summary>
        /// Vérifie le statut d'une transaction distribuée
        /// </summary>
        /// <param name="transactionId">ID de la transaction</param>
        /// <returns>Statut de la transaction</returns>
        [HttpGet("Status/{transactionId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetTransactionStatus(Guid transactionId)
        {
            try
            {
                // Récupération de l'utilisateur depuis le token JWT
                string? userIdClaim = HttpContext.User.FindFirstValue("Id");
                if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out Guid userId))
                {
                    return Unauthorized(new ResponseAPI<object>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized"
                    });
                }

                // TODO: Implémenter la vérification du statut
                // Pour l'instant, retourner un statut basique
                var statusResponse = new
                {
                    TransactionId = transactionId,
                    Status = "Completed", // ou "Pending", "Failed", "Compensated"
                    Timestamp = DateTime.UtcNow
                };

                return Ok(new ResponseAPI<object>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = statusResponse
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception while checking transaction status {TransactionId}", transactionId);
                
                return StatusCode(500, new ResponseAPI<object>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred while checking transaction status"
                });
            }
        }

        /// <summary>
        /// Nettoie les réservations expirées (tâche de maintenance)
        /// </summary>
        /// <returns>Nombre de réservations nettoyées</returns>
        [HttpPost("CleanupExpiredReservations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CleanupExpiredReservations()
        {
            try
            {
                // Vérifier que l'utilisateur a les permissions d'admin
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType != "Admin")
                {
                    return Unauthorized(new ResponseAPI<object>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "Admin access required"
                    });
                }

                // TODO: Implémenter le nettoyage des réservations expirées
                _logger.LogInformation("Cleanup of expired reservations requested by admin");

                var cleanupResult = new
                {
                    CleanedReservations = 0, // Nombre de réservations nettoyées
                    Timestamp = DateTime.UtcNow
                };

                return Ok(new ResponseAPI<object>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = cleanupResult
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during cleanup of expired reservations");
                
                return StatusCode(500, new ResponseAPI<object>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred during cleanup"
                });
            }
        }
    }
}
