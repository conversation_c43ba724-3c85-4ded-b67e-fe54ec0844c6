﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations.Schema;

namespace Platform.Dyno.AccessManagement.DataModel.Address
{
    [Table("Address")]
    public class AddressEntity : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string? FullAddress { get; set; }
        #endregion

        #region Structure
        [ForeignKey("UserId")]
        public UserEntity? User { get; set; }
        public Guid? UserId { get; set; }
        [ForeignKey("CompanyId")]
        public CompanyEntity? Company { get; set; }
        public Guid? CompanyId { get; set; }
        #endregion
    }
}
