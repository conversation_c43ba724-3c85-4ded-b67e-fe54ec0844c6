﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.DTO.User
{
    public class UserTokenDTO : ReferentialData
    {
        #region Data
        [Required]
        public Guid Id { get; set; }
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiredDate { get; set; }
        #endregion

        #region Structure
        public UserDTO? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
