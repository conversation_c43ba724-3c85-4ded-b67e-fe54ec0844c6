﻿using Microsoft.AspNetCore.Identity;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Notification;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.Shared.Enum;
using System.ComponentModel.DataAnnotations.Schema;

namespace Platform.Dyno.AccessManagement.DataModel.User
{
    [Table("User")]
    public class UserEntity : IdentityUser<Guid>
    {
        #region User Data
        public string PinCode { get; set; } = string.Empty;
        public string? FullName { get; set; }
        public string? Picture { get; set; } 
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public UserType UserType { get; set; }
        public string CountryCode { get; set; } = string.Empty;

        #endregion

        #region User Security
        public AuthentificationSource AuthentificationSource { get; set; }
        #endregion

        #region RefData
        public Status Status { get; set; }

        #region Creation
        public Guid? CreatorUserId { get; set; }
        public DateTime? CreationTime { get; set; }
        #endregion

        #region Modification
        public Guid? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        #endregion

        #region Deletion
        public Guid? DeleterUserId { get; set; }
        public DateTime? DeletionTime { get; set; }
        #endregion

        #endregion

        #region Structure
        
        public ICollection<RoleUserEntity> RoleUsers { get; set; } = new List<RoleUserEntity>();

        private bool _clear;
        public IEnumerable<RoleEntity>? Roles { get => !_clear ? RoleUsers?.Select(ur => ur.Role).ToList() : new List<RoleEntity>();} 
        public IList<AddressEntity>? Addresses { get; set; }
        public ICollection<MacAddressEntity>? MacAddresses { get; set; }
        public ICollection<UserOTPEntity>? UserOTPs { get; set; }
        public ICollection<UserTokenEntity>? UserTokens { get; set; }
        public ICollection<SubscriberDeviceEntity>? SubscriberDevices { get; set; }
        public Guid CompanyId { get; set; }
        #endregion


        public void setRoles(bool clear)
        {
            if(clear) Roles?.ToList().Clear();

            this._clear = clear;
        }
    }
}
