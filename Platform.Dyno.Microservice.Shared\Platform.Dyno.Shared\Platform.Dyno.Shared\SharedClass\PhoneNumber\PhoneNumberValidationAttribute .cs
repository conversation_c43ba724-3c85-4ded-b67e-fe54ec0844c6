﻿using PhoneNumbers;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass.PhoneNumber
{
    public class PhoneNumberValidatorAttribute : ValidationAttribute
    {
        private readonly string _countryCode;

        public PhoneNumberValidatorAttribute(string countryCode)
        {
            _countryCode = countryCode;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var phoneNumberUtil = PhoneNumberUtil.GetInstance();
            var phoneNumber = value?.ToString() ?? "";

            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return ValidationResult.Success; 
            }

            try
            {
                var parsedPhoneNumber = phoneNumberUtil.Parse(phoneNumber, _countryCode);
                if (!phoneNumberUtil.IsValidNumberForRegion(parsedPhoneNumber, _countryCode))
                {
                    return new ValidationResult("Le numéro de téléphone n'est pas valide pour le pays spécifié.");
                }
            }
            catch (NumberParseException)
            {
                return new ValidationResult("Le format du numéro de téléphone est invalide.");
            }

            return ValidationResult.Success;
        }
    }
}