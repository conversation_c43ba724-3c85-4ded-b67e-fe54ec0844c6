﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Application.Interfaces;
using Infrastructure.DatabaseConnection;
using Infrastructure.Repository;
using Platform.Dyno.Shared;   
using Neo4j.Driver;

namespace Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddServices(this IServiceCollection services,
            Configuration.Config configuration)
        {
            // 1) Crée et vérifie le driver **immédiatement**
            var driver = Neo4jInit.CreateDriver(
                configuration.NEO4J_URI,
                configuration.NEO4J_USERNAME,
                configuration.NEO4J_PASSWORD
            );

            // 2) Enregistre ce driver en singleton
            services.AddSingleton<IDriver>(driver);

            // 3) Vos autres services/repos
            services.AddScoped<IGenericRepository, GenericRepository>();

            return services;
        }
    }
}
