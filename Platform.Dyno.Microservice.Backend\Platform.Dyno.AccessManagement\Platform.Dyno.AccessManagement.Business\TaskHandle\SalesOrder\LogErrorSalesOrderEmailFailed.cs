﻿using Camunda.Worker;
using Camunda.Worker.Variables;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.Business.Service.Logger;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Logger;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("SO_Log_Error_Email_Failed")]
    public class LogErrorSalesOrderEmailFailed : IExternalTaskHandler
    {
        private readonly ILogger<LogErrorSalesOrderEmailFailed> _logger;
        private readonly ILogErrorService _logErrorService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public LogErrorSalesOrderEmailFailed(
        ILogger<LogErrorSalesOrderEmailFailed> logger,
        ILogErrorService logErrorService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId)

        {
            _logger = logger;
            _logErrorService = logErrorService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
                //SalesOrderDTO salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

                string? error = null;

                if (externalTask.Variables.TryGetValue("Error_Send_Email_SO", out var errorVar))
                {
                    if (errorVar is StringVariable stringVar)
                    {
                        error = stringVar.Value;
                    }
                    else if (errorVar is JsonVariable jsonVar)
                    {
                        error = (string?)jsonVar.Value; // Si l'erreur est stockée en JSON
                    }
                    else
                    {
                        _logger.LogWarning("Type inattendu pour Error_Send_Email_SO : {Type}", errorVar.GetType().Name);
                    }
                }
                else
                {
                    _logger.LogDebug("Aucune erreur trouvée dans Error_Send_Email_SO");
                }



                // string? error = externalTask.Variables["Error_Send_Email_SO"].Value?.ToString();


                if (salesOrderDTO == null)
                {
                    outputVariables.Add("Result_SO_Log_Error_PDF_Failed", new BooleanVariable(false));
                    outputVariables.Add("Error_SO_Log_Error_PDF_Failed", new StringVariable("json conversion failed"));

                }
                else
                {
                    LogErrorDTO logErrorDTO = new LogErrorDTO()
                    {
                        Id = Guid.NewGuid(),
                        Microservice = MicroserviceName.AccessManagement,
                        API = $"invoiceService/CreatePDF/{salesOrderDTO.Id}",
                        Error = error,
                        Type = ErrorType.SO_Email_Failed,
                        CreationDate = DateTime.UtcNow
                    };

                    var response = _logErrorService.Create(logErrorDTO);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.Created)
                    {
                        outputVariables.Add("Result_SO_Log_Error_PDF_Failed", new BooleanVariable(true));
                    }
                    else
                    {
                        outputVariables.Add("Result_SO_Log_Error_PDF_Failed", new BooleanVariable(false));
                        outputVariables.Add("Error_SO_Log_Error_PDF_Failed", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SO_Log_Error_PDF_Failed", new BooleanVariable(false));
                outputVariables.Add("Error_SO_Log_Error_PDF_Failed", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
