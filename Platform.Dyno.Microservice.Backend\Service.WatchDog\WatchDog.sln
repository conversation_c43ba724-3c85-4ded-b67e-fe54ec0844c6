﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WatchDog", "WatchDog\WatchDog.csproj", "{39FA1171-2B81-409E-A9B4-3465CF6220A9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DynoTools", "..\DynoTools\DynoTools\DynoTools.csproj", "{08EE3128-D121-44B3-93E1-769E9FE6299F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Server.Kafka", "..\Server.Kafka\Server.Kafka\Server.Kafka.csproj", "{681B601A-CCE1-4449-A5AC-98DB32F639A1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Shared", "..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj", "{EABDE62A-BC8C-4808-8EDC-79F4833240A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Notification.DTO", "..\Platform.Dyno.Notification\Platform.Dyno.Notification.DTO\Platform.Dyno.Notification.DTO.csproj", "{6B5E9644-82A5-4F74-A0ED-1CD10B6F10DD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{39FA1171-2B81-409E-A9B4-3465CF6220A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39FA1171-2B81-409E-A9B4-3465CF6220A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39FA1171-2B81-409E-A9B4-3465CF6220A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39FA1171-2B81-409E-A9B4-3465CF6220A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{08EE3128-D121-44B3-93E1-769E9FE6299F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08EE3128-D121-44B3-93E1-769E9FE6299F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08EE3128-D121-44B3-93E1-769E9FE6299F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08EE3128-D121-44B3-93E1-769E9FE6299F}.Release|Any CPU.Build.0 = Release|Any CPU
		{681B601A-CCE1-4449-A5AC-98DB32F639A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{681B601A-CCE1-4449-A5AC-98DB32F639A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{681B601A-CCE1-4449-A5AC-98DB32F639A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{681B601A-CCE1-4449-A5AC-98DB32F639A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{EABDE62A-BC8C-4808-8EDC-79F4833240A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EABDE62A-BC8C-4808-8EDC-79F4833240A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EABDE62A-BC8C-4808-8EDC-79F4833240A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EABDE62A-BC8C-4808-8EDC-79F4833240A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B5E9644-82A5-4F74-A0ED-1CD10B6F10DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B5E9644-82A5-4F74-A0ED-1CD10B6F10DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B5E9644-82A5-4F74-A0ED-1CD10B6F10DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B5E9644-82A5-4F74-A0ED-1CD10B6F10DD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {473A69D2-0725-409D-8C09-D05A3ECF303B}
	EndGlobalSection
EndGlobal
