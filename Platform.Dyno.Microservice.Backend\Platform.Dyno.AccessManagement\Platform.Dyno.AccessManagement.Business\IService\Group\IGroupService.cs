﻿using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Group;

public interface IGroupService : IGenericService<GroupDTO>
{
    PagedList<GroupDTO> sortData(PagedParameters pagedParameters, ResponseAPI<PagedList<GroupDTO>> groupsDTO);
    PagedList<GroupDTO> filterData(PagedParameters pagedParameters, ResponseAPI<PagedList<GroupDTO>> groupsDTO);


}
