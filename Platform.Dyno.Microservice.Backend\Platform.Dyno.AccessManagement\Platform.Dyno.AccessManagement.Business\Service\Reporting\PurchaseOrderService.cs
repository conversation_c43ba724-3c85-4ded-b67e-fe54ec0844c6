﻿using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.BusinessModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Reporting
{
    public class PurchaseOrderService : IPurchaseOrderService
    {
        private readonly IUnitOfWork<DocumentsEntity> _purchaseOrderRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly string _purchaseOrderCacheKey = RedisCacheKey.PurchaseOrderCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("purchaseOrder");

        public PurchaseOrderService(IUnitOfWork<DocumentsEntity> purchaseOrderRepository,
            IMapper mapper,
            IRedisCacheService cache,
            IWebHostEnvironment webHostEnvironment)
        {
            _purchaseOrderRepository = purchaseOrderRepository;
            _mapper = mapper;
            _cache = cache;
            _webHostEnvironment = webHostEnvironment;
        }

        #region Get
        public ResponseAPI<List<DocumentsDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if (userType == UserType.Client || userType == UserType.Cashier)
            {
                return new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }
            List<DocumentsDTO>? purchaseOrdersDTO = _cache.GetData<List<DocumentsDTO>>(_purchaseOrderCacheKey);
            if (purchaseOrdersDTO == null || purchaseOrdersDTO.Count() == 0)
            {
                List<DocumentsEntity> purchaseOrderEntities = (List<DocumentsEntity>)_purchaseOrderRepository.Repository.GetAll().Where(document => document.Type == DocumentType.SalesOrder);
                List<DocumentsBM> purchaseOrdersBM = _mapper.Map<List<DocumentsBM>>(purchaseOrderEntities);
                purchaseOrdersDTO = _mapper.Map<List<DocumentsDTO>>(purchaseOrdersBM);

                _cache.SetData(_purchaseOrderCacheKey, purchaseOrdersDTO, DateTimeOffset.UtcNow.AddDays(1));
            }

            if (userType != UserType.SuperAdmin)
            {
                purchaseOrdersDTO = purchaseOrdersDTO.Where(purchaseOrder => purchaseOrder.CompanyId == companyId).ToList();
            }

            return new ResponseAPI<List<DocumentsDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = purchaseOrdersDTO
            };
        }

        public ResponseAPI<PagedList<DocumentsDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<DocumentsDTO>> purchaseOrdersDTO = GetAll(companyId, userType);
            PagedList<DocumentsDTO>? pagedList = null;
            if (purchaseOrdersDTO.ObjectValue != null)
            {
                pagedList = PagedList<DocumentsDTO>.ToGenericPagedList(purchaseOrdersDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<DocumentsDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<DocumentsDTO>> purchaseOrdersDTO = GetAll(companyId, userType);
            PagedList<DocumentsDTO>? pagedList = null;
            if (purchaseOrdersDTO.ObjectValue != null)
            {
                pagedList = PagedList<DocumentsDTO>.ToGenericPagedList(purchaseOrdersDTO.ObjectValue.Where(purchaseOrder => purchaseOrder.Status == Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<DocumentsDTO> Get(Guid id)
        {
            DocumentsDTO? purchaseOrderDTO = GetAll().ObjectValue?.Where(purchaseOrder => purchaseOrder.Id == id).FirstOrDefault();
            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = purchaseOrderDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = purchaseOrderDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = purchaseOrderDTO
            };
        }

        public ResponseAPI<List<DocumentsDTO>> Get(Func<DocumentsDTO, bool> expression)
        {
            List<DocumentsDTO>? purchaseOrdersDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = purchaseOrdersDTO
            };
        }

        #endregion

        #region Create
        public ResponseAPI<DocumentsDTO> Create(DocumentsDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            DocumentsBM document = _mapper.Map<DocumentsBM>(dtoObject);

            #region changed data
            document.Id = Guid.NewGuid();
            document.Name = $"PurchaseOrder_{document.Id}_{DateTime.UtcNow}";
            document.Type = DocumentType.SalesOrder;
            document.CompanyId = companyId;
            RefDataService<DocumentsBM>.CreateRefData(document, document.Id);
            #endregion

            DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
            _purchaseOrderRepository.Repository.Insert(documentEntity);
            _purchaseOrderRepository.Save();

            dtoObject.Id = documentEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_purchaseOrderCacheKey);
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public ResponseAPI<string> CreatePDF(string code, ProductType productType, double dynoAmount, double netAmount, double VATAmount, double totalAmount, PaymentMethod paymentMethod, Guid companyId)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region Update
        public ResponseAPI<DocumentsDTO> Update(DocumentsDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                DocumentsBM document = _mapper.Map<DocumentsBM>(dtoObject);

                #region RefData
                RefDataService<DocumentsBM>.UpdateRefData(document, document.Id);
                #endregion

                DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
                _purchaseOrderRepository.Repository.Update(documentEntity);
                _purchaseOrderRepository.Save();

                dtoObject.Id = documentEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_purchaseOrderCacheKey);
                }

                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<DocumentsDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            DocumentsDTO? documentDto = Get(id).ObjectValue;
            if (documentDto != null)
            {
                DocumentsBM document = _mapper.Map<DocumentsBM>(documentDto);

                #region RefData
                RefDataService<DocumentsBM>.DeleteRefData(document, document.Id);
                #endregion

                DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
                _purchaseOrderRepository.Repository.Update(documentEntity);
                _purchaseOrderRepository.Save();

                documentDto.Id = documentEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_purchaseOrderCacheKey);
                }

                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = documentDto
                };
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }     
        #endregion
    }
}
