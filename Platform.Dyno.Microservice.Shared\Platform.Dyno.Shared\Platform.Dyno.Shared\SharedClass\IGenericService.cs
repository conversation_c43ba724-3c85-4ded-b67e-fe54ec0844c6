﻿using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass
{
    public interface IGenericService<T> where T : class
    {
        public ResponseAPI<List<T>> GetAll(Guid? companyId = null, UserType? userType = null);
        public ResponseAPI<PagedList<T>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null);
        public ResponseAPI<PagedList<T>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null);
        public ResponseAPI<T> Get(Guid id);
        public ResponseAPI<List<T>> Get(Func<T, bool> expression);
        public ResponseAPI<T> Create(T dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null);
        public ResponseAPI<T> Update(T dtoObject, Guid? updateUserId = null, bool updateCache = true);
        public ResponseAPI<T> Delete(Guid id, Guid? deletorUserId = null , bool updateCache = true);
    }
}
