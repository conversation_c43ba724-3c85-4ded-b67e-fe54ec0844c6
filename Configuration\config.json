{"AccessAddress": "http://127.0.0.1:7274", "NotificationAddress": "http://localhost:7038", "PaymentAddress": "http://localhost:7018", "PaymentBlockchainAddress": "https://ab0c-196-203-166-66.ngrok-free.app", "PlatformAdminAddress": "http://localhost:80", "RedisAddress": "dynocachevalkey-t82169.serverless.use1.cache.amazonaws.com:6379", "CamundaAddress": "http://127.0.0.1:8080/engine-rest", "KafkaServer": "ec2-3-216-78-149.compute-1.amazonaws.com:9092", "KafkaGroupId": "watchDog", "AWSS3URL": "https://dynofiles.s3.amazonaws.com", "LogoURL": "https://dynofiles.s3.amazonaws.com/Images/Logo.png", "IPServer": "accessmanagement.c9y0a2c0g03h.us-east-1.rds.amazonaws.com", "Port": 5432, "User": "D<PERSON><PERSON>", "Password": "Dyno123++", "AccessManagement": "AccessManagement", "Key": "7e3344ab-22b2-497d-a294-a998f9950723", "TokenLifeTime": 120, "Issuer": "SecureApi", "RefreshTokenLifeTime": 30, "ValidIssuer": "SecureApi", "NEO4J_URI": "neo4j+s://c3139ebb.databases.neo4j.io", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "W8S--3wtq05qE4tYKrujbYukFcRj_nzaIC1UrYTwoho", "AURA_INSTANCEID": "c3139ebb", "AURA_INSTANCENAME": "Instance01", "EmailFrom": "<EMAIL>", "SmtpServer": "ssl0.ovh.net", "EmailPort": 465, "Email": "<EMAIL>", "EmailPassword": "OLfaa6639;"}