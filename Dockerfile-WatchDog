# Use the official .NET 6 SDK image as the build environment
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy the Dockerfile into the build context
COPY . .
COPY Configuration /app/Configuration/

# Restore NuGet packages for the WebAPI project
RUN dotnet restore "Platform.Dyno.Microservice.Backend/Service.WatchDog/WatchDog/WatchDog.csproj" --disable-parallel

# Publish the WebAPI project
RUN dotnet publish "Platform.Dyno.Microservice.Backend/Service.WatchDog/WatchDog/WatchDog.csproj" -c release -o /app/published-app --no-restore

# Use a separate stage for publishing the application
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS publish
WORKDIR /app

# Copy the published files from the build stage
COPY --from=build /app/published-app .

# Set the entry point for the application
ENTRYPOINT ["dotnet", "WatchDog.dll"]