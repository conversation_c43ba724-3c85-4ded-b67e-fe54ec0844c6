﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.RoleManagement
{
    public class PermissionService : IPermissionService
    {
        private readonly IUnitOfWork<PermissionEntity> _permisionRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _permissionCacheKey = RedisCacheKey.PermissionCacheKey;
        private readonly ConfigurationDefaultId _defaultId;
        public PermissionService(IUnitOfWork<PermissionEntity> permisionRepository,
            IMapper mapper,
            IRedisCacheService cache,
            ConfigurationDefaultId defaultId) 
        {
            _permisionRepository= permisionRepository;
            _mapper= mapper;
            _cache= cache;
            _defaultId= defaultId;
        }

        #region Get
        public ResponseAPI<List<PermissionDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            List<PermissionDTO>? permissionsDTO = _cache.GetData<List<PermissionDTO>>(_permissionCacheKey);
            if (permissionsDTO == null || permissionsDTO.Count == 0)
            {
                List<PermissionEntity> permissionEntities = (List<PermissionEntity>)_permisionRepository.Repository.GetAll(permission => permission.IsRoot == true, includes: new List<string> { "Role", "Permissions", "Permissions.Permissions" }, orderBy: permission => permission.OrderByDescending(permission => permission.LastModificationTime));
                List<PermissionBM> permissions = _mapper.Map<List<PermissionBM>>(permissionEntities);
                permissionsDTO = _mapper.Map<List<PermissionDTO>>(permissions);

                _cache.SetData(_permissionCacheKey, permissionsDTO, DateTimeOffset.UtcNow.AddDays(1));

            }

            ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = permissionsDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<PermissionDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<PermissionDTO>> permissionsDTO = GetAll(companyId, userType);
            PagedList<PermissionDTO>? pagedList = null;
            if (permissionsDTO.ObjectValue != null)
            {
                pagedList = PagedList<PermissionDTO>.ToGenericPagedList(permissionsDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<PermissionDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<PermissionDTO>> permissionsDTO = GetAll(companyId, userType);
            PagedList<PermissionDTO>? pagedList = null;
            if (permissionsDTO.ObjectValue != null)
            {
                pagedList = PagedList<PermissionDTO>.ToGenericPagedList(permissionsDTO.ObjectValue.Where(permission => permission.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PermissionDTO> Get(Guid id)
        {
            PermissionDTO? permissionDTO = GetAll().ObjectValue?.Where(permission => permission.Id == id).FirstOrDefault();
            return new ResponseAPI<PermissionDTO>()
            {
                StatusCode = permissionDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = permissionDTO == null ? $"Permission with id {id} Not Found !" : null,
                ObjectValue = permissionDTO
            };
        }

        public ResponseAPI<List<PermissionDTO>> Get(Func<PermissionDTO, bool> expression)
        {
            List<PermissionDTO>? permissionsDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = permissionsDTO
            };
        }

        public ResponseAPI<List<PermissionDTO>> GetAllList()
        {
            List<PermissionDTO>? permissionsDTO = _cache.GetData<List<PermissionDTO>>(_permissionCacheKey);
            if (permissionsDTO == null || permissionsDTO.Count == 0)
            {
                List<PermissionEntity> permissionEntities = (List<PermissionEntity>)_permisionRepository.Repository.GetAll();
                List<PermissionBM> permissions = _mapper.Map<List<PermissionBM>>(permissionEntities);
                permissionsDTO = _mapper.Map<List<PermissionDTO>>(permissions);

            }

            ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = permissionsDTO,
            };
            return response;
        }

        public ResponseAPI<List<PermissionDTO>> GetList(Func<PermissionDTO, bool> expression)
        {
            List<PermissionDTO>? permissionsDTO = GetAllList().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = permissionsDTO
            };
        }

        public ResponseAPI<List<PermissionDTO>> GetAllAvailablePermissionsByUser(UserDTO user, bool updateCache = true)
        {
            List<PermissionDTO>? permissionsDTO = _cache.GetData<List<PermissionDTO>>(_permissionCacheKey + user.Id);
            if (permissionsDTO == null || permissionsDTO.Count == 0)
            {
                permissionsDTO = new List<PermissionDTO>();
                var userRoles = user.Roles;
                if (userRoles != null)
                {
                    foreach (var role in userRoles)
                    {
                        var rolePermissions = GetList(permission => permission.RoleId == role.Id && permission.PartialSelected == false).ObjectValue as List<PermissionDTO>;
                        if (rolePermissions != null)
                        {
                            foreach (var permission in rolePermissions)
                            {
                                if (!permissionsDTO.Any(x => x.Name == permission.Name))
                                {
                                    permissionsDTO.Add(permission);
                                }
                            }
                        }
                    }
                    if (permissionsDTO.Count > 0)
                    {
                        List<PermissionBM>? permissionBM = _mapper.Map<List<PermissionBM>>(permissionsDTO);
                        List<PermissionEntity>? permissionEntity = _mapper.Map<List<PermissionEntity>>(permissionBM);
                        permissionsDTO = CreatePermissionList(permissionEntity);
                    }
                }

                _cache.SetData(_permissionCacheKey + user.Id, permissionsDTO, DateTimeOffset.UtcNow.AddDays(1));
            }
            ResponseAPI<List<PermissionDTO>> response = new ResponseAPI<List<PermissionDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = permissionsDTO,
            };
            return response;
        }

        #endregion

        #region Create
        public ResponseAPI<PermissionDTO> Create(PermissionDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            PermissionBM permission = _mapper.Map<PermissionBM>(dtoObject);

            #region changed data
            permission.Id = Guid.NewGuid();
            RefDataService<PermissionBM>.CreateRefData(permission, creatorUserId);
            #endregion

            PermissionEntity permissionEntity = _mapper.Map<PermissionEntity>(permission);
            _permisionRepository.Repository.Insert(permissionEntity);
            _permisionRepository.Save();

            dtoObject.Id = permissionEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_permissionCacheKey);
            }

            return new ResponseAPI<PermissionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public List<PermissionDTO>? CreatePermissionList(List<PermissionEntity> permissionEntities)
        {

            List<PermissionDTO>? allListPermission = new List<PermissionDTO>();
            if (permissionEntities != null && permissionEntities.Count > 0)
            {
                allListPermission = _cache.GetData<List<PermissionDTO>>(_permissionCacheKey)?
                    .Where(permission => permission.RoleId == _defaultId.RoleId && permission.Status == Shared.Enum.Status.Active)
                    .ToList();
                if (allListPermission == null || allListPermission.Count == 0)
                {
                    List<PermissionEntity> allListpermissionEntities = (List<PermissionEntity>)_permisionRepository.Repository.GetAll(permission => permission.IsRoot == true && permission.RoleId == _defaultId.RoleId, includes: new List<string> { "Role", "Permissions", "Permissions.Permissions" }, orderBy: permission => permission.OrderByDescending(permission => permission.LastModificationTime));
                    List<PermissionBM> allListpermissions = _mapper.Map<List<PermissionBM>>(allListpermissionEntities);
                    allListPermission = _mapper.Map<List<PermissionDTO>>(allListpermissions);

                    _cache.SetData(_permissionCacheKey, allListPermission, DateTimeOffset.UtcNow.AddDays(1));

                }
                if (allListPermission != null && allListPermission.Count > 0)
                {
                    foreach (PermissionDTO rootPermission in allListPermission)
                    {
                        foreach (PermissionEntity permission in permissionEntities)
                        {
                            if (permission.Name == rootPermission.Name)
                            {
                                rootPermission.IsSelectable = true;
                                //allListPermission => false
                                if (rootPermission.Permissions != null)
                                {
                                    foreach (var permissionSelected in rootPermission.Permissions)
                                    {
                                        permissionSelected.IsSelectable = true;
                                    }
                                }

                            }
                        }
                        if (rootPermission.Permissions != null && rootPermission.Permissions.Count > 0)
                        {
                            int countPartial = 0;
                            foreach (PermissionDTO partialPermission in rootPermission.Permissions)
                            {
                                int countLeaf = 0;
                                foreach (PermissionEntity permission2 in permissionEntities)
                                {
                                    if (permission2.Name == partialPermission.Name)
                                    {
                                        countPartial++;
                                        partialPermission.IsSelectable = true;
                                        //all list Permission children of partial permission => false
                                        if (partialPermission.Permissions != null)
                                        {
                                            foreach (var partialPermissionSelected in partialPermission.Permissions)
                                            {
                                                partialPermissionSelected.IsSelectable = true;
                                            }
                                        }

                                    }
                                }
                                        if (partialPermission.Permissions != null && partialPermission.Permissions.Count > 0)
                                        {
                                            

                                            foreach (PermissionDTO leafPermission in partialPermission.Permissions)
                                            {

                                                foreach (PermissionEntity permission3 in permissionEntities)
                                                {
                                                    if (permission3.Name == leafPermission.Name)
                                                    {
                                                        countLeaf++;
                                                        leafPermission.IsSelectable = true;
                                                    }
                                                }


                                                leafPermission.Id = Guid.NewGuid();
                                                leafPermission.Role = null;
                                            }

                                            
                                        }

                                    

                                    partialPermission.Id = Guid.NewGuid();
                                    partialPermission.Role = null;
                                

                                if (countLeaf == partialPermission?.Permissions?.Count && countLeaf > 0)
                                {
                                    partialPermission.IsSelectable = true;
                                    countPartial++;
                                }

                            }
                            if (countPartial == rootPermission.Permissions.Count)
                            {
                                rootPermission.IsSelectable = true;
                            }

                        }

                        rootPermission.Id = Guid.NewGuid();
                        rootPermission.Role = null;
                    }

                }

            }
            return allListPermission;
        }

        public List<PermissionEntity>? CreatePermissionList(List<PermissionEntity>? permissionEntities, Guid? roleId, UserDTO user)
        {

            List<PermissionDTO>? allListPermission = new List<PermissionDTO>();
            if (permissionEntities != null && permissionEntities.Count > 0)
            {
                allListPermission = GetAllAvailablePermissionsByUser(user).ObjectValue;

                if (allListPermission != null && allListPermission.Count > 0)
                {
                    foreach (PermissionDTO rootPermission in allListPermission)
                    {
                        foreach (PermissionEntity permission in permissionEntities)
                        {
                            if (permission.Name == rootPermission.Name)
                            {
                                rootPermission.PartialSelected = false;
                                //allListPermission => false
                                if (rootPermission.Permissions != null)
                                {
                                    foreach (var permissionSelected in rootPermission.Permissions)
                                    {
                                        permissionSelected.PartialSelected = false;
                                    }
                                }

                            }
                        }
                        if (rootPermission.Permissions != null && rootPermission.Permissions.Count > 0)
                        {
                            int countPartial = 0;
                            foreach (PermissionDTO partialPermission in rootPermission.Permissions)
                            {
                                foreach (PermissionEntity permission2 in permissionEntities)
                                {
                                    if (permission2.Name == partialPermission.Name)
                                    {
                                        countPartial++;
                                        partialPermission.PartialSelected = false;
                                        //all list Permission children of partial permission => false
                                        if (partialPermission.Permissions != null)
                                        {
                                            foreach (var partialPermissionSelected in partialPermission.Permissions)
                                            {
                                                partialPermissionSelected.PartialSelected = false;
                                            }
                                        }

                                    }
                                }
                                if (partialPermission.Permissions != null && partialPermission.Permissions.Count > 0)
                                {
                                    int countLeaf = 0;

                                    foreach (PermissionDTO leafPermission in partialPermission.Permissions)
                                    {

                                        foreach (PermissionEntity permission3 in permissionEntities)
                                        {
                                            if (permission3.Name == leafPermission.Name)
                                            {
                                                countLeaf++;
                                                leafPermission.PartialSelected = false;
                                            }
                                        }


                                        leafPermission.Id = Guid.NewGuid();
                                        leafPermission.RoleId = roleId;
                                        leafPermission.Role = null;
                                    }

                                    if (countLeaf < partialPermission.Permissions.Count && countLeaf > 0)
                                    {
                                        partialPermission.PartialSelected = true;
                                    }
                                }
                                partialPermission.Id = Guid.NewGuid();
                                partialPermission.RoleId = roleId;
                                partialPermission.Role = null;

                            }
                            if (countPartial < rootPermission.Permissions.Count)
                            {
                                rootPermission.PartialSelected = true;
                            }

                        }

                        rootPermission.Id = Guid.NewGuid();
                        rootPermission.RoleId = roleId;
                        rootPermission.Role = null;
                    }

                }

            }
            List<PermissionBM> permissionBM = _mapper.Map<List<PermissionBM>>(allListPermission);
            List<PermissionEntity> allPermissionEntity = _mapper.Map<List<PermissionEntity>>(permissionBM);
            return allPermissionEntity;
        }

        #endregion

        #region Update
        public ResponseAPI<PermissionDTO> Update(PermissionDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                PermissionBM permission = _mapper.Map<PermissionBM>(dtoObject);

                #region RefData
                RefDataService<PermissionBM>.UpdateRefData(permission, updateUserId);
                #endregion

                PermissionEntity permissionEntity = _mapper.Map<PermissionEntity>(permission);
                _permisionRepository.Repository.Update(permissionEntity);
                _permisionRepository.Save();
                dtoObject.Id = permissionEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_permissionCacheKey);
                }

                return new ResponseAPI<PermissionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<PermissionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Permission with id {dtoObject.Id} Not Found !"
            };
        }

        #endregion

        #region Delete
        public ResponseAPI<PermissionDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            PermissionDTO? permissionDTO = GetAll().ObjectValue?.Where(dto => dto.Id == id).FirstOrDefault();
            if (permissionDTO != null)
            {
                PermissionBM permission = _mapper.Map<PermissionBM>(permissionDTO);

                #region Refdata
                RefDataService<PermissionBM>.DeleteRefData(permission, deletorUserId);
                #endregion

                PermissionEntity permissionEntity = _mapper.Map<PermissionEntity>(permission);
                _permisionRepository.Repository.Update(permissionEntity);
                _permisionRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_permissionCacheKey);
                }

                return new ResponseAPI<PermissionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = permissionDTO
                };
            }

            return new ResponseAPI<PermissionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Permission with id {id} Not Found !"
            };
        }

        public ResponseAPI<PermissionDTO> DeleteAllPermissionInRole(Guid roleId, Guid? deletorUserId = null, bool updateCache = true)
        {
            List<PermissionEntity>? permissionEntityList = _permisionRepository.Repository.GetAll().Where(dto => dto.RoleId == roleId).ToList();

            List<PermissionEntity>? rootPermissionEntityList = permissionEntityList.Where(per => per.IsRoot == true).ToList();
            List<PermissionEntity>? leafPermissionEntityList = permissionEntityList.Where(per => per.IsLeaf == true).ToList();
            List<PermissionEntity>? partialPermissionEntityList = permissionEntityList.Where(per => per.IsRoot == false && per.IsLeaf == false).ToList();
            if (permissionEntityList != null && permissionEntityList.Count > 0)
            {


                if (permissionEntityList != null && leafPermissionEntityList.Count > 0)
                {
                    foreach (var permissionEntity in leafPermissionEntityList)
                    {
                        _permisionRepository.Repository.Delete(permissionEntity.Id);
                        _permisionRepository.Save();
                    }
                }
                if (permissionEntityList != null && partialPermissionEntityList.Count > 0)
                {
                    foreach (var permissionEntity in partialPermissionEntityList)
                    {
                        _permisionRepository.Repository.Delete(permissionEntity.Id);
                        _permisionRepository.Save();
                    }
                }
                if (permissionEntityList != null && rootPermissionEntityList.Count > 0)
                {
                    foreach (var permissionEntity in rootPermissionEntityList)
                    {
                        _permisionRepository.Repository.Delete(permissionEntity.Id);
                        _permisionRepository.Save();
                    }

                }
                return new ResponseAPI<PermissionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = $"All permission in role with id {roleId} are deleted !"
                };
            }
            return new ResponseAPI<PermissionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"no permissions found for role with id {roleId}!"
            };

        }
        #endregion
    }
}
