﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.Service.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.Request;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.SalesOrder
{
    [Route("Api/[controller]")]
    [ApiController]
    public class SalesInvoiceController : ControllerBase
    {
        private readonly ISalesInvoiceService _salesInvoiceService;
        private readonly IUserService _userService;
        private readonly ILogger<SalesInvoiceController> _logger;
        private readonly ISortingUtility _sortingUtility;

        public SalesInvoiceController(ISalesInvoiceService salesInvoiceService, IUserService userService,
            ILogger<SalesInvoiceController> logger, ISortingUtility sortingUtility)
        {
            _salesInvoiceService = salesInvoiceService;
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<SalesInvoiceDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    return Unauthorized(new ResponseAPI<SalesInvoiceDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<SalesInvoiceDTO>> salesInvoices = _salesInvoiceService.GetAll(companyId, enumUserType);
                    return Ok(salesInvoices);
                }

                return Unauthorized(new ResponseAPI<SalesInvoiceDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "user not authorized"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<SalesInvoiceDTO>> response = new ResponseAPI<List<SalesInvoiceDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    return Unauthorized(new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<SalesInvoiceDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<SalesInvoiceDTO>> salesInvoicesDTO = _salesInvoiceService.GetAll(pagedParameters, companyId, enumUserType);
                    if (salesInvoicesDTO.ObjectValue != null)
                    {
                        salesInvoicesDTO.ObjectValue = _sortingUtility.SortData(pagedParameters, salesInvoicesDTO);
                        //salesInvoicesDTO.ObjectValue = _sortingUtility.FilterData(pagedParameters, salesInvoicesDTO);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = salesInvoicesDTO.ObjectValue.TotalCount,
                            PageSize = salesInvoicesDTO.ObjectValue.PageSize,
                            CurrentPage = salesInvoicesDTO.ObjectValue.CurrentPage,
                            TotalPages = salesInvoicesDTO.ObjectValue.TotalPages,
                            HasNext = salesInvoicesDTO.ObjectValue.HasNext,
                            HasPrevious = salesInvoicesDTO.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(salesInvoicesDTO);
                }

                return Unauthorized(new ResponseAPI<SalesInvoiceDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "user not authorized"
                });
                

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }

                ResponseAPI<SalesInvoiceDTO> salesInvoiceDTOResponse = _salesInvoiceService.Get(id);
                if (salesInvoiceDTOResponse.ObjectValue == null)
                {
                    return BadRequest(salesInvoiceDTOResponse);
                }
                return Ok(salesInvoiceDTOResponse);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }

        [Route("GetAllByStatus")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //public IActionResult GetAllByStatus([FromQuery] PagedParameters pagedParameters, [FromBody] List<InvoiceStatus> invoiceStatus)
        public IActionResult GetAllByStatus([FromQuery] SalesInvoiceStatusRequest request)
        {
            try
            {
                if (request == null || request.invoiceStatus == null || request.PagedParameters == null || !request.invoiceStatus.Any())
                {
                    var response = new ResponseAPI<SalesInvoiceDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Paged parameters and sales order status list are required"
                    };
                    return BadRequest(response);
                }
                PagedParameters pagedParameters = request.PagedParameters;
                List<InvoiceStatus> invoiceStatus = request.invoiceStatus;
                List<InvoiceStatus> salesOrderStatus = request.invoiceStatus; 
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<SalesInvoiceDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<SalesInvoiceDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<PagedList<SalesInvoiceDTO>> invoicesDTOResponse = _salesInvoiceService.GetByStatus(companyId, isSuperAdminResponse.ObjectValue, invoiceStatus, pagedParameters);
                if (invoicesDTOResponse.ObjectValue != null)
                {
                    invoicesDTOResponse.ObjectValue = _sortingUtility.SortData(pagedParameters, invoicesDTOResponse);
                    invoicesDTOResponse.ObjectValue = _sortingUtility.FilterData(pagedParameters, invoicesDTOResponse);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = invoicesDTOResponse.ObjectValue.TotalCount,
                        PageSize = invoicesDTOResponse.ObjectValue.PageSize,
                        CurrentPage = invoicesDTOResponse.ObjectValue.CurrentPage,
                        TotalPages = invoicesDTOResponse.ObjectValue.TotalPages,
                        HasNext = invoicesDTOResponse.ObjectValue.HasNext,
                        HasPrevious = invoicesDTOResponse.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(invoicesDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllByStatus)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        #endregion
    }
}
