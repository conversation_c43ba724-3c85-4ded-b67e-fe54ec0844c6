# 🧪 TESTS DU SERVICE DE TRANSACTION DISTRIBUÉE - PLATFORM DYNO

### Variables d'environnement
@baseUrl = http://localhost:7018/api
@paymentUrl = http://localhost:7018/api
@authUrl = http://localhost:7274/api

### 1. 🔐 AUTHENTIFICATION - Obtenir un token JWT
POST {{authUrl}}/AuthClient/login
Content-Type: application/json

{
  "phoneNumber": "+33123456789",
  "password": "TestPassword123!",
  "countryCode": "+33"
}

### 2. 📊 VÉRIFIER LES WALLETS DISPONIBLES
GET {{paymentUrl}}/Wallet/GetMyWallets
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. 💰 VÉRIFIER LE SOLDE TOTAL
GET {{paymentUrl}}/Wallet/GetTotalBalance
Authorization: Bearer {{token}}
Content-Type: application/json

### 4. 🔄 TEST TRANSACTION DISTRIBUÉE - CAS NORMAL
POST {{paymentUrl}}/DistributedTransaction/ExecuteSecure?pinCode=1234
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "550e8400-e29b-41d4-a716-446655440001",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 50.00,
  "description": "Test transaction distribuée",
  "transactionDate": "2024-01-15T10:30:00Z"
}

### 5. 🔄 TEST TRANSACTION DISTRIBUÉE - MONTANT ÉLEVÉ
POST {{paymentUrl}}/DistributedTransaction/ExecuteSecure?pinCode=1234
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "550e8400-e29b-41d4-a716-446655440001",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 1000.00,
  "description": "Test transaction élevée pour tester la compensation",
  "transactionDate": "2024-01-15T10:35:00Z"
}

### 6. ❌ TEST TRANSACTION DISTRIBUÉE - FONDS INSUFFISANTS
POST {{paymentUrl}}/DistributedTransaction/ExecuteSecure?pinCode=1234
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "550e8400-e29b-41d4-a716-446655440001",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 999999.00,
  "description": "Test fonds insuffisants",
  "transactionDate": "2024-01-15T10:40:00Z"
}

### 7. ❌ TEST TRANSACTION DISTRIBUÉE - WALLET INEXISTANT
POST {{paymentUrl}}/DistributedTransaction/ExecuteSecure?pinCode=1234
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "00000000-0000-0000-0000-000000000000",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 25.00,
  "description": "Test wallet inexistant",
  "transactionDate": "2024-01-15T10:45:00Z"
}

### 8. ❌ TEST TRANSACTION DISTRIBUÉE - PIN CODE INCORRECT
POST {{paymentUrl}}/DistributedTransaction/ExecuteSecure?pinCode=0000
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "550e8400-e29b-41d4-a716-446655440001",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 25.00,
  "description": "Test PIN incorrect",
  "transactionDate": "2024-01-15T10:50:00Z"
}

### 9. 📈 VÉRIFIER LE STATUT D'UNE TRANSACTION
GET {{paymentUrl}}/DistributedTransaction/Status/550e8400-e29b-41d4-a716-446655440003
Authorization: Bearer {{token}}
Content-Type: application/json

### 10. 🧹 NETTOYAGE DES RÉSERVATIONS EXPIRÉES (Admin seulement)
POST {{paymentUrl}}/DistributedTransaction/CleanupExpiredReservations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### 11. 📋 VÉRIFIER LES TRANSACTIONS APRÈS TESTS
GET {{paymentUrl}}/Transaction/GetAllPaged?pageSize=10&pageNumber=1
Authorization: Bearer {{token}}
Content-Type: application/json

### 12. 💳 VÉRIFIER LES SOLDES APRÈS TESTS
GET {{paymentUrl}}/Wallet/GetTotalBalance
Authorization: Bearer {{token}}
Content-Type: application/json

### 13. 🔄 TEST TRANSACTION STANDARD (Pour comparaison)
POST {{paymentUrl}}/Transaction/Create?pinCode=1234
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "senderWalletId": "550e8400-e29b-41d4-a716-446655440001",
  "receiverWalletId": "550e8400-e29b-41d4-a716-446655440002",
  "amount": 10.00,
  "description": "Transaction standard pour comparaison",
  "transactionDate": "2024-01-15T11:00:00Z"
}

### 📝 NOTES DE TEST

# 🎯 OBJECTIFS DES TESTS :
# 1. Vérifier que les transactions distribuées fonctionnent correctement
# 2. Tester la compensation automatique en cas d'échec blockchain
# 3. Valider la gestion des erreurs (fonds insuffisants, wallets inexistants)
# 4. Confirmer la cohérence des données entre Neo4j et Blockchain
# 5. Tester les timeouts et le nettoyage des réservations

# ✅ RÉSULTATS ATTENDUS :
# - Transaction normale : StatusCode 200, transaction réussie
# - Fonds insuffisants : StatusCode 400, message d'erreur approprié
# - Wallet inexistant : StatusCode 404, message d'erreur approprié
# - PIN incorrect : StatusCode 400, message d'erreur approprié
# - Compensation automatique en cas d'échec blockchain

# 🔍 POINTS À VÉRIFIER :
# 1. Cohérence des soldes avant/après transactions
# 2. Présence des transactions dans Neo4j
# 3. Logs de compensation en cas d'échec
# 4. Performance des transactions distribuées vs standard
# 5. Gestion des timeouts (réservations de 5 minutes)

# 🚨 TESTS DE STRESS (À faire séparément) :
# - Transactions simultanées sur le même wallet
# - Volume élevé de transactions
# - Simulation de pannes blockchain
# - Tests de charge sur les réservations
