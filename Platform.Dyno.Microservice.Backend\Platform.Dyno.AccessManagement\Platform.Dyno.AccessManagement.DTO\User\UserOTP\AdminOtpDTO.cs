﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.User.UserOTP
{
    public class AdminOtpDTO
    {
        #region Data
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Code is required")]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "The 'Code' must be a 4-digit number.")]
        public int Code { get; set; }

        public string MacAddress { get; set; } = string.Empty;

        public bool IsSaved { get; set; }
        #endregion

    }
}
