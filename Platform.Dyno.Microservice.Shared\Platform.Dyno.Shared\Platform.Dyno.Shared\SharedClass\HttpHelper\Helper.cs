﻿using Newtonsoft.Json;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Web;
using Microsoft.Extensions.Logging;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace Platform.Dyno.Shared.SharedClass.HttpHelper
{
    public class Helper<T> : IHelper<T>
    {
        private static readonly JsonSerializerOptions _jsonOptions = new(JsonSerializerDefaults.Web);
        private readonly ILogger<Helper<T>> _logger;
        
        public Helper(ILogger<Helper<T>> logger)
        {
            _logger = logger;
        }

        #region GET Methods
        public ResponseAPI<T>? Get(string endPoint, string? authorization = null)
        {
            using var request = new HttpRequestMessage(HttpMethod.Get, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            var dtoModel = System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
            return dtoModel;
        }

        public ResponseAPI<T>? Get(string endPoint, Guid id, string? authorization = null)
        {
            using var request = new HttpRequestMessage(HttpMethod.Get, $"{endPoint}/{id}");
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            var dtoModel = System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
            return dtoModel;
        }

        public ResponseAPI<List<T>>? GetAll(string endPoint, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Get, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            HttpResponseMessage response = httpClient.Send(request);
            if (response.IsSuccessStatusCode)
            {
                var stream = response.Content.ReadAsStream();
                var dtoModelList = System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<List<T>>>(stream, _jsonOptions);
                return dtoModelList;
            }
            return null;
        }

        public ResponseAPI<decimal> GetTotalBalanceByUserType(string endPoint, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Get, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            HttpResponseMessage response = httpClient.Send(request);
            if (response.IsSuccessStatusCode)
            {
                var stream = response.Content.ReadAsStream();
                var totalBalance = System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<decimal>>(stream, _jsonOptions);
                return totalBalance!;
            }
            return new ResponseAPI<decimal>
            {
                StatusCode = HttpStatusCode.InternalServerError,
                ExceptionMessage = "Error in GetTotalBalanceByUserType"
            };
        }

        public async Task<ResponseAPI<List<T>>> GetAllAsync(string endPoint, Dictionary<string, string>? queryParams = null, CancellationToken cancellationToken = default)
        {
            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var builder = new UriBuilder(endPoint);
                if (queryParams != null)
                {
                    var query = HttpUtility.ParseQueryString(builder.Query);
                    foreach (var param in queryParams)
                    {
                        query[param.Key] = param.Value;
                    }
                    builder.Query = query.ToString();
                }

                using var response = await client.GetAsync(builder.Uri, cancellationToken);
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                return new ResponseAPI<List<T>>
                {
                    StatusCode = response.StatusCode,
                    ObjectValue = System.Text.Json.JsonSerializer.Deserialize<List<T>>(content, _jsonOptions)
                };
            }
            catch (Exception ex)
            {
                return new ResponseAPI<List<T>>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.Message
                };
            }
        }
        #endregion

        #region POST Methods
        public ResponseAPI<T>? Post(string endPoint, T element, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Post, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            var json = System.Text.Json.JsonSerializer.Serialize(element, _jsonOptions);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            if (response.IsSuccessStatusCode || response.StatusCode != HttpStatusCode.InternalServerError)
            {
                return System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
            }
            return new ResponseAPI<T>
            {
                StatusCode = HttpStatusCode.InternalServerError,
                ExceptionMessage = "An error occured in Response of API"
            };
        }

        public ResponseAPI<List<T>>? AddRange(string endPoint, List<T> element, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Post, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            var json = System.Text.Json.JsonSerializer.Serialize(element, _jsonOptions);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            return System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<List<T>>>(stream, _jsonOptions);
        }

        public async Task<ResponseAPI<T>?> PostAsync(string endPoint, T element, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            try
            {
                using var client = new HttpClient { Timeout = TimeSpan.FromSeconds(30) };
                var uriBuilder = new UriBuilder(endPoint);
                if (queryParams != null)
                {
                    var query = HttpUtility.ParseQueryString(string.Empty);
                    foreach (var param in queryParams)
                    {
                        query[param.Key] = param.Value;
                    }
                    uriBuilder.Query = query.ToString();
                }
                var request = new HttpRequestMessage(HttpMethod.Post, uriBuilder.Uri);
                if (!string.IsNullOrEmpty(authorization))
                {
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authorization);
                }
                var json = JsonConvert.SerializeObject(element);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonConvert.DeserializeObject<ResponseAPI<T>>(content);
                    return result ?? new ResponseAPI<T>
                    {
                        StatusCode = response.StatusCode,
                        ObjectValue = default
                    };
                }
                else
                {
                    return new ResponseAPI<T>
                    {
                        StatusCode = response.StatusCode,
                        ExceptionMessage = $"Request failed: {response.ReasonPhrase}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in PostAsync request to {EndPoint}", endPoint);
                return new ResponseAPI<T>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.Message
                };
            }
        }
        #endregion

        #region PATCH Method
        public ResponseAPI<T>? Patch(string endPoint, T? element, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Patch, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            var json = System.Text.Json.JsonSerializer.Serialize(element, _jsonOptions);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            return System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
        }
        #endregion

        #region PUT Method
        public ResponseAPI<T>? Put(string endPoint, T element, Dictionary<string, string>? queryParams = null, string? authorization = null)
        {
            if (queryParams != null)
            {
                endPoint = endPoint + ToQueryString(queryParams);
            }
            using var request = new HttpRequestMessage(HttpMethod.Put, endPoint);
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            var json = System.Text.Json.JsonSerializer.Serialize(element, _jsonOptions);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            return System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
        }
        #endregion

        #region DELETE Method
        public ResponseAPI<T>? Delete(string endPoint, Guid id, string? authorization = null)
        {
            using var request = new HttpRequestMessage(HttpMethod.Delete, $"{endPoint}/{id}");
            using var httpClient = new HttpClient();
            if (AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue(headerValue.Scheme, headerValue.Parameter);
            }
            HttpResponseMessage response = httpClient.Send(request);
            var stream = response.Content.ReadAsStream();
            return System.Text.Json.JsonSerializer.Deserialize<ResponseAPI<T>>(stream, _jsonOptions);
        }
        #endregion

        static string ToQueryString(Dictionary<string, string> queryParams)
        {
            if (queryParams == null || queryParams.Count == 0)
            {
                return string.Empty;
            }
            var queryString = string.Join("&", queryParams.Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));
            return "?" + queryString;
        }
    }
}