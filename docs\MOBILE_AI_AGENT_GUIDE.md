# 🤖 GUIDE POUR AGENT IA - DÉVELOPPEMENT MOBILE FLUTTER

## 🎯 **MISSION : Développer l'application mobile Flutter pour Platform Dyno**

Cette documentation complète permet à un agent IA de développer l'application mobile Flutter pour les employés de Platform Dyno.

## 🏗️ **ARCHITECTURE SYSTÈME CORRECTE**

```
📱 FLUTTER APP (Employés)
    ↓ HTTPS/JSON (POINT D'ENTRÉE UNIQUE)
🌐 AccessManagement (Port 7274)
    ├── 🔐 AuthClient APIs - Authentification employés
    ├── 💰 Wallets & Transactions - Via redirection interne vers Payment
    ├── 🔔 Notifications - Via redirection interne vers Notification
    ├── 🏢 Company Data - Données entreprises
    └── 📊 Autres services - Redirection interne

🔄 SERVICES INTERNES (Non accessibles directement par mobile)
├── 💰 Payment Service (7018) - Transactions SAGA
├── 🔔 Notification Service (7038) - Temps réel
└── 📊 Autres microservices

🌐 INTERFACES WEB SÉPARÉES
├── 🖥️ Angular Admin/Entreprises - AuthAdmin APIs
└── 🖥️ Interface Commerçants - APIs dédiées
```

**IMPORTANT : L'application mobile utilise UNIQUEMENT AccessManagement comme point d'entrée. AccessManagement se charge de rediriger les requêtes vers les services internes appropriés.**

## 🔐 **AUTHENTIFICATION - ENDPOINTS CRITIQUES**

### **URLs de Base - POINT D'ENTRÉE UNIQUE**
```dart
// Production
static const String baseUrl = 'https://api.dyno-motiva.com/api';

// Développement
static const String baseUrl = 'http://localhost:7274/api';

// IMPORTANT: L'application mobile utilise UNIQUEMENT AccessManagement
// Toutes les requêtes passent par ce point d'entrée unique
// AccessManagement redirige automatiquement vers les services internes
```

### **Endpoints Mobile (via AccessManagement uniquement)**
```http
# AUTHENTIFICATION EMPLOYÉS
POST /AuthClient/login                    # Connexion employé
POST /AuthClient/RefreshToken             # Refresh automatique
GET  /AuthClient/ForgetPassword/{country}/{phone}
POST /AuthClient/VerifyOTPCode            # Vérification OTP
POST /AuthClient/ResetPassword            # Reset mot de passe
GET  /AuthClient/BiometricLogin/{password} # Connexion biométrique

# WALLETS & TRANSACTIONS (via AccessManagement → Payment)
GET  /Wallet/GetMyWallets                 # Mes wallets
GET  /Wallet/GetTotalBalance              # Solde total
POST /Transaction/Create                  # Transaction standard
GET  /Transaction/GetAllPaged             # Historique transactions

# DONNÉES ENTREPRISES
GET  /Company/GetRecents                  # Entreprises récentes
GET  /Company/GetAllPaged                 # Liste entreprises

# PROFIL UTILISATEUR
GET  /User/GetMyProfile                   # Mon profil
PUT  /User/UpdateProfile                  # Mettre à jour profil
```

### **Headers Requis**
```http
Content-Type: application/json
acceptLanguage: en
acceptTermsAndConditions: true
Authorization: Bearer {jwt_token}  # Pour les endpoints protégés
```

## 💰 **TRANSACTIONS ET WALLETS (via AccessManagement)**

### **🔄 Endpoints Wallets**
```http
GET  /Wallet/GetMyWallets                 # Mes wallets (redirigé vers Payment)
GET  /Wallet/GetAllPaged                  # Wallets paginés
GET  /Wallet/GetTotalBalance              # Solde total
GET  /Wallet/Get/{id}                     # Détails d'un wallet
```

### **🔄 Endpoints Transactions**
```http
POST /Transaction/Create?pinCode=1234     # Créer transaction (redirigé vers Payment)
GET  /Transaction/GetAllPaged             # Mes transactions paginées
GET  /Transaction/GetWalletTransactions   # Transactions d'un wallet
GET  /Transaction/GetTransactionsByUser   # Mes transactions
GET  /Transaction/Get/{id}                # Détails d'une transaction
```

### **Exemple Transaction Standard**
```http
POST /Transaction/Create?pinCode=1234
Content-Type: application/json
Authorization: Bearer {jwt_token}

{
  "senderWalletId": "guid",
  "receiverWalletId": "guid",
  "amount": 100.00,
  "description": "Payment description",
  "transactionDate": "2024-01-15T10:30:00Z"
}
```

**IMPORTANT :** AccessManagement redirige automatiquement vers Payment Service pour traiter les transactions et retourne le résultat à l'application mobile.

## 📱 **MODÈLES DART ESSENTIELS**

### **AuthResponse & UserProfile**
```dart
class AuthResponse {
  final String token;
  final String refreshToken;
  final DateTime expiredDate;
  final UserProfile userProfile;

  AuthResponse({required this.token, required this.refreshToken, 
               required this.expiredDate, required this.userProfile});

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      refreshToken: json['refreshToken'],
      expiredDate: DateTime.parse(json['expiredDate']),
      userProfile: UserProfile.fromJson(json['userProfile']),
    );
  }
}

class UserProfile {
  final String id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String userType;
  final String? companyId;

  UserProfile({required this.id, required this.fullName, required this.email,
               required this.phoneNumber, required this.userType, this.companyId});

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'], fullName: json['fullName'], email: json['email'],
      phoneNumber: json['phoneNumber'], userType: json['userType'],
      companyId: json['companyId'],
    );
  }
}
```

### **Wallet & Transaction**
```dart
class Wallet {
  final String id;
  final String walletType;
  final String assignedToName;
  final double balance;
  final String status;

  Wallet({required this.id, required this.walletType, required this.assignedToName,
          required this.balance, required this.status});

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      id: json['id'], walletType: json['walletType'],
      assignedToName: json['assignedToName'], balance: json['balance'].toDouble(),
      status: json['status'],
    );
  }

  bool get isActive => status == 'Active';
}

class Transaction {
  final String? id;
  final String senderWalletId;
  final String receiverWalletId;
  final double amount;
  final String description;
  final DateTime transactionDate;
  final String? status;

  Transaction({this.id, required this.senderWalletId, required this.receiverWalletId,
               required this.amount, required this.description, required this.transactionDate,
               this.status});

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'], senderWalletId: json['senderWalletId'],
      receiverWalletId: json['receiverWalletId'], amount: json['amount'].toDouble(),
      description: json['description'] ?? '', transactionDate: DateTime.parse(json['transactionDate']),
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'senderWalletId': senderWalletId, 'receiverWalletId': receiverWalletId,
      'amount': amount, 'description': description,
      'transactionDate': transactionDate.toIso8601String(),
    };
  }
}
```

### **ApiResponse Générique**
```dart
class ApiResponse<T> {
  final int statusCode;
  final T? objectValue;
  final String? exceptionMessage;

  ApiResponse({required this.statusCode, this.objectValue, this.exceptionMessage});

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      statusCode: json['statusCode'],
      objectValue: json['objectValue'] != null && fromJsonT != null
          ? fromJsonT(json['objectValue']) : json['objectValue'],
      exceptionMessage: json['exceptionMessage'],
    );
  }

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
  bool get isUnauthorized => statusCode == 401;
}
```

## 🔧 **SERVICE API COMPLET**

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost:7274/api';
  // POINT D'ENTRÉE UNIQUE - Toutes les requêtes passent par AccessManagement
  
  String? _token;
  void setToken(String token) => _token = token;

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'acceptLanguage': 'en',
    if (_token != null) 'Authorization': 'Bearer $_token',
  };

  // AUTHENTIFICATION
  Future<ApiResponse<AuthResponse>> login(String phoneNumber, String password, String countryCode) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/AuthClient/login'),
        headers: {'Content-Type': 'application/json', 'acceptLanguage': 'en', 'acceptTermsAndConditions': 'true'},
        body: jsonEncode({'phoneNumber': phoneNumber, 'password': password, 'countryCode': countryCode}),
      );
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => AuthResponse.fromJson(data));
    } catch (e) {
      return ApiResponse<AuthResponse>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }

  Future<ApiResponse<AuthResponse>> refreshToken(String token, String refreshToken, DateTime expiredDate) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/AuthClient/RefreshToken'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'token': token, 'refreshToken': refreshToken, 'expiredDate': expiredDate.toIso8601String()}),
      );
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => AuthResponse.fromJson(data));
    } catch (e) {
      return ApiResponse<AuthResponse>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }

  // WALLETS
  Future<ApiResponse<List<Wallet>>> getMyWallets() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/Wallet/GetMyWallets'), headers: _headers);
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => (data as List).map((item) => Wallet.fromJson(item)).toList());
    } catch (e) {
      return ApiResponse<List<Wallet>>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }

  Future<ApiResponse<double>> getTotalBalance() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/Wallet/GetTotalBalance'), headers: _headers);
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => data.toDouble());
    } catch (e) {
      return ApiResponse<double>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }

  // TRANSACTIONS (via AccessManagement)
  Future<ApiResponse<Transaction>> createTransaction(Transaction transaction, String pinCode) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/Transaction/Create?pinCode=$pinCode'),
        headers: _headers,
        body: jsonEncode(transaction.toJson()),
      );
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => Transaction.fromJson(data));
    } catch (e) {
      return ApiResponse<Transaction>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }

  // TRANSACTIONS
  Future<ApiResponse<List<Transaction>>> getMyTransactions({int pageSize = 20, int pageNumber = 1}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/Transaction/GetAllPaged?pageSize=$pageSize&pageNumber=$pageNumber&order=DESC'),
        headers: _headers,
      );
      final jsonResponse = jsonDecode(response.body);
      return ApiResponse.fromJson(jsonResponse, (data) => (data as List).map((item) => Transaction.fromJson(item)).toList());
    } catch (e) {
      return ApiResponse<List<Transaction>>(statusCode: 500, exceptionMessage: 'Network error: $e');
    }
  }
}
```

## 📱 **EXEMPLES D'ÉCRANS FLUTTER**

### **1. Écran de Connexion**
```dart
class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authManager = AuthManager();
  bool _isLoading = false;

  Future<void> _login() async {
    setState(() => _isLoading = true);
    final success = await _authManager.login(_phoneController.text, _passwordController.text, '+33');
    setState(() => _isLoading = false);

    if (success) {
      Navigator.pushReplacementNamed(context, '/dashboard');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Erreur de connexion')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Platform Dyno')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextField(controller: _phoneController, decoration: InputDecoration(labelText: 'Téléphone'), keyboardType: TextInputType.phone),
            TextField(controller: _passwordController, decoration: InputDecoration(labelText: 'Mot de passe'), obscureText: true),
            SizedBox(height: 20),
            _isLoading ? CircularProgressIndicator() : ElevatedButton(onPressed: _login, child: Text('Se connecter')),
          ],
        ),
      ),
    );
  }
}
```

### **2. Dashboard avec Wallets**
```dart
class DashboardScreen extends StatefulWidget {
  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final _apiService = ApiService();
  List<Wallet> _wallets = [];
  double _totalBalance = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    final walletsResponse = await _apiService.getMyWallets();
    if (walletsResponse.isSuccess) _wallets = walletsResponse.objectValue!;

    final balanceResponse = await _apiService.getTotalBalance();
    if (balanceResponse.isSuccess) _totalBalance = balanceResponse.objectValue!;

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Dashboard'), actions: [IconButton(icon: Icon(Icons.logout), onPressed: () async {
        await AuthManager().logout();
        Navigator.pushReplacementNamed(context, '/login');
      })]),
      body: _isLoading ? Center(child: CircularProgressIndicator()) : Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            color: Colors.blue[50],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Solde Total:', style: TextStyle(fontSize: 18)),
                Text('${_totalBalance.toStringAsFixed(2)}€', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _wallets.length,
              itemBuilder: (context, index) {
                final wallet = _wallets[index];
                return ListTile(
                  title: Text(wallet.assignedToName),
                  subtitle: Text('Type: ${wallet.walletType}'),
                  trailing: Text('${wallet.balance.toStringAsFixed(2)}€', style: TextStyle(fontWeight: FontWeight.bold)),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, '/transaction'),
        child: Icon(Icons.send),
        tooltip: 'Nouvelle transaction',
      ),
    );
  }
}
```

### **3. Écran de Transaction Sécurisée**
```dart
class TransactionScreen extends StatefulWidget {
  @override
  _TransactionScreenState createState() => _TransactionScreenState();
}

class _TransactionScreenState extends State<TransactionScreen> {
  final _apiService = ApiService();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _pinController = TextEditingController();

  List<Wallet> _wallets = [];
  Wallet? _selectedSender;
  Wallet? _selectedReceiver;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadWallets();
  }

  Future<void> _loadWallets() async {
    final response = await _apiService.getMyWallets();
    if (response.isSuccess) setState(() => _wallets = response.objectValue!.where((w) => w.isActive).toList());
  }

  Future<void> _executeTransaction() async {
    if (_selectedSender == null || _selectedReceiver == null) {
      _showError('Sélectionnez les wallets');
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      _showError('Montant invalide');
      return;
    }

    setState(() => _isLoading = true);

    final transaction = Transaction(
      senderWalletId: _selectedSender!.id,
      receiverWalletId: _selectedReceiver!.id,
      amount: amount,
      description: _descriptionController.text,
      transactionDate: DateTime.now(),
    );

    final response = await _apiService.createTransaction(transaction, _pinController.text);
    setState(() => _isLoading = false);

    if (response.isSuccess) {
      _showSuccess('Transaction réussie!');
      Navigator.pop(context);
    } else {
      _showError(response.exceptionMessage ?? 'Erreur de transaction');
    }
  }

  void _showError(String message) => ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: Colors.red));
  void _showSuccess(String message) => ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: Colors.green));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Transaction Sécurisée')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<Wallet>(
              value: _selectedSender,
              decoration: InputDecoration(labelText: 'Wallet expéditeur'),
              items: _wallets.map((w) => DropdownMenuItem(value: w, child: Text('${w.assignedToName} (${w.balance.toStringAsFixed(2)}€)'))).toList(),
              onChanged: (wallet) => setState(() => _selectedSender = wallet),
            ),
            DropdownButtonFormField<Wallet>(
              value: _selectedReceiver,
              decoration: InputDecoration(labelText: 'Wallet destinataire'),
              items: _wallets.where((w) => w.id != _selectedSender?.id).map((w) => DropdownMenuItem(value: w, child: Text('${w.assignedToName}'))).toList(),
              onChanged: (wallet) => setState(() => _selectedReceiver = wallet),
            ),
            TextField(controller: _amountController, decoration: InputDecoration(labelText: 'Montant (€)'), keyboardType: TextInputType.numberWithOptions(decimal: true)),
            TextField(controller: _descriptionController, decoration: InputDecoration(labelText: 'Description')),
            TextField(controller: _pinController, decoration: InputDecoration(labelText: 'Code PIN'), keyboardType: TextInputType.number, obscureText: true, maxLength: 4),
            SizedBox(height: 20),
            _isLoading ? CircularProgressIndicator() : ElevatedButton(
              onPressed: _executeTransaction,
              child: Text('Créer Transaction'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green, minimumSize: Size(double.infinity, 50)),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 📋 **DÉPENDANCES FLUTTER**

```yaml
# pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  shared_preferences: ^2.2.2
  signalr_netcore: ^1.3.7
  provider: ^6.1.1
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
```

## 🎯 **INSTRUCTIONS POUR L'AGENT IA**

### **Phase 1 : Configuration (Priorité 1)**
1. **Créer le projet Flutter** avec les dépendances listées
2. **Implémenter les modèles Dart** (AuthResponse, UserProfile, Wallet, Transaction, ApiResponse)
3. **Créer ApiService** avec tous les endpoints
4. **Implémenter AuthManager** avec stockage sécurisé

### **Phase 2 : Écrans Core (Priorité 2)**
1. **LoginScreen** - Authentification avec validation
2. **DashboardScreen** - Affichage wallets et solde total
3. **TransactionScreen** - Transactions sécurisées avec SAGA
4. **Navigation** - Routes et gestion des états

### **Phase 3 : Fonctionnalités Avancées (Priorité 3)**
1. **Historique des transactions** - Liste paginée
2. **Notifications SignalR** - Temps réel
3. **Gestion hors ligne** - Cache et synchronisation
4. **Biométrie** - Connexion sécurisée

### **Points Critiques :**
- ✅ **TOUJOURS utiliser `/DistributedTransaction/ExecuteSecure`** pour les transactions
- ✅ **Refresh automatique** des tokens avant expiration
- ✅ **Gestion d'erreurs** complète avec messages utilisateur
- ✅ **Validation des formulaires** avant envoi
- ✅ **États de chargement** visuels

### **Sécurité :**
- ✅ Stockage sécurisé avec SharedPreferences
- ✅ Validation SSL en production
- ✅ Timeout des requêtes (30 secondes)
- ✅ Gestion des erreurs 401 (déconnexion automatique)

### **Performance :**
- ✅ Pagination (20 éléments par page)
- ✅ Cache des données statiques
- ✅ Optimisation des rebuilds avec setState
- ✅ Lazy loading des listes

### **Tests de Validation :**
1. **Connexion/Déconnexion** - Avec refresh automatique
2. **Affichage des wallets** - Avec soldes corrects
3. **Transaction sécurisée** - Avec validation PIN
4. **Gestion d'erreurs** - Fonds insuffisants, wallets inexistants
5. **Navigation** - Entre tous les écrans

## 🚀 **RÉSULTAT ATTENDU**

Une application mobile Flutter complète avec :
- ✅ **Authentification robuste** avec JWT et refresh automatique
- ✅ **Gestion des wallets** avec affichage des soldes
- ✅ **Transactions sécurisées** utilisant le système SAGA
- ✅ **Interface utilisateur** intuitive et responsive
- ✅ **Gestion d'erreurs** complète et messages clairs
- ✅ **Performance optimisée** avec pagination et cache

**Cette documentation fournit tout ce qui est nécessaire pour qu'un agent IA développe une application mobile Flutter fonctionnelle et sécurisée pour Platform Dyno !**

## 🔐 **GESTIONNAIRE D'AUTHENTIFICATION**

```dart
import 'package:shared_preferences/shared_preferences.dart';

class AuthManager {
  static const String _tokenKey = 'dyno_token';
  static const String _refreshTokenKey = 'dyno_refresh_token';
  static const String _expiredDateKey = 'dyno_expired_date';
  
  final ApiService _apiService = ApiService();

  Future<bool> login(String phoneNumber, String password, String countryCode) async {
    final response = await _apiService.login(phoneNumber, password, countryCode);
    if (response.isSuccess && response.objectValue != null) {
      await _saveAuthData(response.objectValue!);
      _apiService.setToken(response.objectValue!.token);
      return true;
    }
    return false;
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    final expiredDateStr = prefs.getString(_expiredDateKey);
    
    if (token == null || expiredDateStr == null) return false;
    
    final expiredDate = DateTime.parse(expiredDateStr);
    if (DateTime.now().isAfter(expiredDate.subtract(Duration(minutes: 5)))) {
      return await _refreshToken();
    }
    
    _apiService.setToken(token);
    return true;
  }

  Future<bool> _refreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    final refreshToken = prefs.getString(_refreshTokenKey);
    final expiredDateStr = prefs.getString(_expiredDateKey);
    
    if (token == null || refreshToken == null || expiredDateStr == null) return false;
    
    final expiredDate = DateTime.parse(expiredDateStr);
    final response = await _apiService.refreshToken(token, refreshToken, expiredDate);
    
    if (response.isSuccess && response.objectValue != null) {
      await _saveAuthData(response.objectValue!);
      _apiService.setToken(response.objectValue!.token);
      return true;
    }
    
    await logout();
    return false;
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_expiredDateKey);
  }

  Future<void> _saveAuthData(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, authResponse.token);
    await prefs.setString(_refreshTokenKey, authResponse.refreshToken);
    await prefs.setString(_expiredDateKey, authResponse.expiredDate.toIso8601String());
  }
}
```
