﻿using Amazon.Runtime.Internal.Transform;
using AutoMapper;
using Castle.Components.DictionaryAdapter.Xml;
using MimeKit;
using MimeKit.Encodings;
using Org.BouncyCastle.Tls;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Group;
using Platform.Dyno.AccessManagement.Business.IService.Ticket;
using Platform.Dyno.AccessManagement.Business.IService.Transaction;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.AccessManagement.BusinessModel.Ticket;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Net;
using System.Security.Cryptography.Xml;

namespace Platform.Dyno.AccessManagement.Business.Service.Transaction
{
    public class TransactionService: ITransactionService
    {
        private readonly ITicketService _ticketService;
        private readonly IUserService _userService;
        private readonly IGroupService _groupService;
        private readonly IWalletService _walletService;
        private readonly ICompanyService _companyService;
        private readonly Configuration _configuration;
        private readonly IHelper<TransactionDTO> _helper;
        private readonly IHelper<TransactionGroupDTO> _transactionGroupHelper;
        private readonly IHelper<BalanceNotificationDTO> _notificationHelper;
        private readonly IHelper<bool> _helperBool;
        private readonly IHelper<WalletDTO> _walletHelper;
        private readonly IMapper _mapper;
        public TransactionService(
            ITicketService ticketService,
            IUserService userService,
            IGroupService groupService,
            IWalletService walletService,
            ICompanyService companyService,
            Configuration configuration,
            IHelper<TransactionDTO> helper,
            IHelper<WalletDTO> walletHelper,
            IHelper<BalanceNotificationDTO> notificationHelper,
            IHelper<TransactionGroupDTO> transactionGroupHelper,
            IHelper<bool> helperBool,
            IMapper mapper
            )
        {
            _ticketService = ticketService;
            _userService = userService;
            _groupService = groupService;
            _walletService = walletService;
            _companyService = companyService;
            _configuration = configuration;
            _helper = helper;
            _walletHelper = walletHelper;
            _helperBool = helperBool;
            _mapper = mapper;
            _transactionGroupHelper= transactionGroupHelper;
            _notificationHelper = notificationHelper;
        }

        #region Get
        public ResponseAPI<List<CashierTransactionsDTO>> GetCashiersTransactions(Guid shopOwnerId, PagedParameters pagedParameters)
        {
            UserDTO? shopOwner = _userService.Get(shopOwnerId).ObjectValue;
            if (shopOwner == null)
            {
                return new ResponseAPI<List<CashierTransactionsDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized"
                };
            }
            List<UserDTO> cashiers = _userService.Get(user => (user.UserType == Shared.Enum.UserType.Cashier || user.UserType == UserType.ShopOwner) &&
                                                        user.Status == Shared.Enum.Status.Active &&
                                                        user.CompanyId == shopOwner.CompanyId).ObjectValue ?? new List<UserDTO>();

            List<CashierTransactionsDTO> cashierTransactionsDTOs = new List<CashierTransactionsDTO>();
            foreach (var cashier in cashiers)
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserTransactions?PageNumber={pagedParameters.PageNumber}&PageSize={pagedParameters.PageSize}&userId={(cashier.UserType == UserType.Cashier ? cashier.Id.ToString() ?? string.Empty : cashier.CompanyId.ToString())}";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "userId", cashier.UserType == UserType.Cashier ? cashier.Id.ToString() ?? string.Empty : cashier.CompanyId.ToString() }
                    };

                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url);
                List<HistoriqueTransactionDetailsDTO> historiqueTransactions = new List<HistoriqueTransactionDetailsDTO>();
                if (response != null && response.StatusCode == HttpStatusCode.OK)
                {
                    if (response.ObjectValue != null && response.ObjectValue.Count > 0)
                    {
                        foreach (var transactionDTO in response.ObjectValue)
                        {
                            ResponseAPI<WalletDTO>? wallet = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{(transactionDTO.IsCredit ? transactionDTO.SenderWalletId : transactionDTO.ReceiverWalletId)}");
                            UserDTO user = new UserDTO();
                            if (wallet != null && wallet.StatusCode == HttpStatusCode.OK && wallet.ObjectValue != null)
                            {
                                user = _userService.Get(wallet.ObjectValue.AssignedToId).ObjectValue ?? new UserDTO();
                            }

                            HistoriqueTransactionDetailsDTO historiqueTransaction = new HistoriqueTransactionDetailsDTO
                            {
                                Id = transactionDTO.Id,
                                ReceiverWalletId = transactionDTO.IsCredit ? transactionDTO.ReceiverWalletId : transactionDTO.SenderWalletId,
                                ReceiverName = transactionDTO.IsCredit ? cashier.FullName : user.FullName,
                                ReceiverId = (transactionDTO.IsCredit ? cashier.Id : user.Id) ?? Guid.Empty,
                                SenderWalletId = !transactionDTO.IsCredit ? transactionDTO.ReceiverWalletId : transactionDTO.SenderWalletId,
                                SenderName = transactionDTO.IsCredit ? user.FullName : cashier.FullName,
                                SenderId = (transactionDTO.IsCredit ? user.Id : cashier.Id) ?? Guid.Empty,
                                IsCredit = transactionDTO.IsCredit,
                                Amount = transactionDTO.Amount,
                                TransactionDate = transactionDTO.TransactionDate,
                                RefundStatus = transactionDTO.RefundStatus
                            };

                            historiqueTransactions.Add(historiqueTransaction);

                        }
                    }


                    CashierTransactionsDTO cashierTransactions = new CashierTransactionsDTO
                    {
                        UserId = cashier.Id,
                        Email = cashier.Email,
                        FullName = cashier.FullName,
                        Gender = cashier.Gender,
                        PhoneNumber = cashier.PhoneNumber,
                        UserName = cashier.UserName,
                        Transactions = historiqueTransactions
                    };
                    cashierTransactionsDTOs.Add(cashierTransactions);
                }
            }

            return new ResponseAPI<List<CashierTransactionsDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = cashierTransactionsDTOs
            };
        }

        public ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueReceiverTransactions(Guid userId, int pageSize, int pageNumber)
        {
            string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserReceivedTransactions";
            Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "PageNumber", pageNumber.ToString() },
                        { "PageSize", pageSize.ToString() },
                        { "userId", userId.ToString() }
                    };
            ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);

            if(response != null && response.StatusCode == HttpStatusCode.OK)
            {
                List<HistoriqueTransactionDetailsDTO> historiqueTransactions = new List<HistoriqueTransactionDetailsDTO>();
                if (response.ObjectValue!= null && response.ObjectValue.Count > 0)
                {
                    ResponseAPI<WalletDTO>? walletReceiver = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{response.ObjectValue.FirstOrDefault()?.ReceiverWalletId}");
                    UserProfileDTO? receiver = _userService.GetUserProfile(userId).ObjectValue;
                    if(receiver != null && walletReceiver != null)
                    {
                        foreach (var transactionDTO in response.ObjectValue)
                        {
                            ResponseAPI<WalletDTO>? wallet = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transactionDTO.SenderWalletId}");
                            if (wallet != null && wallet.ObjectValue != null)
                            {
                                UserProfileDTO? user = _userService.GetUserProfile(wallet.ObjectValue.AssignedToId).ObjectValue;
                                if(user == null)
                                {
                                    UserDTO? userDto = _userService.Get(user => user.CompanyId == wallet.ObjectValue.AssignedToId).ObjectValue?.FirstOrDefault();
                                    user = _mapper.Map<UserProfileDTO>(userDto);
                                }                                
                                if (user != null)
                                {
                                    historiqueTransactions.Add(new HistoriqueTransactionDetailsDTO
                                    {
                                        Id = transactionDTO.Id,
                                        ReceiverId = receiver?.Id ?? Guid.Empty,
                                        ReceiverName = receiver?.FullName,
                                        ReceiverWalletId =transactionDTO.ReceiverWalletId,
                                        SenderId = user?.Id ?? Guid.Empty,
                                        SenderName = user?.FullName,
                                        SenderWalletId= transactionDTO.SenderWalletId,
                                        Amount= transactionDTO.Amount,
                                        IsCredit= true,
                                        TransactionDate= transactionDTO.TransactionDate,
                                        RefundStatus = transactionDTO.RefundStatus
                                    });
                                }
                            }                    
                        }

                        
                    }
                }

                return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = historiqueTransactions
                };

            }

            return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
            {
                StatusCode = response?.StatusCode ?? HttpStatusCode.InternalServerError,
                ExceptionMessage = response?.ExceptionMessage ?? "Internall Server Error"
            };

        }

        public ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueSenderTransactions(Guid userId, int pageSize, int pageNumber)
        {
            string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserSentTransactions";
            Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "PageNumber", pageNumber.ToString() },
                        { "PageSize", pageSize.ToString() },
                        { "userId", userId.ToString() }
                    };
            ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);

            if (response != null && response.StatusCode == HttpStatusCode.OK)
            {
                List<HistoriqueTransactionDetailsDTO> historiqueTransactions = new List<HistoriqueTransactionDetailsDTO>();
                if (response.ObjectValue != null && response.ObjectValue.Count > 0)
                {
                    ResponseAPI<WalletDTO>? walletsent = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{response.ObjectValue.FirstOrDefault()?.SenderWalletId}");
                    UserProfileDTO? sender = _userService.GetUserProfile(userId).ObjectValue;
                    if (sender != null && walletsent != null)
                    {
                        foreach (var transactionDTO in response.ObjectValue)
                        {
                            ResponseAPI<WalletDTO>? wallet = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transactionDTO.ReceiverWalletId}");
                            if (wallet != null && wallet.ObjectValue != null)
                            {
                                UserProfileDTO? user = _userService.GetUserProfile(wallet.ObjectValue.AssignedToId).ObjectValue;
                                if (user == null)
                                {
                                    UserDTO? userDto = _userService.Get(user => user.CompanyId == wallet.ObjectValue.AssignedToId).ObjectValue?.FirstOrDefault();
                                    user = _mapper.Map<UserProfileDTO>(userDto);
                                }
                                if (user != null)
                                {
                                    historiqueTransactions.Add(new HistoriqueTransactionDetailsDTO
                                    {
                                        Id = transactionDTO.Id,
                                        ReceiverId = user.Id ?? Guid.Empty,
                                        ReceiverName = user?.FullName,
                                        ReceiverWalletId = transactionDTO.ReceiverWalletId,
                                        SenderId = user?.Id ?? Guid.Empty,
                                        SenderName = user?.FullName,
                                        SenderWalletId = transactionDTO.SenderWalletId,
                                        Amount = transactionDTO.Amount,
                                        IsCredit = false,
                                        TransactionDate = transactionDTO.TransactionDate,
                                        RefundStatus = transactionDTO.RefundStatus
                                    });
                                }
                            }
                        }


                    }
                }

                return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = historiqueTransactions
                };

            }

            return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
            {
                StatusCode = response?.StatusCode ?? HttpStatusCode.InternalServerError,
                ExceptionMessage = response?.ExceptionMessage ?? "Internall Server Error"
            };

        }

        public ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueTransactions(Guid userId, int pageSize, int pageNumber)
        {
            string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetUserTransactions";
            Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "PageNumber", pageNumber.ToString() },
                        { "PageSize", pageSize.ToString() },
                        { "userId", userId.ToString() }
                    };
            ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);

            if (response != null && response.StatusCode == HttpStatusCode.OK)
            {
                List<HistoriqueTransactionDetailsDTO> historiqueTransactions = new List<HistoriqueTransactionDetailsDTO>();
                if (response.ObjectValue != null && response.ObjectValue.Count > 0)
                {
                    foreach(var transaction in response.ObjectValue)
                    {
                        var transactionDetails = new HistoriqueTransactionDetailsDTO
                        {
                            Id = transaction.Id,
                            ReceiverWalletId = transaction.ReceiverWalletId,
                            SenderWalletId = transaction.SenderWalletId,
                            Amount = transaction.Amount,
                            IsCredit = transaction.IsCredit,
                            TransactionDate = transaction.TransactionDate,
                            RefundStatus = transaction.RefundStatus
                        };
                        var historicalTransaction = historiqueTransactions.FirstOrDefault(t => t.SenderWalletId == transaction.SenderWalletId || t.ReceiverWalletId == transaction.SenderWalletId);
                        if (historicalTransaction != null)
                        {
                            transactionDetails.SenderId = historicalTransaction.SenderWalletId == transaction.SenderWalletId ? historicalTransaction.SenderId : historicalTransaction.ReceiverId;
                            transactionDetails.SenderName = historicalTransaction.SenderWalletId == transaction.SenderWalletId ? historicalTransaction.SenderName : historicalTransaction.ReceiverName;
                        }
                        else
                        {
                            WalletDTO? walletSender = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transaction.SenderWalletId}")?.ObjectValue;
                            if(walletSender != null)
                            {
                                UserProfileDTO? sender = new UserProfileDTO();
                                CompanyDTO? comapnySender = new CompanyDTO();
                                if (walletSender.AssignedToType == UserType.Client)
                                {
                                    sender = _userService.GetUserProfile(walletSender.AssignedToId).ObjectValue;
                                }
                                else
                                {
                                    comapnySender = _companyService.Get(walletSender.AssignedToId).ObjectValue;
                                }

                                transactionDetails.SenderId = sender?.Id ?? comapnySender?.Id ?? Guid.Empty;
                                transactionDetails.SenderName = sender?.FullName ?? comapnySender?.Name ?? "Unknow";
                            }
                        }

                        historicalTransaction = historiqueTransactions.FirstOrDefault(t => t.SenderWalletId == transaction.ReceiverWalletId || t.ReceiverWalletId == transaction.ReceiverWalletId);
                        if (historicalTransaction != null)
                        {
                            transactionDetails.ReceiverId = historicalTransaction.SenderWalletId == transaction.ReceiverWalletId ? historicalTransaction.SenderId : historicalTransaction.ReceiverId;
                            transactionDetails.ReceiverName = historicalTransaction.SenderWalletId == transaction.ReceiverWalletId ? historicalTransaction.SenderName : historicalTransaction.ReceiverName;
                        }
                        else
                        {
                            WalletDTO? walletReceiver = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transaction.ReceiverWalletId}")?.ObjectValue;
                            if (walletReceiver != null)
                            {
                                UserProfileDTO? receiver = new UserProfileDTO();
                                CompanyDTO? comapnyReceiver = new CompanyDTO();
                                if (walletReceiver.AssignedToType == UserType.Client)
                                {
                                    receiver = _userService.GetUserProfile(walletReceiver.AssignedToId).ObjectValue;
                                }
                                else
                                {
                                    comapnyReceiver = _companyService.Get(walletReceiver.AssignedToId).ObjectValue;
                                }

                                transactionDetails.ReceiverId = receiver?.Id ?? comapnyReceiver?.Id ?? Guid.Empty;
                                transactionDetails.ReceiverName = receiver?.FullName ?? comapnyReceiver?.Name ?? "Unknow";
                            }
                        }

                        historiqueTransactions.Add(transactionDetails);

                    }
                }
                return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>> {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = historiqueTransactions
                };
            }

            return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
            {
                StatusCode = response?.StatusCode ?? HttpStatusCode.InternalServerError,
                ExceptionMessage = response?.ExceptionMessage ?? "Internall Server Error"
            };

        }

        public ResponseAPI<List<HistoriqueTransactionDetailsDTO>> GetHistoriqueTransactionsByWalletType(Guid userId, WalletType walletType, int pageSize, int pageNumber)
        {
            ResponseAPI<List<WalletDTO>>? wallets = _walletHelper.GetAll($"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets", new Dictionary<string, string>
                    {
                        { "userId", userId.ToString() }
                    });
            if(wallets != null && wallets.StatusCode == HttpStatusCode.OK)
            {
                WalletDTO? wallet = wallets.ObjectValue?.FirstOrDefault(w => w.WalletType == walletType);
                if(wallet == null)
                {
                    return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = " Wallet Not found !"
                    };
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Transaction/GetWalletTransactions";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "PageNumber", pageNumber.ToString() },
                        { "PageSize", pageSize.ToString() },
                        { "walletId", wallet.Id.ToString() }
                    };
                ResponseAPI<List<TransactionDTO>>? response = _helper.GetAll(Url, queryParams);

                if (response != null && response.StatusCode == HttpStatusCode.OK)
                {
                    List<HistoriqueTransactionDetailsDTO> historiqueTransactions = new List<HistoriqueTransactionDetailsDTO>();
                    if (response.ObjectValue != null && response.ObjectValue.Count > 0)
                    {
                        foreach (var transaction in response.ObjectValue)
                        {
                            var transactionDetails = new HistoriqueTransactionDetailsDTO
                            {
                                Id = transaction.Id,
                                ReceiverWalletId = transaction.ReceiverWalletId,
                                SenderWalletId = transaction.SenderWalletId,
                                Amount = transaction.Amount,
                                IsCredit = transaction.IsCredit,
                                TransactionDate = transaction.TransactionDate,
                                RefundStatus = transaction.RefundStatus
                            };
                            var historicalTransaction = historiqueTransactions.FirstOrDefault(t => t.SenderWalletId == transaction.SenderWalletId || t.ReceiverWalletId == transaction.SenderWalletId);
                            if (historicalTransaction != null)
                            {
                                transactionDetails.SenderId = historicalTransaction.SenderWalletId == transaction.SenderWalletId ? historicalTransaction.SenderId : historicalTransaction.ReceiverId;
                                transactionDetails.SenderName = historicalTransaction.SenderWalletId == transaction.SenderWalletId ? historicalTransaction.SenderName : historicalTransaction.ReceiverName;
                            }
                            else
                            {
                                WalletDTO? walletSender = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transaction.SenderWalletId}")?.ObjectValue;
                                if (walletSender != null)
                                {
                                    UserProfileDTO? sender = new UserProfileDTO();
                                    CompanyDTO? comapnySender = new CompanyDTO();
                                    if (walletSender.AssignedToType == UserType.Client)
                                    {
                                        sender = _userService.GetUserProfile(walletSender.AssignedToId).ObjectValue;
                                    }
                                    else
                                    {
                                        comapnySender = _companyService.Get(walletSender.AssignedToId).ObjectValue;
                                    }

                                    transactionDetails.SenderId = sender?.Id ?? comapnySender?.Id ?? Guid.Empty;
                                    transactionDetails.SenderName = sender?.FullName ?? comapnySender?.Name ?? "Unknow";
                                }
                            }

                            historicalTransaction = historiqueTransactions.FirstOrDefault(t => t.SenderWalletId == transaction.ReceiverWalletId || t.ReceiverWalletId == transaction.ReceiverWalletId);
                            if (historicalTransaction != null)
                            {
                                transactionDetails.ReceiverId = historicalTransaction.SenderWalletId == transaction.ReceiverWalletId ? historicalTransaction.SenderId : historicalTransaction.ReceiverId;
                                transactionDetails.ReceiverName = historicalTransaction.SenderWalletId == transaction.ReceiverWalletId ? historicalTransaction.SenderName : historicalTransaction.ReceiverName;
                            }
                            else
                            {
                                WalletDTO? walletReceiver = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{transaction.ReceiverWalletId}")?.ObjectValue;
                                if (walletReceiver != null)
                                {
                                    UserProfileDTO? receiver = new UserProfileDTO();
                                    CompanyDTO? comapnyReceiver = new CompanyDTO();
                                    if (walletReceiver.AssignedToType == UserType.Client)
                                    {
                                        receiver = _userService.GetUserProfile(walletReceiver.AssignedToId).ObjectValue;
                                    }
                                    else
                                    {
                                        comapnyReceiver = _companyService.Get(walletReceiver.AssignedToId).ObjectValue;
                                    }

                                    transactionDetails.ReceiverId = receiver?.Id ?? comapnyReceiver?.Id ?? Guid.Empty;
                                    transactionDetails.ReceiverName = receiver?.FullName ?? comapnyReceiver?.Name ?? "Unknow";
                                }
                            }

                            historiqueTransactions.Add(transactionDetails);

                        }
                    }
                    return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                    {
                        StatusCode = HttpStatusCode.OK,
                        ObjectValue = historiqueTransactions
                    };
                }

                return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
                {
                    StatusCode = response?.StatusCode ?? HttpStatusCode.InternalServerError,
                    ExceptionMessage = response?.ExceptionMessage ?? "Internall Server Error"
                };
            };


            return new ResponseAPI<List<HistoriqueTransactionDetailsDTO>>
            {
                StatusCode = HttpStatusCode.Unauthorized,
                ExceptionMessage = " Wallet Not found !"
            };

        }
        #endregion

        #region Create
        public ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(UniqueQRCodeTransactionDTO transaction, string pinCode, Guid userId)
        {
            #region Cashier Check
            List<UserDTO>? cashier = _userService.Get(u =>
                                                        u.Id == userId &&
                                                        u.UserType == Shared.Enum.UserType.Cashier &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;
            if (cashier == null || cashier.Count <= 0)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Casier not Found !"
                };
            }
            #endregion

            #region Client Check
            ResponseAPI<WalletDTO>? clientWallet = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get", transaction.SenderWalletId);
            if (clientWallet == null || clientWallet.StatusCode == HttpStatusCode.InternalServerError)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred in payment Micro-Service"
                };

            }
            else if (clientWallet?.StatusCode == HttpStatusCode.BadRequest)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = clientWallet.ExceptionMessage
                };
            }

            List<UserDTO>? client = _userService.Get(u =>
                                                        u.Id == clientWallet?.ObjectValue?.AssignedToId &&
                                                        u.UserType == Shared.Enum.UserType.Client &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;
            if (client == null || client.Count <= 0)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User Unauthorized"
                };
            }

            #endregion

            #region Shop Owner Check
            //Get Company Shop Owner Id 
            List<CompanyDTO>? shopOwner = _companyService.Get(u =>
                                                        u.Id == cashier.FirstOrDefault()?.CompanyId &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;

            if (shopOwner == null || shopOwner.Count <= 0)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User Unauthorized"
                };
            }

            ResponseAPI<List<WalletDTO>>? shopOwnerWallets = _walletHelper.GetAll($"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets", new Dictionary<string, string> { { "userId", shopOwner.FirstOrDefault()?.Id.ToString() ?? string.Empty } });
            if (shopOwnerWallets?.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = shopOwnerWallets?.ExceptionMessage
                };
            }
            #endregion

            #region Transcation Client => Cashier (Neo4j)
            //Create Transaction Client -> Cashier (Neo4j)
            var transactionDTO = _mapper.Map<TransactionDTO>(transaction);
            ResponseAPI<TransactionDTO>? response = _helper.Post($"{_configuration.PaymentAddress}/Api/Transaction/CreateUniqueQrCodeTransaction",
                                                                transactionDTO,
                                                                new Dictionary<string, string> { { "pinCode", pinCode } });
            if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred in payment Micro-Service"
                };
            }
            else if (response.StatusCode != HttpStatusCode.Created)
            {
                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = response.ExceptionMessage
                };
            }
            #endregion

            if (response != null
                && response.StatusCode == HttpStatusCode.Created
                && response.ObjectValue != null)
            {

                #region Transaction Cashier => Shop Owner (Neo4j)
                ResponseAPI<TransactionDTO>? responseTransactionCashierShop = _helper.Post(
                    $"{_configuration.PaymentAddress}/Api/Transaction/Create",
                    new TransactionDTO
                    {
                        SenderWalletId = transaction.ReceiverWalletId,
                        ReceiverWalletId = shopOwnerWallets.ObjectValue?.FirstOrDefault()?.Id ?? Guid.Empty,
                        Amount = response.ObjectValue.Amount,
                    },
                    new Dictionary<string, string> { { "pinCode", pinCode } });

                if (responseTransactionCashierShop?.StatusCode != HttpStatusCode.Created)
                {
                    TransactionDTO transactioncashierUser = new TransactionDTO
                    {
                        SenderWalletId = transaction.ReceiverWalletId,
                        ReceiverWalletId = transaction.SenderWalletId,
                        Amount = response.ObjectValue.Amount,
                        QrCodeId = transaction.QrCodeId,
                        IsCredit = true
                    };
                    ResponseAPI<TransactionDTO>? transactionRollBack = _helper.Post(
                        $"{_configuration.PaymentAddress}/Api/Transaction/Create",
                        transactioncashierUser,
                        new Dictionary<string, string> { { "pinCode", pinCode } });

                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                }
                #endregion

                #region Transaction Client => Shop Owner Blockchain

                ResponseAPI<TransactionDTO>? blockchaintransactionResponse = _helper.Post(
                    $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/TransactionClientShop",
                    new TransactionDTO
                    {
                        SenderWalletId = transaction.SenderWalletId,
                        ReceiverWalletId = shopOwnerWallets.ObjectValue?.FirstOrDefault()?.Id ?? Guid.Empty,
                        Amount = response.ObjectValue.Amount,
                        IsCredit = response.ObjectValue.IsCredit,
                        QrCodeId = transaction.QrCodeId,
                        Status = TransactionStatus.succeeded,
                        TransactionDate = DateTime.UtcNow
                    },
                    new Dictionary<string, string> { { "pinCode", pinCode } });
                if (blockchaintransactionResponse == null || blockchaintransactionResponse.StatusCode != HttpStatusCode.Created)
                {
                    //Roll Back deux transaction Neo4j
                    return new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = blockchaintransactionResponse?.ExceptionMessage
                    };

                }
                #endregion

                return new ResponseAPI<TransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = response.ObjectValue
                };
            }

            return new ResponseAPI<TransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "An error occurred in payment Micro-Service"
            };
        }

        public ResponseAPI<bool> TransactionClientToCashier(TransactionDTO transaction, string pinCode, Guid senderUserId)
        {
            //Check User Id exist
            List<UserDTO>? client = _userService.Get(u =>
                                                        u.Id == senderUserId &&
                                                        u.UserType == Shared.Enum.UserType.Client &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;
            if (client == null || client.Count <= 0)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User Unauthorized"
                };
            }

            //Check QRCODE use it or not
            ResponseAPI<bool>? transactionsQRCode = _helperBool.Get($"{_configuration.PaymentAddress}/Api/Transaction/CheckQRcodeExist", transaction.QrCodeId);
            if (transactionsQRCode == null
                || transactionsQRCode.StatusCode != System.Net.HttpStatusCode.OK)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "Error in Payment Microservice"
                };
            }
            if (transactionsQRCode != null
                && transactionsQRCode.StatusCode == System.Net.HttpStatusCode.OK
                && transactionsQRCode.ObjectValue)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "QRcode already use it !"
                };
            }

            transaction.Id = Guid.NewGuid();
            //Get Cashier Wallet

            string walletGetUrl = $"{_configuration.PaymentAddress}/Api/Wallet/Get";
            ResponseAPI<WalletDTO>? cashierWallet = _walletHelper.Get(walletGetUrl, transaction.ReceiverWalletId);
            if (cashierWallet == null || cashierWallet.StatusCode == HttpStatusCode.InternalServerError)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred in payment Micro-Service"
                };

            }
            else if (cashierWallet?.StatusCode == HttpStatusCode.BadRequest)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = cashierWallet.ExceptionMessage
                };
            }

            //Check Cashier Id 
            List<UserDTO>? cashier = _userService.Get(u =>
                                                        u.Id == cashierWallet?.ObjectValue?.AssignedToId &&
                                                        u.UserType == Shared.Enum.UserType.Cashier &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;
            if (cashier == null || cashier.Count <= 0)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User Unauthorized"
                };
            }


            //Get Company Shop Owner Id 
            List<CompanyDTO>? shopOwner = _companyService.Get(u =>
                                                        u.Id == cashier.FirstOrDefault()?.CompanyId &&
                                                        u.Status == Shared.Enum.Status.Active).ObjectValue;

            if (shopOwner == null || shopOwner.Count <= 0)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User Unauthorized"
                };
            }

            //Get shop Owner Wallet Id 
            string walletUrl = $"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets";
            Dictionary<string, string> queryParamsWallet = new Dictionary<string, string>
                    {
                        { "userId", shopOwner.FirstOrDefault().Id.ToString() }
                    };

            ResponseAPI<List<WalletDTO>>? responseWallet = _walletHelper.GetAll(walletUrl, queryParamsWallet);
            if (responseWallet?.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = responseWallet?.ExceptionMessage
                };
            }

            //Create Transaction user => Cashier (Neo4j)
            string Url = $"{_configuration.PaymentAddress}/Api/Transaction/Create";
            Dictionary<string, string> queryParams = new Dictionary<string, string>
            {
                        {"pinCode", pinCode}
                    };
            ResponseAPI<TransactionDTO>? response = _helper.Post(Url, transaction, queryParams);
            if (response?.StatusCode != HttpStatusCode.Created)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = response.ExceptionMessage
                };
            }


            //Create Transaction cashier => Shop Owner (Neo4j)
            TransactionDTO transactionCashierShop = new TransactionDTO
            {
                Id = transaction.Id,
                SenderWalletId = transaction.ReceiverWalletId,
                ReceiverWalletId = responseWallet.ObjectValue.FirstOrDefault().Id,
                QrCodeId = transaction.QrCodeId,
                Amount = transaction.Amount,
            };
            ResponseAPI<TransactionDTO>? responseTransactionCashierShop = _helper.Post(Url, transactionCashierShop, queryParams);
            if (responseTransactionCashierShop?.StatusCode != HttpStatusCode.Created)
            {
                TransactionDTO transactioncashierUser = new TransactionDTO
                {
                    SenderWalletId = transaction.ReceiverWalletId,
                    ReceiverWalletId = transaction.SenderWalletId,
                    Amount = transaction.Amount,
                    QrCodeId = transaction.QrCodeId,
                    IsCredit = transaction.IsCredit
                };
                ResponseAPI<TransactionDTO>? responseRollBack = _helper.Post(Url, transactioncashierUser, queryParams);
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred in payment Micro-Service"
                };
            }

            //Create Transaction user => Shop owner (Blockchain)
            string BlockChainUrl = $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/TransactionClientShop";
            TransactionDTO transactionUserShop = new TransactionDTO
            {
                Id = transaction.Id,
                SenderWalletId = transaction.SenderWalletId,
                ReceiverWalletId = responseWallet.ObjectValue.FirstOrDefault().Id,
                Amount = transaction.Amount,
                IsCredit = transaction.IsCredit,
                QrCodeId = transaction.QrCodeId,
                Status = transaction.Status,
                TransactionDate = transaction.TransactionDate
            };

            ResponseAPI<TransactionDTO>? blockchaintransactionResponse = _helper.Post(BlockChainUrl, transactionUserShop, queryParams);
            if (blockchaintransactionResponse == null || blockchaintransactionResponse.StatusCode != HttpStatusCode.Created)
            {
                //Roll Back deux transaction Neo4j
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = blockchaintransactionResponse?.ExceptionMessage
                };
            }

            return new ResponseAPI<bool>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = true
            };

        }

        #endregion

        #region Send Tickets

        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionForGroup(Guid companyId, Guid userId, Guid groupId, List<TicketDTO> TicketsId)
        {
            var groupDTO = _groupService.Get(groupId).ObjectValue;
            if (groupDTO == null)
            {
                return new ResponseAPI<List<TransactionUserDTO>>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Group not found"
                };
            }

            var employeeDTOList = groupDTO.Employees?.ToList();
            if (employeeDTOList != null && employeeDTOList.Count() > 0)
            {
                double totalAmount = 0;
                List<ReceiverListDTO> receiverList = new List<ReceiverListDTO>();
                foreach (var ticket in TicketsId)
                {
                    var ticketDTO = _ticketService.Get(companyId, ticket.TicketId).ObjectValue;
                    if (ticketDTO != null)
                    {
                        
                        
                        foreach (var employee in employeeDTOList)
                        {
                            var userDTO = _userService.Get(employee.UserId).ObjectValue;
                            if (userDTO != null)
                            {
                                totalAmount += ticketDTO.TotalAmount;
                                receiverList.Add(new ReceiverListDTO
                                {
                                    ReceiverUserId = employee.UserId,
                                    Amount = ticketDTO.TotalAmount,
                                    ReceiverWalletType = ticketDTO.Type
                                });
                            }
                            else
                            {
                                return new ResponseAPI<List<TransactionUserDTO>>()
                                {
                                    StatusCode = System.Net.HttpStatusCode.NotFound,
                                    ExceptionMessage = $"User not found"
                                };
                            }

                        }                    
                    }
                    else
                    {
                        return new ResponseAPI<List<TransactionUserDTO>>()
                        {
                            StatusCode = System.Net.HttpStatusCode.NotFound,
                            ExceptionMessage = $"Ticket not found"
                        };

                    }
                }

                ResponseAPI<List<WalletDTO>>? companyWallets = _walletHelper.GetAll($"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets", new Dictionary<string, string> { { "userId", companyId.ToString() } });

                if(companyWallets == null || companyWallets.ObjectValue == null || companyWallets.ObjectValue.Count < 0 ||
                    companyWallets.ObjectValue.FirstOrDefault()?.Balance < totalAmount) 
                {
                    return new ResponseAPI<List<TransactionUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Balance of the company insufficient to send this amount of tickets !"
                    };
                }
                var transactionUserDTO = new TransactionGroupDTO()
                {
                    Id = Guid.NewGuid(),
                    SenderUserId = companyId,
                    QrCodeId = Guid.NewGuid(),
                    TotalAmount = totalAmount,
                    Status = TransactionStatus.succeeded,
                    TransactionDate = DateTime.Now,
                    ReceiverList = receiverList
                };

                Dictionary<string, string> queryParams = new Dictionary<string, string>
                        {
                            {"pinCode", companyId.ToString()}
                        };
                ResponseAPI<TransactionGroupDTO>? response = _transactionGroupHelper.Post($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/CreateTransactionsForGroup",
                    transactionUserDTO,
                    queryParams);
                if (response?.StatusCode != HttpStatusCode.Created)
                {
                    

                    return new ResponseAPI<List<TransactionUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = response?.ExceptionMessage
                    };
                }
                BalanceNotificationDTO balanceNotification = new BalanceNotificationDTO
                {
                    Balance = -totalAmount,
                    GroupName = companyId.ToString()
                };
                ResponseAPI<BalanceNotificationDTO>? notification = _notificationHelper.Post($"{_configuration.NotificationAddress}/Api/Notification/SendBalance", balanceNotification);
                return new ResponseAPI<List<TransactionUserDTO>>()
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                };

            }
            else
            {
                return new ResponseAPI<List<TransactionUserDTO>>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "Group does not contain any employee"
                };
            }

        }
        public ResponseAPI<List<TransactionUserDTO>> SendMultipleTicketsToGroups(Guid companyId, Guid userId, List<GroupTicketDTO> groupTickets)
        {
            List<ReceiverListDTO> receiverList = new List<ReceiverListDTO>();
            double totalAmount = 0;
            foreach (var groupTicket in groupTickets)
            {
                if(groupTicket.GroupId == null || groupTicket.TicketId == Guid.Empty)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"Group not found"
                    };
                }
                var groupDTO = _groupService.Get((Guid)groupTicket.GroupId).ObjectValue;
                if (groupDTO == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"Group not found"
                    };
                }
                var ticketDTO = _ticketService.Get(companyId, groupTicket.TicketId).ObjectValue;
                if(ticketDTO == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"Ticket not found"
                    };
                }

                var employeeDTOList = groupDTO.Employees?.ToList();
                
                if(employeeDTOList != null)
                {
                    totalAmount += ticketDTO.TotalAmount * employeeDTOList.Count;
                    foreach (var employee in employeeDTOList)
                    {
                        var userDTO = _userService.Get(employee.UserId).ObjectValue;
                        if (userDTO != null)
                        {
                            receiverList.Add(new ReceiverListDTO
                            {
                                ReceiverUserId = employee.UserId,
                                Amount = ticketDTO.TotalAmount,
                                ReceiverWalletType = ticketDTO.Type
                            });
                        }
                        else
                        {
                            return new ResponseAPI<List<TransactionUserDTO>>()
                            {
                                StatusCode = System.Net.HttpStatusCode.NotFound,
                                ExceptionMessage = $"User not found"
                            };
                        }

                    }
                }
            }

            var transactionUserDTO = new TransactionGroupDTO()
            {
                Id = Guid.NewGuid(),
                SenderUserId = companyId,
                QrCodeId = Guid.NewGuid(),
                TotalAmount = totalAmount,
                Status = TransactionStatus.succeeded,
                TransactionDate = DateTime.Now,
                ReceiverList = receiverList
            };

            Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"pinCode", companyId.ToString()}
                    };
            ResponseAPI<TransactionGroupDTO>? response = _transactionGroupHelper.Post($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/CreateTransactionsForGroup",
                transactionUserDTO,
                queryParams);

            if (response?.StatusCode != HttpStatusCode.Created)
            {
                BalanceNotificationDTO balanceNotification = new BalanceNotificationDTO
                {
                    Balance = -totalAmount,
                    GroupName = companyId.ToString()
                };
                ResponseAPI<BalanceNotificationDTO>? notification = _notificationHelper.Post($"{_configuration.NotificationAddress}/Api/Notification/SendBalance", balanceNotification);
                return new ResponseAPI<List<TransactionUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = response?.ExceptionMessage
                };
            }
            return new ResponseAPI<List<TransactionUserDTO>>()
            {
                StatusCode = System.Net.HttpStatusCode.OK
            };
        }
        public ResponseAPI<List<TransactionUserDTO>> SendTicketsToEmployees(Guid companyId, Guid userId, List<EmployeeTicketDTO> employeeTickets)
        {
            List<ReceiverListDTO> receiverList = new List<ReceiverListDTO>();
            double totalAmount = 0;
            foreach (var employeeTicket in employeeTickets)
            {
                var ticketDTO = _ticketService.Get(companyId, employeeTicket.TicketId).ObjectValue;
                if (ticketDTO == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"Ticket not found"
                    };
                }
                totalAmount += ticketDTO.TotalAmount;
                var userDTO = _userService.Get(employeeTicket.EmployeeId).ObjectValue;
                if(userDTO == null)
                {
                    return new ResponseAPI<List<TransactionUserDTO>>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = $"User not found"
                    };
                }

                receiverList.Add(new ReceiverListDTO
                {
                    ReceiverUserId = userDTO?.Id ?? Guid.Empty,
                    Amount = ticketDTO.TotalAmount,
                    ReceiverWalletType = ticketDTO.Type
                });
            }

            var transactionUserDTO = new TransactionGroupDTO()
            {
                Id = Guid.NewGuid(),
                SenderUserId = companyId,
                QrCodeId = Guid.NewGuid(),
                TotalAmount = totalAmount,
                Status = TransactionStatus.succeeded,
                TransactionDate = DateTime.Now,
                ReceiverList = receiverList
            };

            Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        {"pinCode", companyId.ToString()}
                    };
            ResponseAPI<TransactionGroupDTO>? response = _transactionGroupHelper.Post($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/CreateTransactionsForGroup",
                transactionUserDTO,
                queryParams);
            if (response?.StatusCode != HttpStatusCode.Created)
            {
                return new ResponseAPI<List<TransactionUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = response?.ExceptionMessage
                };

            }
            BalanceNotificationDTO balanceNotification = new BalanceNotificationDTO
            {
                Balance = -totalAmount,
                GroupName = companyId.ToString()
            };
            ResponseAPI<BalanceNotificationDTO>? notification = _notificationHelper.Post($"{_configuration.NotificationAddress}/Api/Notification/SendBalance", balanceNotification);

            return new ResponseAPI<List<TransactionUserDTO>>()
            {
                StatusCode = System.Net.HttpStatusCode.OK
            };
            
        }
        #endregion
    }
}
