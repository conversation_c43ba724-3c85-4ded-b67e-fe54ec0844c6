﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.CashBack;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.CashBack;
using Platform.Dyno.AccessManagement.DataModel.CashBack;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.CashBack
{
    public class FailedCashBackService : IFailedCashBackService
    {
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IUnitOfWork<FailedCashBackEntity> _failedCashBackRepository;
        private readonly ICompanyService _companyService;
        private readonly ICashBackService _cashbackService;
        private readonly IUserService _userService;
        private readonly string _failedCashBackCacheKey = RedisCacheKey.FailedCashBackCacheKey;

        public FailedCashBackService(IUnitOfWork<FailedCashBackEntity> failedCashBackRepository,
            ICashBackService cashbackService,
            ICompanyService companyService,
            IUserService userService,
            IMapper mapper,
            IRedisCacheService cache)
        {
            _failedCashBackRepository = failedCashBackRepository;
            _companyService = companyService;
            _cashbackService = cashbackService;
            _userService = userService;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<FailedCashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<FailedCashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<List<FailedCashBackDTO>> response = new ResponseAPI<List<FailedCashBackDTO>>();
            var cashbacksDTO = _cache.GetData<List<CashBackDTO>>(_failedCashBackCacheKey + companyId.ToString());
            try
            {
                if (cashbacksDTO == null || cashbacksDTO.Count() == 0)
                {
                    IList<FailedCashBackEntity> failedCashBacksEntity;
                    if (isSuperAdmin == true)
                    {
                        failedCashBacksEntity = _failedCashBackRepository.Repository.GetAll(includes: new List<string> { "Cashback" , "Cashback.Company", "Cashback.PaymentDetails" });
                    }
                    else
                    {
                        failedCashBacksEntity = _failedCashBackRepository.Repository.GetAll(failedCashBacks => failedCashBacks.Cashback.CompanyId == companyId, includes: new List<string> { "Cashback","Cashback.Company", "Cashback.PaymentDetails" });
                    }
                    var failedCashBacksBM = _mapper.Map<List<FailedCashBackBM>>(failedCashBacksEntity);
                    var failedCashBacksDTOList = _mapper.Map<List<FailedCashBackDTO>>(failedCashBacksBM);
                    response.StatusCode = failedCashBacksDTOList == null || failedCashBacksDTOList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = failedCashBacksDTOList?.OrderByDescending(c => c.Cashback?.Code).ToList();
                    _cache.SetData(_failedCashBackCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<FailedCashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<FailedCashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<FailedCashBackDTO>> response = new ResponseAPI<PagedList<FailedCashBackDTO>>();
            var failedCashBacksDTO = _cache.GetData<List<CashBackDTO>>(_failedCashBackCacheKey + companyId.ToString());
            try
            {
                if (failedCashBacksDTO == null || failedCashBacksDTO.Count() == 0)
                {
                    var failedCashBacksDTOList = GetAll(companyId, isSuperAdmin).ObjectValue;
                    if (failedCashBacksDTOList != null)
                    {
                        response.StatusCode = failedCashBacksDTO == null || failedCashBacksDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                        response.ObjectValue = PagedList<FailedCashBackDTO>.ToGenericPagedList(failedCashBacksDTOList, pagedParameters);
                        _cache.SetData(_failedCashBackCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                    }
                    else
                    {
                        response.StatusCode = HttpStatusCode.NotFound;
                        response.ObjectValue = PagedList<FailedCashBackDTO>.ToGenericPagedList(new List<FailedCashBackDTO>(), pagedParameters);
                    }

                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        #endregion

        #region Create
        public ResponseAPI<FailedCashBackDTO> Create(Guid companyId, bool isSuperAdmin, FailedCashBackDTO failedCashBackDTO, Guid creatorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<FailedCashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(creatorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<FailedCashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={creatorUserId} is not found. Please Contact IT support"
                };
            }
            if (failedCashBackDTO.Reason.Length > 256)
            {
                return new ResponseAPI<FailedCashBackDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"reason exceeds 256 caractere"
                };
            }
            var cashbackDTO = _cashbackService.Get(companyId, isSuperAdmin, failedCashBackDTO.CashbackId).ObjectValue;

            if (cashbackDTO == null)
            {
                return new ResponseAPI<FailedCashBackDTO>()
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = $"cashback with id = {failedCashBackDTO.CashbackId} not found"
                };
            }
            failedCashBackDTO.Id = Guid.NewGuid();
            #region Refdata
            RefDataService<FailedCashBackDTO>.CreateRefData(failedCashBackDTO, cashbackDTO.CreatorUserId, cashbackDTO.CreatorUserEmail);
            RefDataService<FailedCashBackDTO>.UpdateRefData(failedCashBackDTO, creatorUserId, user.CreatorUserEmail);
            if (isSuperAdmin)
            {
                failedCashBackDTO.Status = CashBackStatus.Rejected;
            }
            else
            {
                failedCashBackDTO.Status = CashBackStatus.Cancelled;
            }
            
            #endregion
            FailedCashBackBM failedCashBackBM = _mapper.Map<FailedCashBackBM>(failedCashBackDTO);
            FailedCashBackEntity failedCashBackEntity = _mapper.Map<FailedCashBackEntity>(failedCashBackBM);
            _failedCashBackRepository.Repository.Insert(failedCashBackEntity);
            _failedCashBackRepository.Save();
            failedCashBackDTO.Id = failedCashBackEntity.Id;
            if (isSuperAdmin)
            {
                RefDataService<CashBackDTO>.DeleteRefData(cashbackDTO, creatorUserId, user.Email);
                cashbackDTO.Status = CashBackStatus.Rejected;
            }
            else
            {
                RefDataService<CashBackDTO>.DeleteRefData(cashbackDTO, creatorUserId, user.Email);
                cashbackDTO.Status = CashBackStatus.Cancelled;
            }
            var CashBack = _cashbackService.Update(cashbackDTO.CompanyId, isSuperAdmin, cashbackDTO, creatorUserId);
            if (CashBack.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<FailedCashBackDTO>()
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = CashBack.ExceptionMessage
                };
            }
            if (updateCache)
            _cache.RemoveData(_failedCashBackCacheKey + companyId.ToString());
            return new ResponseAPI<FailedCashBackDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = failedCashBackDTO
            };
        }

        #endregion
    }
}
