﻿using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.SalesOrder
{
    public class FailedSalesOrderEntity: ReferentialData
    {
        public Guid Id { get; set; }
        [ForeignKey("SalesOrderId")]
        public Guid SalesOrderId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public SalesOrderEntity? SalesOrder { get; set; }
        public new SalesOrderStatus Status { get; set; } = SalesOrderStatus.Cancelled;
    }
}
