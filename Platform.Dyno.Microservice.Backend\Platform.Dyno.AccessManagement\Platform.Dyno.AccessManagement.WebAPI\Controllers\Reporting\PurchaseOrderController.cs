﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Reporting
{
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderController : ControllerBase
    {
        private readonly IPurchaseOrderService _purchaseOrderService;
        private readonly ILogger<PurchaseOrderController> _logger;
        private readonly ISortingUtility _sortingUtility;

        public PurchaseOrderController(IPurchaseOrderService purchaseOrderService,
            ILogger<PurchaseOrderController> logger,
            ISortingUtility sortingUtility)
        {
            _purchaseOrderService = purchaseOrderService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }


        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                ResponseAPI<List<DocumentsDTO>> documents = _purchaseOrderService.GetAll();
                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<DocumentsDTO>> documents = _purchaseOrderService.GetAll(pagedParameters);

                if (documents.ObjectValue != null)
                {
                    documents.ObjectValue = _sortingUtility.SortData(pagedParameters, documents);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = documents.ObjectValue.TotalCount,
                        PageSize = documents.ObjectValue.PageSize,
                        CurrentPage = documents.ObjectValue.CurrentPage,
                        TotalPages = documents.ObjectValue.TotalPages,
                        HasNext = documents.ObjectValue.HasNext,
                        HasPrevious = documents.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<DocumentsDTO> document = _purchaseOrderService.Get(id);
                return Ok(document);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Create 

        //2: Create Purchase Order PDF
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add(SalesOrderDTO salesOrder)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<string> response = _purchaseOrderService.CreatePDF(salesOrder.Code,
                    salesOrder.ProductType,
                    salesOrder.DynoAmount,
                    salesOrder.NetAmount,
                    salesOrder.VATAmount,
                    salesOrder.TotalAmount,
                    salesOrder.PaymentMethod,
                    salesOrder.CompanyId
                    );
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
