﻿using Camunda.Worker;
using Camunda.Worker.Variables;
using Microsoft.Extensions.Logging;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("Send_Notification_New_SO")]
    public class SendNotifSA : IExternalTaskHandler
    {
        private readonly ILogger<LogErrorSalesOrderEmailFailed> _logger;
        private readonly ISignalRNotificationService _signalRNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ConfigurationDefaultId _configuration;
        public SendNotifSA(ISignalRNotificationService signalRNotificationService,
            ICompanyService companyService,
            ILogger<LogErrorSalesOrderEmailFailed> logger,
            ConfigurationDefaultId configuration)
        {
            _logger = logger;
            _signalRNotificationService = signalRNotificationService;
            _companyService = companyService;
            _configuration = configuration;
        }
        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());
            var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
            SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
            
            string? resultSendEmail = null;

            if (externalTask.Variables.TryGetValue("Result_Send_Email_SO", out var resultVar))
            {
                if (resultVar is StringVariable stringVar)
                {
                    resultSendEmail = stringVar.Value;
                }
                else if (resultVar is JsonVariable jsonVar)
                {
                    resultSendEmail = (string?)jsonVar.Value; // Si le result est stocké en JSON
                }
                else
                {
                    _logger.LogWarning("Resultat inattendu pour Result_Send_Email_SO : {Type}", resultVar.GetType().Name);
                }
            }
            else
            {
                _logger.LogDebug("Aucune erreur trouvée dans Result_Send_Email_SO");
            }
            //string? resultSendEmail = externalTask.Variables["Result_Send_Email_SO"].Value?.ToString();
            if (salesOrderDTO != null)
            {
                CompanyDTO? company = _companyService.Get(salesOrderDTO.CompanyId).ObjectValue;
                CompanyDTO? superCompany = _companyService.Get(_configuration.CompanyId).ObjectValue;
                if (company != null)
                {
                    if (resultSendEmail == "fasle")
                    {
                        SignalRNotificationDTO notificationDTO = new SignalRNotificationDTO
                        {
                            Title = "Email Failed",
                            Message = "Email Failed To generate in Sales order !",
                            SendToId = new List<CompanyDTO> { company }

                        };
                        _signalRNotificationService.Create(notificationDTO, companyId: salesOrderDTO.CompanyId);
                    }
                    if(superCompany != null)
                    {
                        SignalRNotificationDTO superAdminnotificationDTO = new SignalRNotificationDTO
                        {
                            Title = "New Sales Order",
                            Message = $"New Sales order request generate from company {company?.Name} !",
                            SendToId = new List<CompanyDTO> { superCompany }

                        };
                        _signalRNotificationService.Create(superAdminnotificationDTO, companyId: salesOrderDTO.CompanyId);
                    }
                    
                }              
            }

            var completeResult = new CompleteResult();
            return completeResult;
        }
    }
}
