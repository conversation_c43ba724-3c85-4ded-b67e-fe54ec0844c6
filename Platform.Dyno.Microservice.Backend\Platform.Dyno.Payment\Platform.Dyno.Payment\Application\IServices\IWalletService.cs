﻿using Application.Models.BMs;
using Domain.Enums.Query;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface IWalletService
    {
        public ResponseAPI<List<WalletDTO>> GetAll(string sort ="",
            Ordering order =Ordering.Desc, int limit=int.MaxValue, int skip=0);
        public ResponseAPI<decimal> GetTotalBalanceByUserType(UserType userType);
        public ResponseAPI<List<WalletDTO>> GetAllByUserType(UserType userType, string sort = "",
            Ordering order = Ordering.Desc, int limit = int.MaxValue, int skip = 0);
        public ResponseAPI<WalletDTO> Get(Guid id);
        public ResponseAPI<List<WalletDTO>> GetUserWallets(Guid userId);
        public ResponseAPI<WalletDTO> GetUserWalletByType(Guid userId, WalletType walletType);

        public ResponseAPI<WalletDTO> Create(WalletDTO walletDTO,Guid CreateUserId, string pinCode);
        public ResponseAPI<List<WalletDTO>> CreateDefaultWallets(WalletDTO walletDTO, Guid CreateUserId, string pinCode);
        public ResponseAPI<WalletDTO> Update(WalletDTO walletDTO, Guid UpdateUserId);
        public ResponseAPI<WalletDTO> Delete(Guid id, Guid DeleteUserId);
    }
}
