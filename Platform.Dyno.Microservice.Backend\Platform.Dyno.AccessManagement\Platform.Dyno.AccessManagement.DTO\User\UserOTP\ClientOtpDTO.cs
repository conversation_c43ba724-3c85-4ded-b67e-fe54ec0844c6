﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.User.UserOTP
{
    public class ClientOtpDTO
    {
        #region Data

        [Required(ErrorMessage = "Phone number is required")]
        public string CountryCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Code is required")]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "The 'Code' must be a 4-digit number.")]
        public int Code { get; set; }

        public string MacAddress { get; set; } = string.Empty;
        public bool IsSaved { get; set; }
        #endregion
    }
}
