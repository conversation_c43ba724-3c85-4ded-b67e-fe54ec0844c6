﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.User.EmailConfirmation
{
    public class EmailConfirmationDTO
    {
        #region data
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;
        [Required(ErrorMessage = "token is required")]
        public string Token { get; set; } = string.Empty;
        #endregion
    }
}
