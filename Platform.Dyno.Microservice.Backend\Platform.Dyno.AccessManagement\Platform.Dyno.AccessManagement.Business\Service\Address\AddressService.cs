﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;

namespace Platform.Dyno.AccessManagement.Business.Service.Address;

public class AddressService : IAddressService
{
    private readonly IUnitOfWork<AddressEntity> _addressRepository;
    private readonly IMapper _mapper;
    private readonly IRedisCacheService _cache;
    private readonly string _addressCacheKey = RedisCacheKey.AddressCacheKey;
    private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("address");

    public AddressService(IUnitOfWork<AddressEntity> addressRepository, IMapper mapper, IRedisCacheService cache)
    {
        _addressRepository = addressRepository;
        _mapper = mapper;
        _cache = cache;
    }

    #region Get
    public ResponseAPI<List<AddressDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<AddressDTO>> response = new ResponseAPI<List<AddressDTO>>();
        
        try
        {
            List<AddressDTO>? addressesDTO = _cache.GetData<List<AddressDTO>>(_addressCacheKey);
            if (addressesDTO == null || addressesDTO.Count() == 0)
            {
                List<AddressEntity> addressesEntity = (List<AddressEntity>)_addressRepository.Repository.GetAll();
                List<AddressBM> addressesBM = _mapper.Map<List<AddressBM>>(addressesEntity);
                addressesDTO = _mapper.Map<List<AddressDTO>>(addressesBM);
                
                _cache.SetData(_addressCacheKey, addressesDTO, DateTimeOffset.UtcNow.AddDays(1));
            }

            response.StatusCode = addressesDTO == null || addressesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = addressesDTO;
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    
    public ResponseAPI<PagedList<AddressDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<PagedList<AddressDTO>> response = new ResponseAPI<PagedList<AddressDTO>>();
        var addressesDTO = _cache.GetData<List<AddressDTO>>(_addressCacheKey);
        try
        {
            if (addressesDTO == null || addressesDTO.Count() == 0)
            {
                addressesDTO = GetAll(companyId, userType).ObjectValue;
                
                _cache.SetData(_addressCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
            }

            response.StatusCode = addressesDTO == null || addressesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = PagedList<AddressDTO>.ToGenericPagedList(addressesDTO, pagedParameters);
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }

    public ResponseAPI<PagedList<AddressDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<PagedList<AddressDTO>> response = new ResponseAPI<PagedList<AddressDTO>>();
        var addressesDTO = _cache.GetData<List<AddressDTO>>(_addressCacheKey);
        try
        {
            if (addressesDTO == null || addressesDTO.Count() == 0)
            {
                addressesDTO = GetAll().ObjectValue?.Where(address => address.Status == Shared.Enum.Status.Active).ToList();
                response.StatusCode = addressesDTO == null || addressesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = PagedList<AddressDTO>.ToGenericPagedList(addressesDTO, pagedParameters);
                _cache.SetData(_addressCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));

            }
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<AddressDTO> Get(Guid id)
    {
        ResponseAPI<AddressDTO> response = new ResponseAPI<AddressDTO>();
        try
        {
            AddressDTO? addressDTO = GetAll().ObjectValue?.Where(address => address.Id == id).FirstOrDefault();
            response.StatusCode = addressDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ExceptionMessage = addressDTO == null ? exceptionMessages.ReadError : null;
            response.ObjectValue = addressDTO;
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;

    }

    public ResponseAPI<List<AddressDTO>> Get(Func<AddressDTO, bool> expression)
    {
        var addressesDTO = GetAll().ObjectValue?.Where(expression).ToList();
        return new ResponseAPI<List<AddressDTO>>
        {
            StatusCode = HttpStatusCode.OK,
            ObjectValue = addressesDTO
        };
    }
    #endregion

    #region Create
    public ResponseAPI<AddressDTO> Create(AddressDTO addressDTO, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
    {
        #region Refdata
        addressDTO.Id = Guid.NewGuid();
        RefDataService<AddressDTO>.CreateRefData(addressDTO, creatorUserId);
        #endregion

        AddressBM addressBM = _mapper.Map<AddressBM>(addressDTO);
        AddressEntity addressEntity = _mapper.Map<AddressEntity>(addressBM);
        try
        {
            _addressRepository.Repository.Insert(addressEntity);
            _addressRepository.Save();
            addressDTO.Id = addressEntity.Id;

            if (updateCache)
                _cache.RemoveData(_addressCacheKey);

            return new ResponseAPI<AddressDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = addressDTO
            };
        }
        catch(Exception ex)
        {
            return new ResponseAPI<AddressDTO>()
            {
                StatusCode = HttpStatusCode.BadRequest,
                ExceptionMessage = exceptionMessages.CreateError + $"Exception Message : {ex.Message}"
            };
        }
        
    }
    #endregion

    #region Update
    public ResponseAPI<AddressDTO> Update(AddressDTO addressDTO, Guid? updateUserId = null, bool updateCache = true)
    {
        try
        {
            if (Get(addressDTO.Id) != null)
            {
                #region Refdata
                RefDataService<AddressDTO>.UpdateRefData(addressDTO, updateUserId);
                #endregion

                AddressBM addressBM = _mapper.Map<AddressBM>(addressDTO);
                AddressEntity addressEntity = _mapper.Map<AddressEntity>(addressBM);
                _addressRepository.Repository.Update(addressEntity);
                _addressRepository.Save();

                if (updateCache)
                    _cache.RemoveData(_addressCacheKey);
                return new ResponseAPI<AddressDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = addressDTO
                };
            }

            return new ResponseAPI<AddressDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }catch(Exception ex)
        {
            return new ResponseAPI<AddressDTO>()
            {
                StatusCode = HttpStatusCode.BadRequest,
                ExceptionMessage = exceptionMessages.UpdateError + $"Exception Message : {ex.Message}"
            };
        }
        

    }
    #endregion

    #region Delete
    public ResponseAPI<AddressDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
    {
        try
        {
            AddressDTO? addressDTO = Get(id).ObjectValue;
            if (addressDTO != null)
            {
                #region Refdata
                RefDataService<AddressDTO>.DeleteRefData(addressDTO, deletorUserId);
                #endregion
                AddressBM addressBM = _mapper.Map<AddressBM>(addressDTO);
                AddressEntity addressEntity = _mapper.Map<AddressEntity>(addressBM);
                addressEntity.Status = Shared.Enum.Status.Deleted;
                _addressRepository.Repository.Update(addressEntity);
                _addressRepository.Save();
                addressDTO.Status = Shared.Enum.Status.Deleted;
                if (updateCache)
                {
                    _cache.RemoveData(_addressCacheKey);
                }
                return new ResponseAPI<AddressDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = addressDTO
                };
            }
            return new ResponseAPI<AddressDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.DeleteError
            };
        }catch(Exception ex)
        {
            return new ResponseAPI<AddressDTO>()
            {
                StatusCode = HttpStatusCode.BadRequest,
                ExceptionMessage = exceptionMessages.DeleteError + $"Exception Message : {ex.Message}"
            };
        }
        
    }
    #endregion
}
