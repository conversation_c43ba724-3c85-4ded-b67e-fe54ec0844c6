﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-WatchDog-9684ad53-6277-4417-96b9-2993cbf84ef8</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Confluent.Kafka" Version="1.9.3" />
	<PackageReference Include="Microsoft.AspNet.SignalR" Version="2.4.3" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="5.0.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Udp" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\DynoTools\DynoTools\DynoTools.csproj" />
    <ProjectReference Include="..\..\Platform.Dyno.Notification\Platform.Dyno.Notification.DTO\Platform.Dyno.Notification.DTO.csproj" />
    <ProjectReference Include="..\..\Server.Kafka\Server.Kafka\Server.Kafka.csproj" />
  </ItemGroup>
</Project>
