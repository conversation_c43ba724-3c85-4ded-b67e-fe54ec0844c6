﻿using Application.IServices;
using Application.Models.BMs;
using Application.Services;
using Domain.Enums.Query;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.Paiement.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class WalletController : ControllerBase
    {
        private readonly IWalletService _walletService;
        private readonly ILogger<WalletController> _logger;

        public WalletController(IWalletService walletService,
            ILogger<WalletController> logger)
        {
            _walletService = walletService;
            _logger = logger;
        }

        //#region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {

                var  wallets = _walletService.GetAll();
                return Ok(wallets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllByUserType")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllByUserType(UserType userType)
        {
            try
            {

                var wallets = _walletService.GetAllByUserType(userType);
                return Ok(wallets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllByUserType)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetTotalBalanceByUserType")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetTotalBalanceByUserType(UserType userType)
        {
            try
            {
                var balance = _walletService.GetTotalBalanceByUserType(userType);
                return Ok(balance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetTotalBalanceByUserType)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPagedWallets()
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Wallet);
                var wallets = _walletService.GetAll(sort, order, limit, skip);

                return Ok(wallets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<WalletDTO> wallet = _walletService.Get(id);
                return Ok(wallet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("GetUserWallets")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserWallets(Guid userId)
        {
            try
            {
                ResponseAPI<List<WalletDTO>> response = _walletService.GetUserWallets(userId);
                if (response.StatusCode== System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #region Create 
        [Route("Create/{pinCode}")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] WalletDTO walletDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<WalletDTO> response = _walletService.Create(walletDTO, new Guid(), pinCode);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        [Route("CreateDefaultWallets")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateDefaultWallets([FromBody] List<WalletDTO> walletDTOList,string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                //string? createUserId = HttpContext.User.FindFirstValue("Id");

                //if (createUserId == null)
                //{
                //    return Unauthorized(new ResponseAPI<WalletBM>
                //    {
                //        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                //        ExceptionMessage = "User unauthorized !"
                //    });
                //}
                var walletDTO = walletDTOList.FirstOrDefault();
                if ( walletDTO != null)
                {
                    ResponseAPI<List<WalletDTO>> response = _walletService.CreateDefaultWallets(walletDTO, new Guid(), pinCode);
                    return Ok(response);
                }
                else
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Update
        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] WalletDTO walletDTO)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                //string? updateUserId = HttpContext.User.FindFirstValue("Id");

                //if (updateUserId == null)
                //{
                //    return Unauthorized(new ResponseAPI<WalletBM>
                //    {
                //        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                //        ExceptionMessage = "User unauthorized !"
                //    });
                //}
                ResponseAPI<WalletDTO> response = _walletService.Update(walletDTO, Guid.NewGuid());
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Delete

        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<WalletBM>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                //string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                //if (deleteUserId == null)
                //{
                //    return Unauthorized(new ResponseAPI<WalletBM>
                //    {
                //        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                //        ExceptionMessage = "User unauthorized !"
                //    });
                //}
                ResponseAPI<WalletDTO> response = _walletService.Delete(id, Guid.NewGuid());
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

    }
}
