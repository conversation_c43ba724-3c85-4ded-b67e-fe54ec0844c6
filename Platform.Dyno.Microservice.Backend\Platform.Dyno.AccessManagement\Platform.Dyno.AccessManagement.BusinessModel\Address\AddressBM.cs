﻿using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.BusinessModel.Address
{
    public class AddressBM : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string? FullAddress { get; set; }
        #endregion
        #region Structure
        public UserBM? User { get; set; }
        public Guid? UserId { get; set; }
        public CompanyBM? Company { get; set; }
        public Guid? CompanyId { get; set; }
        #endregion
    }
}
