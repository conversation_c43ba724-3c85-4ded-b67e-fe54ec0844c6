﻿using MimeKit.Encodings;
using Platform.Dyno.AccessManagement.Business.IService.Transaction;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Transaction
{
    public class RefundTransactionService : IRefundTransactionService
    {
        private readonly IUserService _userService;
        private readonly Configuration _configuration;
        private readonly IHelper<RefundTransactionDTO> _helper;
        private readonly IHelper<TransactionDTO> _transactionHelper;
        private readonly IHelper<WalletDTO> _walletHelper;

        public RefundTransactionService(IUserService userService,
            Configuration configuration,
            IHelper<RefundTransactionDTO> helper,
            IHelper<TransactionDTO> transactionHelper,
            IHelper<WalletDTO> walletHelper
            )
        {
            _userService= userService;
            _configuration= configuration;
            _helper= helper;
            _transactionHelper= transactionHelper;
            _walletHelper= walletHelper;
        }

        public ResponseAPI<RefundTransactionDTO> DemandeCancledTransaction(Guid transactionId, Guid cashierId)
        {
            UserDTO? cashier = _userService.Get(cashierId).ObjectValue;
            if(cashier == null)
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                };
            }
            string Url = $"{_configuration.PaymentAddress}/Api/Transaction/Get/{transactionId}";
            ResponseAPI<TransactionDTO>? response = _transactionHelper.Get(Url);
            if (response != null && response.StatusCode == HttpStatusCode.OK)
            {
                if(response.ObjectValue != null)
                {
                    ResponseAPI<WalletDTO>? wallet = _walletHelper.Get($"{_configuration.PaymentAddress}/Api/Wallet/Get/{response.ObjectValue.ReceiverWalletId}");
                    if (wallet != null && wallet.StatusCode == HttpStatusCode.OK && wallet.ObjectValue?.AssignedToId == cashier.Id) 
                    {
                        ResponseAPI<TransactionDTO>? updateTransactionRefundStatus = _transactionHelper.Patch($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/UpdateRefundStatus/{transactionId}/{RefundStatus.InProgress}", null);
                        if(updateTransactionRefundStatus != null && updateTransactionRefundStatus.StatusCode == HttpStatusCode.OK) 
                        {
                            RefundTransactionDTO refundTransaction = new RefundTransactionDTO
                            {
                                Id = transactionId,
                                Amount = response.ObjectValue.Amount,
                                IsCredit = response.ObjectValue.IsCredit,
                                QrCodeId = response.ObjectValue.QrCodeId,
                                ReceiverWalletId = response.ObjectValue.SenderWalletId,
                                SenderWalletId = response.ObjectValue.ReceiverWalletId,
                                TransactionDate = response.ObjectValue.TransactionDate,
                                RefundStatus = Shared.Enum.RefundStatus.InProgress
                            };
                            ResponseAPI<RefundTransactionDTO>? responseRefund = _helper.Post($"{_configuration.PaymentAddress}/Api/RefundTransaction/Create", refundTransaction);

                            return responseRefund ?? new ResponseAPI<RefundTransactionDTO>
                            {
                                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                                ExceptionMessage = "An error occurred in payment Micro-Service"
                            };
                        }
                        
                    }

                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "User unauthorize !"
                    };
                }
                
            }

            return new ResponseAPI<RefundTransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = "An error occurred in payment Micro-Service"
            };
        }

        public ResponseAPI<List<CashierRefundTransactionsDTO>> GetRefundDemands(Guid companyId)
        {

            List<UserDTO>? cashiersList = _userService.Get(user => user.CompanyId == companyId
                                                                       && user.UserType == Shared.Enum.UserType.Cashier
                                                                       && user.Status == Shared.Enum.Status.Active).ObjectValue;
            if(cashiersList == null || cashiersList.Count == 0)
            {
                return new ResponseAPI<List<CashierRefundTransactionsDTO>>
                {
                    StatusCode = HttpStatusCode.NotFound
                };
            }

            List<CashierRefundTransactionsDTO> response = new List<CashierRefundTransactionsDTO>();
            foreach(var cashier in cashiersList)
            {
                CashierRefundTransactionsDTO cashierRefund = new CashierRefundTransactionsDTO
                {
                    UserId = cashier.Id,
                    FullName= cashier.FullName,
                    Email= cashier.Email,
                    Gender= cashier.Gender,
                    PhoneNumber= cashier.PhoneNumber,
                    UserName= cashier.UserName
                };
                string Url = $"{_configuration.PaymentAddress}/Api/RefundTransaction/GetUserRefundTransactions/{cashier.Id}";
                ResponseAPI<List<RefundTransactionDTO>>? refundTransactions = _helper.GetAll(Url);
                if(refundTransactions != null && refundTransactions.ObjectValue != null)
                {
                    cashierRefund.Transactions = refundTransactions.ObjectValue;    
                }

                response.Add(cashierRefund);
            }

            return new ResponseAPI<List<CashierRefundTransactionsDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = response
            };
        }

        public ResponseAPI<RefundTransactionDTO> RejectCancledTransaction(Guid transactionId, Guid shopOwnerId)
        {
            UserDTO? shopOwner = _userService.Get(shopOwnerId).ObjectValue;
            if(shopOwner == null)
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                };
            }
            ResponseAPI<TransactionDTO>? updateTransactionRefundStatus = _transactionHelper.Patch($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/UpdateRefundStatus/{transactionId}/{RefundStatus.Rejected}", null);
            if(updateTransactionRefundStatus != null && updateTransactionRefundStatus.StatusCode== System.Net.HttpStatusCode.OK)
            {
                string Url = $"{_configuration.PaymentAddress}/Api/RefundTransaction/UpdateRefundStatus/{transactionId}/{RefundStatus.Rejected}";
                ResponseAPI<RefundTransactionDTO>? response = _helper.Get(Url);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                }
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = response.ObjectValue
                };
            }

            return new ResponseAPI<RefundTransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = "An error occurred in payment Micro-Service"
            };


        }

        public ResponseAPI<RefundTransactionDTO> ValidateCancledTransaction(Guid transactionId, Guid shopOwnerId)
        {
            UserDTO? shopOwner = _userService.Get(shopOwnerId).ObjectValue;
            if (shopOwner == null)
            {
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                };
            }
            ResponseAPI<TransactionDTO>? updateTransactionRefundStatus = _transactionHelper.Patch($"{_configuration.PaymentAddress}/Api/TransactionBlockchain/UpdateRefundStatus/{transactionId}/{RefundStatus.Validated}", null);
            if(updateTransactionRefundStatus != null && updateTransactionRefundStatus.StatusCode == HttpStatusCode.OK)
            {

                string Url = $"{_configuration.PaymentAddress}/Api/RefundTransaction/UpdateRefundStatus/{transactionId}/{RefundStatus.Validated}";
                ResponseAPI<RefundTransactionDTO>? updateRefundStatus = _helper.Get(Url);
                if (updateRefundStatus == null || updateRefundStatus.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                }
                else if (updateRefundStatus.StatusCode != HttpStatusCode.OK)
                {
                    return updateRefundStatus;
                }

                //Get shop Owner Wallet
                ResponseAPI<List<WalletDTO>>? responseWallet = _walletHelper.GetAll(
                    $"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets"
                    , new Dictionary<string, string> { { "userId", shopOwner.CompanyId.ToString() ?? string.Empty } });
                if (responseWallet?.StatusCode != HttpStatusCode.OK || responseWallet.ObjectValue == null || responseWallet.ObjectValue.Count <= 0)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = responseWallet?.ExceptionMessage
                    };
                }

                ResponseAPI<TransactionDTO>? transaction = _transactionHelper.Post(
                    $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/Create",
                    new TransactionDTO
                    {
                        SenderWalletId = responseWallet.ObjectValue?.FirstOrDefault()?.Id ?? Guid.Empty,
                        ReceiverWalletId = updateRefundStatus.ObjectValue?.ReceiverWalletId ?? Guid.Empty,
                        Amount = updateRefundStatus.ObjectValue?.Amount ?? 0,
                        QrCodeId = updateRefundStatus.ObjectValue?.QrCodeId ?? Guid.Empty,
                        Status = TransactionStatus.succeeded,
                        TransactionDate = DateTime.UtcNow,
                        IsCredit = updateRefundStatus.ObjectValue?.IsCredit ?? true
                    },
                    new Dictionary<string, string> { { "pinCode", shopOwner.CompanyId.ToString() } });

                if (transaction == null || transaction.StatusCode == HttpStatusCode.InternalServerError)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                }
                else if (transaction.StatusCode != HttpStatusCode.Created)
                {
                    return new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = transaction.ExceptionMessage
                    };
                }
                return new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = updateRefundStatus.ObjectValue
                };

            }

            return new ResponseAPI<RefundTransactionDTO>
            {
                StatusCode = System.Net.HttpStatusCode.InternalServerError,
                ExceptionMessage = "An error occurred in payment Micro-Service"
            };
        }
    }
}
