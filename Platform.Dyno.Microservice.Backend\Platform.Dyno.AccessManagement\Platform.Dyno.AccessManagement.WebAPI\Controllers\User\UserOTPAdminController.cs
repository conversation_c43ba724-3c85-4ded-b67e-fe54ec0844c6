﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.EmailConfirmation;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserOTPAdminController : ControllerBase
    {
        private readonly IUserOTPService _userOTPService;
        private readonly ILogger<UserOTPAdminController> _logger;

        public UserOTPAdminController(IUserOTPService userOTPService,
            ILogger<UserOTPAdminController> logger)
        {
            _userOTPService = userOTPService;
            _logger = logger;
        }

        [Route("GetOTPCode/{email}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetOTPCode(string email)
        {
            try
            {
                ResponseAPI<UserOTPDTO> userOTPs = _userOTPService.Create(email);
                return Ok(userOTPs);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetOTPCode)}");
                ResponseAPI<List<UserOTPDTO>> response = new ResponseAPI<List<UserOTPDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("VerifierDevice")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult VerifierDevice([FromBody] AdminOtpDTO userOtp)
        {
            try
            {
                ResponseAPI<UserOTPDTO> userOTPs = _userOTPService.VerifierDevice(userOtp);
                return Ok(userOTPs);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetOTPCode)}");
                ResponseAPI<List<UserOTPDTO>> response = new ResponseAPI<List<UserOTPDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("VerifierEmail")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task <IActionResult> VerifierEmail(EmailConfirmationDTO emailConfirmationDTO)
        {
            try
           {

                ResponseAPI<EmailConfirmationDTO> emailConfirmation = await _userOTPService.EmailConfirmation(emailConfirmationDTO);
                return Ok(emailConfirmation);
            }catch(Exception ex) {

                _logger.LogError(ex, $"Something went wrong in the {nameof(VerifierEmail)}");
                ResponseAPI<List<EmailConfirmationDTO>> response = new ResponseAPI<List<EmailConfirmationDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
    }
}
