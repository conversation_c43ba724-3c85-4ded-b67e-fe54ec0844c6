﻿using System;
using System.Drawing;
using System.Text.Json;
using QRCoder;

namespace Platform.Dyno.Shared.SharedClass.QrCode
{
    public static class QrCodeGenerator<T>
    {
        public static string GenerateQrCode(T data)
        {
            var qrCodeGenerator = new QRCodeGenerator();
            // Convert the generic data to a string
            string dataString = JsonSerializer.Serialize(data);

            // Create QR code data with specific settings
            var qrCodeData = qrCodeGenerator.CreateQrCode(dataString, QRCodeGenerator.ECCLevel.Q);

            // Create QR code using the customized data
            BitmapByteQRCode qrCode = new(qrCodeData);

            // Get the bitmap representation of the QR code
            var qrCodeImage = qrCode.GetGraphic(2,"#000000", "#FFFFFF");
            return Convert.ToBase64String(qrCodeImage);
            
        }

    }




    
}