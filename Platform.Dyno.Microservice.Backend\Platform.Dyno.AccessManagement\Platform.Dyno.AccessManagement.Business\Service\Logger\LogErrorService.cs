﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.BusinessModel.Logger;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Logger;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Logger;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Logger
{
    public class LogErrorService : ILogErrorService
    {
        private readonly IMapper _mapper;
        private readonly IUnitOfWork<LogErrorEntity> _logErrorRepository;
        private readonly IRedisCacheService _cache;
        private readonly string _logErrorCacheKey = RedisCacheKey.LogErrorCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("log error");

        public LogErrorService(IUnitOfWork<LogErrorEntity> logErrorRepository,
            IMapper mapper,
            IRedisCacheService cache)
        {
            _logErrorRepository = logErrorRepository;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<LogErrorDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if(userType != UserType.SuperAdmin && userType != null)
            {
                return new ResponseAPI<List<LogErrorDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }
            ResponseAPI<List<LogErrorDTO>> response = new ResponseAPI<List<LogErrorDTO>>();
            var errorsDTO = _cache.GetData<List<LogErrorDTO>>(_logErrorCacheKey);
            try
            {
                if (errorsDTO == null || errorsDTO.Count() == 0)
                {
                    List<LogErrorEntity> errorsEntity = (List<LogErrorEntity>)_logErrorRepository.Repository.GetAll(orderBy: o => o.OrderBy(o => o.CreationDate));

                    var errorsBM = _mapper.Map<List<LogErrorBM>>(errorsEntity);
                    errorsDTO = _mapper.Map<List<LogErrorDTO>>(errorsBM);

                    _cache.SetData(_logErrorCacheKey , response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
                response.StatusCode = errorsDTO == null || errorsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = errorsDTO?.OrderByDescending(c => c.CreationDate).ToList();
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }

        public ResponseAPI<PagedList<LogErrorDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<LogErrorDTO>> logErrorsDTO = GetAll(companyId, userType);
            PagedList<LogErrorDTO>? pagedList = null;
            if (logErrorsDTO.ObjectValue != null)
            {
                pagedList = PagedList<LogErrorDTO>.ToGenericPagedList(logErrorsDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<LogErrorDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<LogErrorDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            throw new NotImplementedException();
        }

        public ResponseAPI<LogErrorDTO> Get(Guid id)
        {
            LogErrorDTO? logErrorDTO = GetAll().ObjectValue?.Where(log => log.Id == id).FirstOrDefault();
            return new ResponseAPI<LogErrorDTO>
            {
                StatusCode = logErrorDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = logErrorDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = logErrorDTO
            };
        }

        public ResponseAPI<List<LogErrorDTO>> Get(Func<LogErrorDTO, bool> expression)
        {
            List<LogErrorDTO>? logErrorDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<LogErrorDTO>>
            {
                StatusCode = logErrorDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = logErrorDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = logErrorDTO
            };
        }
        #endregion

        #region Create 
        public ResponseAPI<LogErrorDTO> Create(LogErrorDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            LogErrorBM logError = _mapper.Map<LogErrorBM>(dtoObject);

            #region changed data
            logError.Id = Guid.NewGuid();
            logError.CreationDate = DateTime.UtcNow;
            #endregion

            LogErrorEntity logErrorEntity = _mapper.Map<LogErrorEntity>(logError);
            _logErrorRepository.Repository.Insert(logErrorEntity);
            _logErrorRepository.Save();

            dtoObject.Id = logErrorEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_logErrorCacheKey);
            }

            return new ResponseAPI<LogErrorDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }
        #endregion

        #region Update
        public ResponseAPI<LogErrorDTO> Update(LogErrorDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region Delete
        public ResponseAPI<LogErrorDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            throw new NotImplementedException();
        }
        #endregion

    }
}
