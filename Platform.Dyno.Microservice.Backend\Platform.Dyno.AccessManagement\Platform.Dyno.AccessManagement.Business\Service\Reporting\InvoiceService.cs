﻿using Amazon.Runtime;
using Amazon.Runtime.Internal.Util;
using Amazon.S3.Model;
using Amazon.S3;
using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.BusinessModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;
using Amazon;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using QuestPDF.Infrastructure;
using QuestPDF;
using QuestPDF.Fluent;
using Platform.Dyno.Shared.Image;

namespace Platform.Dyno.AccessManagement.Business.Service.Reporting
{
    public class InvoiceService : IInvoiceService
    {
        private readonly IUnitOfWork<DocumentsEntity> _invoiceRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<InvoiceService> _logger;
        private readonly IRedisCacheService _cache;
        private readonly Configuration _configuration;
        private readonly ICompanyService _companyService;
        private readonly string _invoiceCacheKey = RedisCacheKey.InvoiceCacheKey;
        private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("invoice");

        public InvoiceService(IUnitOfWork<DocumentsEntity> invoiceRepository,
            IMapper mapper,
            ILogger<InvoiceService> logger,
            Configuration configuration,
            IRedisCacheService cache,
            ICompanyService companyService) 
        {
            _invoiceRepository = invoiceRepository;
            _mapper = mapper;
            _logger = logger;
            _cache = cache;
            _configuration= configuration;
            _companyService = companyService;
        }

        #region Get
        public ResponseAPI<List<DocumentsDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            if(userType == UserType.Client || userType == UserType.Cashier)
            {
                return new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }
            List<DocumentsDTO>? invoicesDTO = _cache.GetData<List<DocumentsDTO>>(_invoiceCacheKey);
            if (invoicesDTO == null || invoicesDTO.Count() == 0)
            {
                List<DocumentsEntity>  invoiceEntities = (List<DocumentsEntity>)_invoiceRepository.Repository.GetAll().ToList();
                List<DocumentsBM> invoicesBM = _mapper.Map<List<DocumentsBM>>(invoiceEntities);
                invoicesDTO = _mapper.Map<List<DocumentsDTO>>(invoicesBM);

                _cache.SetData(_invoiceCacheKey, invoicesDTO, DateTimeOffset.UtcNow.AddDays(1));
            }

            if(userType != UserType.SuperAdmin)
            {
          //      invoicesDTO = invoicesDTO.Where(invoice => invoice.CompanyId == companyId).ToList();
            }
            
            return new ResponseAPI<List<DocumentsDTO>>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = invoicesDTO
            };
        }

        public ResponseAPI<PagedList<DocumentsDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<DocumentsDTO>> invoicesDTO = GetAll(companyId, userType);
            PagedList<DocumentsDTO>? pagedList = null;
            if (invoicesDTO.ObjectValue != null)
            {
                pagedList = PagedList<DocumentsDTO>.ToGenericPagedList(invoicesDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<DocumentsDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<DocumentsDTO>> invoicesDTO = GetAll(companyId, userType);
            PagedList<DocumentsDTO>? pagedList = null;
            if (invoicesDTO.ObjectValue != null)
            {
                pagedList = PagedList<DocumentsDTO>.ToGenericPagedList(invoicesDTO.ObjectValue.Where(invoice => invoice.Status == Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<DocumentsDTO> Get(Guid id)
        {
            DocumentsDTO? invoiceDTO = GetAll().ObjectValue?.Where(invoice => invoice.Id == id).FirstOrDefault();
            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = invoiceDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = invoiceDTO == null ? exceptionMessages.ReadError : null,
                ObjectValue = invoiceDTO
            };
        }

        public ResponseAPI<List<DocumentsDTO>> Get(Func<DocumentsDTO, bool> expression)
        {
            List<DocumentsDTO>? invoicesDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<DocumentsDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = invoicesDTO
            };
        }

        #endregion

        #region Create
        public ResponseAPI<DocumentsDTO> Create(DocumentsDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            DocumentsBM document = _mapper.Map<DocumentsBM>(dtoObject);

            #region changed data
            document.Id = Guid.NewGuid();
            RefDataService<DocumentsBM>.CreateRefData(document, document.Id);
            #endregion

            DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
            _invoiceRepository.Repository.Insert(documentEntity);
            _invoiceRepository.Save();

            dtoObject.Id = documentEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_invoiceCacheKey);
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public async Task<ResponseAPI<DocumentsDTO>> CreatePDF(string code,
            ProductType productType, 
            double dynoAmount, 
            double netAmount, 
            double VATAmount, 
            double totalAmount, 
            PaymentMethod paymentMethod,
            Guid companyCreatorId,
            Guid companyReceiverId,
            DocumentType documentType)
        {
            try
            {
                CompanyDTO? companyCreator = _companyService.Get(companyCreatorId).ObjectValue;
                
                if(companyCreator == null)
                {
                    return new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = "Company of creator Invoice unfound! "
                    };
                }

                CompanyDTO? companyReceiver = _companyService.Get(companyReceiverId).ObjectValue;

                if(companyReceiver == null)
                {
                    return new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = "Company of receiver Invoice unfound! "
                    };
                }
                string fileName ;
                string filePath ;
                if (documentType == DocumentType.Invoice)
                {
                    fileName = $"Invoice_{code}_{DateTime.UtcNow.ToString("yyyy_MM_dd_HH_mm_ss")}";
                    filePath = $"Invoice/{fileName}.pdf";
                }
                else if (documentType==DocumentType.SalesOrder) {
                    fileName = $"BC_{code}_{DateTime.UtcNow.ToString("yyyy_MM_dd_HH_mm_ss")}";
                    filePath = $"Invoice/{fileName}.pdf";
                }
                else
                {
                    return new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = "Wrong Document type "
                    };
                }
                DocumentsDTO document = new DocumentsDTO
                {
                    Name = fileName,
                    Path = $"PDFs/{filePath}",
                    CompanyId = companyReceiverId,
                    Type = documentType
                };
                ResponseAPI<DocumentsDTO> invoice = Create(document, companyCreatorId);
                if (invoice.StatusCode != HttpStatusCode.Created)
                {
                    return new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = invoice.StatusCode,
                        ExceptionMessage = invoice.ExceptionMessage,
                    };
                }           
                Tuple<decimal, decimal> results = DiviserNombreDouble((decimal)totalAmount + 1);

                string item1Letter = ConvertirNombreEntiereEnLettres((int)results.Item1);
                string item2Letter = ConvertirNombreEntiereEnLettres((int)results.Item2);
                Settings.License = LicenseType.Community;
                var model = new InvoiceModel
                {
                    InvoiceNumber = code,
                    IssueDate = DateTime.Now,
                    DueDate = DateTime.Now + TimeSpan.FromDays(14),
                    Title = documentType == DocumentType.Invoice ? "Facture" : "Ordre de vente",
                    
                    SellerAddress = new CompanyDetails
                    {
                        CompanyName = "Dyno & Motiva Systems",
                        RNE = "1803838P",
                        VAT = "000 M A 1803838/P",
                        Address = "033 Rue Tanit, Menzah 4 1004, Tunis Tunisia",
                        Phone = "+216-93-762-002",
                        Email = "<EMAIL>"
                    },
                    CustomerAddress = new CompanyDetails
                    {
                        CompanyName = companyReceiver.Name,
                        RNE = companyReceiver.RNECode,
                        VAT = companyReceiver.TaxCode,
                        Address = companyReceiver.Addresses?.FirstOrDefault()?.FullAddress,
                        Email= companyReceiver.Email,
                        Phone = companyReceiver.CountryCode +'-'+companyReceiver.PhoneNumber
                    },
                    TotalPriceText = $"{item1Letter + "  dinars " + item2Letter + " millimes"}",
                    TotalPrice = totalAmount,
                    paiementDetails = new PaiementDetails
                    {
                        Bank = "BIAT",
                        BIC = "BIATTNTT",
                        IBAN = "TN59 08 036 ***************"
                    },
                    Items = new List<OrderItem>
                    {
                        new OrderItem
                        {
                            ProductType = productType.ToString(),
                            VAT = VATAmount,
                            NET = netAmount,
                            Quantity = 1,
                            Total = totalAmount
                        }
                    }
                };
                byte[] imageBytes = await ImageHelper.DownloadImage(_configuration.LogoURL);
                string base64Image = Convert.ToBase64String(imageBytes);
                var documentInvoice = new InvoiceDocument(model, imageBytes);
                byte[] pdfBytes = documentInvoice.GeneratePdf();
                await ImageHelper.UploadPDFInAWSAsync(pdfBytes, filePath);
                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = HttpStatusCode.Created,
                    ObjectValue = invoice.ObjectValue

                };

            }
            catch(Exception ex)
            {
                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.Message
                };
            }
            
        }
        #endregion

        #region Update
        public ResponseAPI<DocumentsDTO> Update(DocumentsDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (GetAll().ObjectValue?.Where(dto => dto.Id == dtoObject.Id).Count() > 0)
            {

                DocumentsBM document = _mapper.Map<DocumentsBM>(dtoObject);

                #region RefData
                RefDataService<DocumentsBM>.UpdateRefData(document, document.Id);
                #endregion

                DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
                _invoiceRepository.Repository.Update(documentEntity);
                _invoiceRepository.Save();

                dtoObject.Id = documentEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_invoiceCacheKey);
                }

                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.UpdateError
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<DocumentsDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            DocumentsDTO? documentDto = Get(id).ObjectValue;
            if (documentDto != null)
            {
               DocumentsBM document = _mapper.Map<DocumentsBM>(documentDto);

                #region RefData
                RefDataService<DocumentsBM>.DeleteRefData(document, document.Id);
                #endregion

                DocumentsEntity documentEntity = _mapper.Map<DocumentsEntity>(document);
                _invoiceRepository.Repository.Update(documentEntity);
                _invoiceRepository.Save();

                documentDto.Id = documentEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_invoiceCacheKey);
                }

                return new ResponseAPI<DocumentsDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = documentDto
                };
            }

            return new ResponseAPI<DocumentsDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = exceptionMessages.DeleteError
            };
        }


        #endregion

        #region Convertir Montant to letter

        public static Tuple<decimal, decimal> DiviserNombreDouble(decimal nombre)
        {
            decimal partieEntiere = (decimal)Math.Floor(nombre);
            decimal partieDecimal = (int)((nombre - partieEntiere) * 1000);
            return Tuple.Create(partieEntiere, partieDecimal);
        }

        public static string ConvertirNombreEntiereEnLettres(int nombre)
        {

            string[] unite = { "", "un", "deux", "trois", "quatre", "cinq", "six", "sept", "huit", "neuf", "dix", "onze", "douze", "treize", "quatorze", "quinze", "seize", "dix-sept", "dix-huit", "dix-neuf" };
            string[] dizaineArray = { "", "dix", "vingt", "trente", "quarante", "cinquante", "soixante", "soixante", "quatre-vingt", "quatre-vingt" };

            int mille = nombre / 1000;
            int centaines = (nombre % 1000) / 100;
            int dizainesEtUnites = nombre % 100;

            string resultat = "";

            if(mille > 0)
            {
                if(mille < 10)
                {
                    if (mille == 1)
                    {
                        resultat += " mille ";
                    }
                    else
                    {
                        resultat += unite[mille] + " milles ";
                    }
                }else if(mille < 20)
                {
                    resultat += unite[mille] + " milles ";
                }else
                {
                    int centMille = mille / 100;
                    int dizaineMille = (mille / 10) % 10;
                    int uniteMille = mille % 10;
                    if(centMille > 0)
                    {
                        if (centMille == 1)
                        {
                            resultat += " cent ";
                        }
                        else
                        {
                            resultat += unite[centMille] + " cents ";
                        }
                    }
                    
                    if (uniteMille > 0)
                    {
                        resultat += dizaineArray[dizaineMille] + "-" + unite[uniteMille]  +" milles ";
                    }else
                    {
                        resultat += dizaineArray[dizaineMille] + " milles ";
                    }
                    
                }
                
                
            }

            if (centaines > 0)
            {
                if (centaines == 1)
                {
                    resultat += " cent ";
                }
                else
                {
                    resultat += unite[centaines] + " cents ";
                }               
            }

            if (dizainesEtUnites > 0)
            {

                if (dizainesEtUnites < 10)
                {
                    resultat += unite[dizainesEtUnites];
                }
                else if (dizainesEtUnites < 20)
                {
                    resultat += unite[dizainesEtUnites - 10] + " dix";
                }
                else
                {
                    int dizaine = dizainesEtUnites / 10;
                    
                    int uniteRestante = dizainesEtUnites % 10;
                    if (dizaine == 7 || dizaine == 9)
                        uniteRestante += 10;
                    resultat += dizaineArray[dizaine];

                    if (uniteRestante > 0)
                    {
                        resultat += "-" + unite[uniteRestante];
                    }
                }
            }

            return resultat ;
        }

        #endregion

    }
}
