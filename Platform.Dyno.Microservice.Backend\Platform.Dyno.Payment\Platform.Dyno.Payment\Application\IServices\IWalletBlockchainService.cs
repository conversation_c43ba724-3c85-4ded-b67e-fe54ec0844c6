﻿using Domain.Enums.Query;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface IWalletBlockchainService
    {
        ResponseAPI<List<WalletDTO>> GetAll();
        ResponseAPI<PagedList<WalletDTO>> GetAll(PagedParameters pagedParameters);
        ResponseAPI<WalletDTO> Get(Guid id);
        ResponseAPI<List<WalletDTO>> GetUserWallets(Guid userId);
        ResponseAPI<WalletDTO> GetUserWalletByType(Guid userId, WalletType walletType);

        ResponseAPI<WalletDTO> Create(WalletDTO walletDTO, Guid CreateUserId, string pinCode);
        ResponseAPI<List<WalletDTO>> CreateDefaultWallets(WalletDTO walletDTO, Guid CreateUserId, string pinCode);
        ResponseAPI<WalletDTO> Update(WalletDTO walletDTO, Guid UpdateUserId);
        ResponseAPI<WalletDTO> Delete(Guid id, Guid DeleteUserId);
    }
}
