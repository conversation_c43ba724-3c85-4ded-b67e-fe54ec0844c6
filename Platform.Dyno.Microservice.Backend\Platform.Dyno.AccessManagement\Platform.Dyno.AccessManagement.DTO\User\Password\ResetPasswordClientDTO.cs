﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.User.Password
{
    public class ResetPasswordClientDTO
    {
        [Required(ErrorMessage = "Country code is required")]
        public string CountryCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string NewPassword { get; set; } = string.Empty;
    }
}
