﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class company_categoryType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("109bafac-2fe4-40e7-87fe-590c987dc848"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1319a316-0442-4a9c-9106-6a18faa58dc5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1a8360a6-2433-4c1e-80f0-8a7613ef6c93"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1a97ef7d-13e5-40db-a7a2-64254875b4e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("20498995-70c3-467a-9978-f937f9091269"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("31877d89-32e6-4a31-8d2d-322ad5dcb8dc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3f2aa28d-d2f7-40ef-bdc6-c3e61a4241c1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50ce0cd0-1cb0-42ce-a6cb-b5f69e4c63a1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5646ae84-55c7-489e-a16d-b175b006e495"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5c2fde05-aa88-41f7-ba7d-9d511b3e8557"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("68a6aa59-fe68-4267-b431-1706c194edac"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("69ea35e9-bc06-487c-9f68-6444a75430c0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("71af6b29-ca7d-4856-9c9f-5227bc8d2342"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("76f874d4-44fd-4e31-ab6e-64f1792d7759"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8379e6bf-ed60-4d5e-8bab-68020ee3764f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9319e86f-3a58-4a9e-98af-073bd780a4f1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a75c0dfe-03de-48ae-b855-e5b2459c0a6f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b801c653-b091-4d37-b08f-43ee5395b813"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b9b46627-9b78-473f-8a69-eb1899255408"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d1fa49ed-76ce-44b2-a483-d6fa374e4766"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e6a33497-1a82-4c6e-b926-f03348b16bc5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e7a96040-1ed4-479a-befc-ade376979634"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e866cb5f-b3fd-494a-87c3-2bc6e0ac52a6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e99490ce-86f9-4dde-81b9-061203d1fe89"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("49694334-e359-499d-ba36-a659953650c9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c0662728-170f-4608-8790-a75002485c5e"));

            migrationBuilder.AddColumn<int>(
                name: "CategoryType",
                table: "Company",
                type: "integer",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Company",
                keyColumn: "Id",
                keyValue: new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                column: "CategoryType",
                value: null);

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0acbfbe8-3349-482b-9bfc-4824250a0816", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5106) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0e7a606d-8454-413e-9868-e5bc3eb4f4f3", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5083) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "64543438-a9da-444a-b7db-26ea225cc2a4", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5091) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "8f0319ae-5e56-4005-b90d-3e002266a1e7", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5120) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "312096dc-9485-48db-82a9-8f18c56f720c", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5101) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f0af4f7c-e9a5-4364-ad92-b71a37293160", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5096) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "a90c297a-f5eb-4a40-95ce-f7e870d066df", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), "AQAAAAEAACcQAAAAEJ0+oX6N25DaHXAF43NrLXkDoCQOrjJww9ZiMa6vObnLktq/9yCa12SfPC+8f+IVAg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7512033a-3ffb-4156-9c22-014b0b035348"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("80cb2a33-a443-492b-b005-eb51b674e504"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9c28fb26-9536-467e-8667-69716031350a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7512033a-3ffb-4156-9c22-014b0b035348"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("80cb2a33-a443-492b-b005-eb51b674e504"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9c28fb26-9536-467e-8667-69716031350a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"));

            migrationBuilder.DropColumn(
                name: "CategoryType",
                table: "Company");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c0662728-170f-4608-8790-a75002485c5e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "82405186-493a-4588-b063-dcb990d38bf5", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8023) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0a833934-4eb2-4ee6-ac8b-60c5b9cc80d2", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8001) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "e35130ca-60b5-409e-87fb-b3b4d4093406", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8011) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "efacfc0c-864a-496e-8dbe-47d803eaa2f9", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8027) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "c55dbbd5-2ada-4a41-8179-425e5d2e749c", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8020) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a136db30-3f8e-4a22-85eb-fe7175a41cd2", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8016) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "8b451254-5d0e-4d90-b5fe-d207f8cf8694", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8292), new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8292), "AQAAAAEAACcQAAAAEIha7wm1O0eg6VE2ODmXNWKHkpkYoTqQjUcbZrHR3nLyOVLMKvYHFTaJkrFZn+1f/g==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("c0662728-170f-4608-8790-a75002485c5e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("49694334-e359-499d-ba36-a659953650c9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("c0662728-170f-4608-8790-a75002485c5e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("109bafac-2fe4-40e7-87fe-590c987dc848"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1319a316-0442-4a9c-9106-6a18faa58dc5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1a8360a6-2433-4c1e-80f0-8a7613ef6c93"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1a97ef7d-13e5-40db-a7a2-64254875b4e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("20498995-70c3-467a-9978-f937f9091269"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("31877d89-32e6-4a31-8d2d-322ad5dcb8dc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3f2aa28d-d2f7-40ef-bdc6-c3e61a4241c1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("50ce0cd0-1cb0-42ce-a6cb-b5f69e4c63a1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5646ae84-55c7-489e-a16d-b175b006e495"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5c2fde05-aa88-41f7-ba7d-9d511b3e8557"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("68a6aa59-fe68-4267-b431-1706c194edac"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("69ea35e9-bc06-487c-9f68-6444a75430c0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("71af6b29-ca7d-4856-9c9f-5227bc8d2342"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("76f874d4-44fd-4e31-ab6e-64f1792d7759"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8379e6bf-ed60-4d5e-8bab-68020ee3764f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9319e86f-3a58-4a9e-98af-073bd780a4f1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("a75c0dfe-03de-48ae-b855-e5b2459c0a6f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b801c653-b091-4d37-b08f-43ee5395b813"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b9b46627-9b78-473f-8a69-eb1899255408"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d1fa49ed-76ce-44b2-a483-d6fa374e4766"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e6a33497-1a82-4c6e-b926-f03348b16bc5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e7a96040-1ed4-479a-befc-ade376979634"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e866cb5f-b3fd-494a-87c3-2bc6e0ac52a6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e99490ce-86f9-4dde-81b9-061203d1fe89"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }
    }
}
