# 🧪 TEST ARCHITECTURE MOBILE CORRECTE - ACCESSMANAGEMENT UNIQUEMENT
# Platform Dyno - Validation de l'architecture mobile

Write-Host "🏗️ TEST ARCHITECTURE MOBILE PLATFORM DYNO" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Configuration CORRECTE - Point d'entrée unique
$baseUrl = "http://localhost:7274/api"

# Ignorer les erreurs SSL pour les tests locaux
[System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}

Write-Host "`n🎯 ARCHITECTURE TESTÉE:" -ForegroundColor Cyan
Write-Host "📱 Flutter App → AccessManagement (7274) → Services internes" -ForegroundColor White
Write-Host "✅ Point d'entrée unique: AccessManagement" -ForegroundColor Green
Write-Host "❌ PAS d'accès direct aux services Payment/Notification" -ForegroundColor Red

Write-Host "`n🔐 ÉTAPE 1: AUTHENTIFICATION VIA ACCESSMANAGEMENT" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Données de test
$loginData = @{
    phoneNumber = "+33123456789"
    password = "TestPassword123!"
    countryCode = "+33"
} | ConvertTo-Json

$headers = @{
    'Content-Type' = 'application/json'
    'acceptLanguage' = 'en'
    'acceptTermsAndConditions' = 'true'
}

try {
    Write-Host "📞 Connexion via: $baseUrl/AuthClient/login" -ForegroundColor Yellow
    
    $authResponse = Invoke-RestMethod -Uri "$baseUrl/AuthClient/login" -Method POST -Body $loginData -Headers $headers
    
    if ($authResponse.statusCode -eq 200) {
        $token = $authResponse.objectValue.token
        $userProfile = $authResponse.objectValue.userProfile
        
        Write-Host "✅ AUTHENTIFICATION RÉUSSIE!" -ForegroundColor Green
        Write-Host "   Utilisateur: $($userProfile.fullName)" -ForegroundColor White
        Write-Host "   Email: $($userProfile.email)" -ForegroundColor White
        Write-Host "   Token: $($token.Substring(0, 30))..." -ForegroundColor White
        
        # Headers avec token pour les requêtes suivantes
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $token"
            'acceptLanguage' = 'en'
        }
        
        Write-Host "`n💰 ÉTAPE 2: WALLETS VIA ACCESSMANAGEMENT" -ForegroundColor Cyan
        Write-Host "=======================================" -ForegroundColor Cyan
        
        Write-Host "📞 Requête: $baseUrl/Wallet/GetMyWallets" -ForegroundColor Yellow
        Write-Host "🔄 AccessManagement → Payment Service (interne)" -ForegroundColor Gray
        
        $walletsResponse = Invoke-RestMethod -Uri "$baseUrl/Wallet/GetMyWallets" -Method GET -Headers $authHeaders
        
        if ($walletsResponse.statusCode -eq 200) {
            $wallets = $walletsResponse.objectValue
            Write-Host "✅ WALLETS RÉCUPÉRÉS: $($wallets.Count) wallet(s)" -ForegroundColor Green
            
            foreach ($wallet in $wallets) {
                Write-Host "   📱 $($wallet.assignedToName): $($wallet.balance)€" -ForegroundColor White
            }
            
            Write-Host "`n💰 ÉTAPE 3: SOLDE TOTAL VIA ACCESSMANAGEMENT" -ForegroundColor Cyan
            Write-Host "============================================" -ForegroundColor Cyan
            
            Write-Host "📞 Requête: $baseUrl/Wallet/GetTotalBalance" -ForegroundColor Yellow
            
            $balanceResponse = Invoke-RestMethod -Uri "$baseUrl/Wallet/GetTotalBalance" -Method GET -Headers $authHeaders
            
            if ($balanceResponse.statusCode -eq 200) {
                $totalBalance = $balanceResponse.objectValue
                Write-Host "✅ SOLDE TOTAL: $totalBalance€" -ForegroundColor Green
            }
            
            Write-Host "`n🔄 ÉTAPE 4: TRANSACTION VIA ACCESSMANAGEMENT" -ForegroundColor Cyan
            Write-Host "============================================" -ForegroundColor Cyan
            
            if ($wallets.Count -ge 2) {
                $senderWallet = $wallets[0]
                $receiverWallet = $wallets[1]
                
                $transactionData = @{
                    senderWalletId = $senderWallet.id
                    receiverWalletId = $receiverWallet.id
                    amount = 5.00
                    description = "Test transaction via AccessManagement"
                    transactionDate = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
                } | ConvertTo-Json
                
                Write-Host "📞 Requête: $baseUrl/Transaction/Create?pinCode=1234" -ForegroundColor Yellow
                Write-Host "🔄 AccessManagement → Payment Service (interne)" -ForegroundColor Gray
                Write-Host "📤 De: $($senderWallet.assignedToName)" -ForegroundColor White
                Write-Host "📥 Vers: $($receiverWallet.assignedToName)" -ForegroundColor White
                Write-Host "💵 Montant: 5.00€" -ForegroundColor White
                
                try {
                    $transactionResponse = Invoke-RestMethod -Uri "$baseUrl/Transaction/Create?pinCode=1234" -Method POST -Body $transactionData -Headers $authHeaders
                    
                    if ($transactionResponse.statusCode -eq 200) {
                        $transaction = $transactionResponse.objectValue
                        Write-Host "✅ TRANSACTION CRÉÉE!" -ForegroundColor Green
                        Write-Host "   ID: $($transaction.id)" -ForegroundColor White
                        Write-Host "   Statut: $($transaction.status)" -ForegroundColor White
                        Write-Host "   Montant: $($transaction.amount)€" -ForegroundColor White
                    } else {
                        Write-Host "❌ ERREUR TRANSACTION: $($transactionResponse.exceptionMessage)" -ForegroundColor Red
                    }
                    
                } catch {
                    Write-Host "❌ ERREUR TRANSACTION: $($_.Exception.Message)" -ForegroundColor Red
                }
                
            } else {
                Write-Host "⚠️  Pas assez de wallets pour tester une transaction" -ForegroundColor Yellow
            }
            
            Write-Host "`n📊 ÉTAPE 5: HISTORIQUE VIA ACCESSMANAGEMENT" -ForegroundColor Cyan
            Write-Host "===========================================" -ForegroundColor Cyan
            
            Write-Host "📞 Requête: $baseUrl/Transaction/GetAllPaged" -ForegroundColor Yellow
            
            $historyResponse = Invoke-RestMethod -Uri "$baseUrl/Transaction/GetAllPaged?pageSize=5&pageNumber=1&order=DESC" -Method GET -Headers $authHeaders
            
            if ($historyResponse.statusCode -eq 200) {
                $transactions = $historyResponse.objectValue
                Write-Host "✅ HISTORIQUE RÉCUPÉRÉ: $($transactions.Count) transaction(s)" -ForegroundColor Green
                
                foreach ($tx in $transactions) {
                    Write-Host "   🔄 $($tx.amount)€ - $($tx.description)" -ForegroundColor White
                }
            }
            
        } else {
            Write-Host "❌ ERREUR WALLETS: $($walletsResponse.exceptionMessage)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ ERREUR AUTHENTIFICATION: $($authResponse.exceptionMessage)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ ERREUR DE CONNEXION: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Vérifiez qu'AccessManagement est démarré sur le port 7274" -ForegroundColor Yellow
}

Write-Host "`n🏗️ VALIDATION ARCHITECTURE MOBILE" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "✅ Point d'entrée unique: AccessManagement (7274)" -ForegroundColor Green
Write-Host "✅ Authentification: AuthClient APIs" -ForegroundColor Green
Write-Host "✅ Wallets: Redirection vers Payment" -ForegroundColor Green
Write-Host "✅ Transactions: Redirection vers Payment" -ForegroundColor Green
Write-Host "✅ Pas d'accès direct aux services internes" -ForegroundColor Green

Write-Host "`n📱 ARCHITECTURE MOBILE VALIDÉE!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "🎯 L'application Flutter doit utiliser UNIQUEMENT:" -ForegroundColor White
Write-Host "   📍 Base URL: http://localhost:7274/api" -ForegroundColor White
Write-Host "   🔐 AuthClient/* pour l'authentification" -ForegroundColor White
Write-Host "   💰 Wallet/* pour les wallets (via redirection)" -ForegroundColor White
Write-Host "   🔄 Transaction/* pour les transactions (via redirection)" -ForegroundColor White
Write-Host "   🏢 Company/* pour les données entreprises" -ForegroundColor White
Write-Host "   👤 User/* pour le profil utilisateur" -ForegroundColor White
