﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserBlacklistController : ControllerBase
    {
        private readonly IBlackListedUserService _blacklistUserService;
        private readonly ILogger<UserBlacklistController> _logger;
        private readonly ISortingUtility _sortingUtility;

        public UserBlacklistController(IBlackListedUserService blacklistUserService,
            ILogger<UserBlacklistController> logger,
            ISortingUtility sortingUtility)
        {
            _blacklistUserService = blacklistUserService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<List<BlackListedUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<List<BlackListedUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<BlackListedUserDTO>> users = _blacklistUserService.GetAll(new Guid(companyId), enumUserType);
                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<List<BlackListedUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<BlackListedUserDTO>> response = new ResponseAPI<List<BlackListedUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPagedBlacklistUsers")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<PagedList<BlackListedUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<PagedList<BlackListedUserDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<BlackListedUserDTO>> users = _blacklistUserService.GetAll(pagedParameters, new Guid(companyId), enumUserType);

                    if (users.ObjectValue != null)
                    {
                        users.ObjectValue = _sortingUtility.SortData(pagedParameters, users);
                        users.ObjectValue = _sortingUtility.FilterData(pagedParameters, users);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = users.ObjectValue.TotalCount,
                            PageSize = users.ObjectValue.PageSize,
                            CurrentPage = users.ObjectValue.CurrentPage,
                            TotalPages = users.ObjectValue.TotalPages,
                            HasNext = users.ObjectValue.HasNext,
                            HasPrevious = users.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<PagedList<BlackListedUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<PagedList<BlackListedUserDTO>> response = new ResponseAPI<PagedList<BlackListedUserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Blocked User
        [Route("Block")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult BlockUser(BlackListedUserDTO blackListedUserDTO)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<BlackListedUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<BlackListedUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<BlackListedUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<BlackListedUserDTO> users = _blacklistUserService.BlockedUser(blackListedUserDTO, new Guid(companyId), enumUserType, new Guid(userId));
                    return Ok(users);
                }

                return Unauthorized(new ResponseAPI<BlackListedUserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<BlackListedUserDTO> response = new ResponseAPI<BlackListedUserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
