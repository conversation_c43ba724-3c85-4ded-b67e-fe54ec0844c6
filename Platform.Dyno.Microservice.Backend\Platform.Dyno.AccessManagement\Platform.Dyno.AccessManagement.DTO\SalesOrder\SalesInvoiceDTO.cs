﻿using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations.Schema;


namespace Platform.Dyno.AccessManagement.DTO.SalesOrder
{
    public class SalesInvoiceDTO : ReferentialData
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ProcessInstanceId { get; private set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double DynoAmount { get; set; }
        public Currency Currency { get; set; }
        public double NetAmount { get; set; }
        public double VATAmount { get; set; }
        public double TotalAmount { get; set; }
        public double TimbreAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public ProductType ProductType { get; set; }
        public DateTime Date { get; set; }
        public new InvoiceStatus Status { get; set; }

        #region Structure
        public Guid SalesOrderId { get; set; }
        public SalesOrderDTO? SalesOrder { get; set; }
        public Guid? DocumentId { get; set; }
        public DocumentsDTO? Document { get; set; }
        public Guid CompanyId { get; set; }
        public CompanyDTO? Company { get; set; }
        #endregion
        public void AssociateWithProcessInstance(string processInstanceId)
        {
            this.ProcessInstanceId = processInstanceId;
        }
    }
}
