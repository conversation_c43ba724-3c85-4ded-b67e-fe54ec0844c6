﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Models.BMs
{
    public class TransactionBM
    {
        public Guid Id { get; set; }
        public Guid SenderWalletId { get; set; }
        public Guid ReceiverWalletId { get; set; }
        public Guid QrCodeId { get; set; }
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        public double Amount { get; set; }
        public TransactionStatus Status { get; set; }

        public RefundStatus RefundStatus { get; set; }  = RefundStatus.None;
    }
}
