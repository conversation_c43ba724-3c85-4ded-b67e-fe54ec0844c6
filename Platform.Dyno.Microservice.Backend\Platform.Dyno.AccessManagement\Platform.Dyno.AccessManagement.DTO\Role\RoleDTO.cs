﻿using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System.Text.Json.Serialization;

namespace Platform.Dyno.AccessManagement.DTO.Role
{
    public class RoleDTO : ReferentialData
    {
        #region Role Identity Data
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public UserType UserType { get; set; }
        #endregion

        #region Structure
        [JsonIgnore]
        public IList<UserDTO>? Users { get; set; } =  new List<UserDTO>();
        public ICollection<PermissionDTO>? Permissions { get; set; }

        public Guid? CompanyId { get; set; }
        #endregion

        public override bool Equals(object obj)
        {
            if (obj == null || GetType() != obj.GetType())
            {
                return false;
            }

            RoleDTO otherRole = (RoleDTO)obj;
            return this.Id == otherRole.Id;
        }

        public override int GetHashCode()
        {
            return this.Id.GetHashCode();
        }
    }
}
