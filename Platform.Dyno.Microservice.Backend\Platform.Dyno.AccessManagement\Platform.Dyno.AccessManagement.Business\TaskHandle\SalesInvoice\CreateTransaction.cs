﻿
using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using static Camunda.Api.Client.Filter.FilterInfo;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using MimeKit.Encodings;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Notification.DTO.Notification;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("Create_Transaction")]
    public class CreateTansaction : IExternalTaskHandler
    {
        private readonly ILogger<CreateTansaction> _logger;
        private readonly IInvoiceService _invoiceService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IHelper<TransactionToCompanyDTO> _helper;
        private readonly IHelper<BalanceNotificationDTO> _notificationHelper;
        private readonly Configuration _configuration;



        public CreateTansaction(
        ILogger<CreateTansaction> logger,
        IInvoiceService invoiceService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        Configuration configuration,
        ISalesOrderService salesOrderService,
         IHelper<TransactionToCompanyDTO> helper,
         IHelper<BalanceNotificationDTO> notificationHelper)

        {
            _logger = logger;
            _invoiceService = invoiceService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _configuration = configuration;
            _salesOrderService = salesOrderService;
            _helper = helper;
            _notificationHelper= notificationHelper;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                // SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>((externalTask.Variables["SalesInvoice"] as JsonVariable)?.Value);
                var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
                SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);

                if (salesInvoiceDTO == null)
                {
                    outputVariables.Add("Result_Create_Transaction", new BooleanVariable(false));
                    outputVariables.Add("Error_Create_Transaction", new StringVariable("json conversion failed"));

                }
                else
                {
                    Guid senderCompanyId = _configurationDefaultId.CompanyId;
                    Guid receiverCompanyId = salesInvoiceDTO.CompanyId;
                    TransactionToCompanyDTO transactionToCompanyDTO = new TransactionToCompanyDTO()
                    {
                        SenderCompanyId = senderCompanyId,
                        ReceiverCompanyId = receiverCompanyId,  
                        Amount = salesInvoiceDTO.DynoAmount
                    };
                   

                        string Url = $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/CreateTransactionFromCompanyToCompany";
                        Dictionary<string, string> queryParams = new Dictionary<string, string>
                        {
                            {"pinCode", senderCompanyId.ToString()}
                        };
                    ResponseAPI<TransactionToCompanyDTO>? response = _helper.Post(Url, transactionToCompanyDTO, queryParams);
                    if (response?.StatusCode == HttpStatusCode.Created)
                    {
                        outputVariables.Add("Result_Create_Transaction", new BooleanVariable(true));
                        BalanceNotificationDTO balanceSenderNotification = new BalanceNotificationDTO
                        {
                            Balance = -salesInvoiceDTO.DynoAmount,
                            GroupName = senderCompanyId.ToString()
                        };
                        BalanceNotificationDTO balanceReceiverNotification = new BalanceNotificationDTO
                        {
                            Balance = salesInvoiceDTO.DynoAmount,
                            GroupName = receiverCompanyId.ToString()
                        };
                        ResponseAPI<BalanceNotificationDTO>? responsenotificationSenderAPI = _notificationHelper.Post($"{_configuration.NotificationAddress}/Api/Notification/SendBalance", balanceSenderNotification);
                        ResponseAPI<BalanceNotificationDTO>? responsenotificationReceiverAPI = _notificationHelper.Post($"{_configuration.NotificationAddress}/Api/Notification/SendBalance", balanceReceiverNotification);
                    }
                    else
                    {
                        outputVariables.Add("Result_Create_Transaction", new BooleanVariable(false));
                        outputVariables.Add("Error_Create_Transaction", new StringVariable($"Transaction Failed : {response?.ExceptionMessage}"));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_Create_Transaction", new BooleanVariable(false));
                outputVariables.Add("Error_Create_Transaction", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
