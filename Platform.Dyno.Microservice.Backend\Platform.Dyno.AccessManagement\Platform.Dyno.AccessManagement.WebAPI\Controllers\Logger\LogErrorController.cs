﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Logger;
using Platform.Dyno.AccessManagement.DTO.Logger;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Logger
{
    [Route("Api/[controller]")]
    [ApiController]
    public class LogErrorController : ControllerBase
    {
        private readonly ILogger<LogErrorController> _logger;
        private readonly ILogErrorService _logErrorService;
        private readonly ISortingUtility _sortingUtility;

        public LogErrorController(ILogErrorService logErrorService,
            ILogger<LogErrorController> logger,
            ISortingUtility sortingUtility)
        {
            _logErrorService = logErrorService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<LogErrorDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.SuperAdmin)
                    {
                        return Unauthorized(new ResponseAPI<LogErrorDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<List<LogErrorDTO>> errors = _logErrorService.GetAll(userType: enumUserType);
                    return Ok(errors);
                }

                return Unauthorized(new ResponseAPI<LogErrorDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "user not authorized"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<LogErrorDTO>> response = new ResponseAPI<List<LogErrorDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<LogErrorDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<LogErrorDTO>> errors = _logErrorService.GetAll(pagedParameters, userType: enumUserType);
                    if (errors.ObjectValue != null)
                    {
                        errors.ObjectValue= _sortingUtility.SortData(pagedParameters, errors);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = errors.ObjectValue.TotalCount,
                            PageSize = errors.ObjectValue.PageSize,
                            CurrentPage = errors.ObjectValue.CurrentPage,
                            TotalPages = errors.ObjectValue.TotalPages,
                            HasNext = errors.ObjectValue.HasNext,
                            HasPrevious = errors.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(errors);
                }

                return Unauthorized(new ResponseAPI<LogErrorDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "user not authorized"
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<LogErrorDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.SuperAdmin)
                    {
                        return Unauthorized(new ResponseAPI<LogErrorDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<LogErrorDTO> error = _logErrorService.Get(id);
                    if (error.ObjectValue == null)
                    {
                        return BadRequest(error);
                    }
                    return Ok(error);
                }

                return Unauthorized(new ResponseAPI<LogErrorDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });



            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<LogErrorDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }


        [Route("GetByMicroservice/{microserviceName}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(MicroserviceName microserviceName)
        {
            try
            {
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<LogErrorDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.SuperAdmin)
                    {
                        return Unauthorized(new ResponseAPI<LogErrorDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<List<LogErrorDTO>> error = _logErrorService.Get(error => error.Microservice == microserviceName);
                    return Ok(error);
                }

                return Unauthorized(new ResponseAPI<LogErrorDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });



            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<LogErrorDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }          
        }
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add(LogErrorDTO logError)
        {
            try
            {
                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<LogErrorDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.SuperAdmin)
                    {
                        return Unauthorized(new ResponseAPI<LogErrorDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<LogErrorDTO> errors = _logErrorService.Create(logError);
                    return Ok(errors);
                }

                return Unauthorized(new ResponseAPI<LogErrorDTO>()
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = "user not authorized"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<LogErrorDTO>> response = new ResponseAPI<List<LogErrorDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
