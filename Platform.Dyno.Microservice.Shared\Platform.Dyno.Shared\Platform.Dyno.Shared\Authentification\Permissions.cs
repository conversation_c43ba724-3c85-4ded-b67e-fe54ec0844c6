﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Authentification
{
    public static class Permissions
    {
        #region Company
        public readonly static string CompanyManagement = "Company Management";
        public readonly static string Company = "Company";
        public readonly static string ViewCompanyManagement = "View All Company";
        public readonly static string AddCompanyManagement = "Create New Company";
        public readonly static string UpdateCompanyManagement = "Update Company";
        public readonly static string DeleteCompanyManagement = "Delete Company";
        #endregion

        #region Authentification
        public readonly static string Authentification = "Auth Management";

        #region User
        public readonly static string User = "User";
        public readonly static string ViewUser = "View All Users";
        public readonly static string AddUser = "Create New User";
        public readonly static string UpdateUser = "Update User";
        public readonly static string DeleteUser = "Delete User";
        #endregion

        #region Role
        public readonly static string Role = "Role";
        public readonly static string ViewRole = "View All Roles";
        public readonly static string AddRole = "Create New Role";
        public readonly static string UpdateRole = "Update Role";
        public readonly static string DeleteRole = "Delete Role";
        #endregion

        #endregion
    }
}
