﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Company
{
    [Route("Api/[controller]")]
    [ApiController]
    public class CompanyController : ControllerBase
    {
        private readonly ICompanyService _companyService;
        private readonly ILogger<CompanyController> _logger;
        private readonly ISortingUtility _sortingUtility;
        public CompanyController(ICompanyService companyService, ILogger<CompanyController> logger, ISortingUtility sortingUtility)
        {
            _companyService = companyService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get

        #region Company endpoints
        
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<CompanyDTO>> companies = _companyService.GetAll(new Guid(companyId), enumUserType);
                    return Ok(companies);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<CompanyDTO>> response = new ResponseAPI<List<CompanyDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<CompanyDTO>> companiesDTO = _companyService.GetAll(pagedParameters, new Guid(companyId), enumUserType);
                    if(companiesDTO.ObjectValue != null)
                    {
                        companiesDTO.ObjectValue= _sortingUtility.SortData(pagedParameters, companiesDTO);
                       companiesDTO.ObjectValue= _sortingUtility.FilterData(pagedParameters, companiesDTO);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = companiesDTO.ObjectValue.TotalCount,
                            PageSize = companiesDTO.ObjectValue.PageSize,
                            CurrentPage = companiesDTO.ObjectValue.CurrentPage,
                            TotalPages = companiesDTO.ObjectValue.TotalPages,
                            HasNext = companiesDTO.ObjectValue.HasNext,
                            HasPrevious = companiesDTO.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }
                    
                    return Ok(companiesDTO);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllActive/{filterType}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllActive([FromQuery] PagedParameters pagedParameters, UserType filterType)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<CompanyDTO>> companiesDTO = _companyService.GetAllActiveByUserType(pagedParameters, filterType, new Guid(companyId), enumUserType);
                    if(companiesDTO.ObjectValue != null)
                    {
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = companiesDTO.ObjectValue.TotalCount,
                            PageSize = companiesDTO.ObjectValue.PageSize,
                            CurrentPage = companiesDTO.ObjectValue.CurrentPage,
                            TotalPages = companiesDTO.ObjectValue.TotalPages,
                            HasNext = companiesDTO.ObjectValue.HasNext,
                            HasPrevious = companiesDTO.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }
                    
                    return Ok(companiesDTO);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActive)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetRecents")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetRecents([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<CompanyDTO>> companiesDTO = _companyService.GetRecents(pagedParameters);
                if(companiesDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = companiesDTO.ObjectValue.TotalCount,
                        PageSize = companiesDTO.ObjectValue.PageSize,
                        CurrentPage = companiesDTO.ObjectValue.CurrentPage,
                        TotalPages = companiesDTO.ObjectValue.TotalPages,
                        HasNext = companiesDTO.ObjectValue.HasNext,
                        HasPrevious = companiesDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(companiesDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetRecents)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<CompanyDTO> companyDTO = _companyService.Get(id);
                return Ok(companyDTO);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion

        #region Employee Endpoints
        [Route("GetAllEmployees")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllEmployees([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<PagedList<UserDTO>> employeesDTO = _companyService.GetAllEmployees(pagedParameters, new Guid(companyId));
                if (employeesDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = employeesDTO.ObjectValue.TotalCount,
                        PageSize = employeesDTO.ObjectValue.PageSize,
                        CurrentPage = employeesDTO.ObjectValue.CurrentPage,
                        TotalPages = employeesDTO.ObjectValue.TotalPages,
                        HasNext = employeesDTO.ObjectValue.HasNext,
                        HasPrevious = employeesDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(employeesDTO);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllEmployees)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }


        [Route("GetAllActiveEmployees")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllActiveEmployees([FromQuery] PagedParameters pagedParameters)
        {
            try
            {

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<PagedList<UserDTO>> employeesDTO = _companyService.GetAllActiveEmployees(pagedParameters, new Guid(companyId));
                if (employeesDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = employeesDTO.ObjectValue.TotalCount,
                        PageSize = employeesDTO.ObjectValue.PageSize,
                        CurrentPage = employeesDTO.ObjectValue.CurrentPage,
                        TotalPages = employeesDTO.ObjectValue.TotalPages,
                        HasNext = employeesDTO.ObjectValue.HasNext,
                        HasPrevious = employeesDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(employeesDTO);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActiveEmployees)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllShopByCategory/{categoryType}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllShopByCategory(CategoryType categoryType)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<CompanyDTO>> companiesDTO = _companyService.GetAllShopByCategory(categoryType);
                    
                    return Ok(companiesDTO);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetNearestShopByCategory/{categoryType}/{longitude}/{latitude}/{distance}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetNearestShopByCategory(CategoryType categoryType, double longitude, double latitude, double distance)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<CompanyDTO>> companiesDTO = _companyService.GetAllShopByCategory(categoryType);

                    return Ok(companiesDTO);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        #endregion

        #endregion

        #region Create

        #region Company Endpoints
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] CompanyDTO companyDTO)
        {
            try
            {

                string? creatorUserId = HttpContext.User.FindFirstValue("Id");
                if (creatorUserId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<CompanyDTO> response = _companyService.Create(companyDTO, creatorUserId:new Guid(creatorUserId), userType:enumUserType);
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region Employee Endpoints
        [Route("AssociateEmployeeToCompany")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult AssociateEmployeeToCompany([FromBody] EmployeeDTO employee)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<EmployeeDTO> response = _companyService.AssociateEmployeeToCompany(employee.CompanyId,
                        employee.UserId,
                        employee.GroupId,
                        employee.Status);

                if (response.StatusCode == HttpStatusCode.Conflict) 
                {
                    return Ok(new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.Conflict,
                        ExceptionMessage = "Conflict associating employee to company."
                    });
                  
                }
                else if (response.StatusCode == HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred while associating employee to company."
                    });
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(AssociateEmployeeToCompany)}");
                return BadRequest(new ResponseAPI<UserDTO>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #endregion

        #region Update

        #region Company Endpoints
        [Route("Update")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] CompanyDTO companyDTO)
        {
            try
            {
                string? updateUserId = HttpContext.User.FindFirstValue("Id");
                if (updateUserId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<CompanyDTO> response = _companyService.Update(companyDTO, updateUserId:new Guid(updateUserId));
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region Employee Endpoints
        [Route("UpdateEmployeeAssociation")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateEmployeeAssociation([FromBody] EmployeeDTO employee)
        {
            try
            {
                ResponseAPI<EmployeeDTO> response = _companyService.UpdateEmployeeAssociation(employee.CompanyId,
                        employee.EmployeeId,
                        employee.UserId,
                        employee.GroupId,
                        employee.Status);
                if (response.StatusCode == HttpStatusCode.Conflict)
                {
                    return Ok(new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.Conflict,
                        ExceptionMessage = "Conflict associating employee to company."
                    });

                }
                else if (response.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred while associating employee to company."
                    });
                }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(UpdateEmployeeAssociation)}");
                return BadRequest(new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #endregion

        #region Delete

        #region Company Endpoints
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                string? deletedUserId = HttpContext.User.FindFirstValue("Id");
                if (deletedUserId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<CompanyDTO> response = _companyService.Delete(id, deletorUserId:new Guid(deletedUserId));
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        [Route("Reactivate/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Reactivate(Guid id)
        {
            try
            {
                string? reactivatedUserId = HttpContext.User.FindFirstValue("Id");
                if (reactivatedUserId == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<CompanyDTO> response = _companyService.Reactivate(id, reactivateUserId: new Guid(reactivatedUserId));
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region Employee endpoints
        [Route("DeleteEmployee")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult DeleteEmployee(EmployeeDTO employee)
        {
            try
            {
                ResponseAPI<EmployeeDTO> response = _companyService.DeleteEmployee(employee.EmployeeId,
                        employee.CompanyId);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(DeleteEmployee)}");
                return BadRequest(new ResponseAPI<CompanyDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #endregion

    }
}

