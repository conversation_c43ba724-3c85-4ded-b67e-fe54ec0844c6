﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Payment.DTO
{
    public class TransactionGroupBlockchain
    {
        public Guid Id { get; set; }
        public string SenderPrivateKey { get; set; } = string.Empty;
        public double TotalAmount { get; set; }
        public Guid QrCodeId { get; set; }
        public DateTime TransactionDate { get; set; }
        public TransactionStatus Status { get; set; }

        public List<ReceiverListBlockchain>? ReceiverTransactions { get; set; }
    }
}
