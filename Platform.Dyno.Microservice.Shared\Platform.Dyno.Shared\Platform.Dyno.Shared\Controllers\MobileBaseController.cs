using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.Helpers;
using System.Security.Claims;
using System.Net;

namespace Platform.Dyno.Shared.Controllers
{
    [ApiController]
    [Produces("application/json")]
    public abstract class MobileBaseController : ControllerBase
    {
        protected readonly ILogger _logger;

        protected MobileBaseController(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Récupère l'ID utilisateur depuis le token JWT
        /// </summary>
        protected Guid? GetCurrentUserId()
        {
            var userIdClaim = HttpContext.User.FindFirstValue("Id");
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        /// <summary>
        /// Récupère l'ID de la compagnie depuis le token JWT
        /// </summary>
        protected Guid? GetCurrentCompanyId()
        {
            var companyIdClaim = HttpContext.User.FindFirstValue("Company");
            return Guid.TryParse(companyIdClaim, out var companyId) ? companyId : null;
        }

        /// <summary>
        /// Récupère le type d'utilisateur depuis le token JWT
        /// </summary>
        protected string? GetCurrentUserType()
        {
            return HttpContext.User.FindFirstValue("UserType");
        }

        /// <summary>
        /// Récupère la langue préférée depuis les headers
        /// </summary>
        protected string GetPreferredLanguage()
        {
            return HttpContext.Request.Headers["Language"].FirstOrDefault() ?? "en";
        }

        /// <summary>
        /// Valide les paramètres de pagination pour mobile
        /// </summary>
        protected (int pageSize, int pageNumber) ValidatePagination(int? pageSize, int? pageNumber)
        {
            var (isValid, validPageSize, validPageNumber) = MobileValidationHelper.ValidatePaginationParameters(pageSize, pageNumber);
            return (validPageSize, validPageNumber);
        }

        /// <summary>
        /// Retourne une réponse d'erreur standardisée
        /// </summary>
        protected IActionResult ErrorResponse<T>(HttpStatusCode statusCode, string message)
        {
            var response = new ResponseAPI<T>
            {
                StatusCode = statusCode,
                ExceptionMessage = message
            };

            return StatusCode((int)statusCode, response);
        }

        /// <summary>
        /// Retourne une réponse de succès standardisée
        /// </summary>
        protected IActionResult SuccessResponse<T>(T data, HttpStatusCode statusCode = HttpStatusCode.OK)
        {
            var response = new ResponseAPI<T>
            {
                StatusCode = statusCode,
                ObjectValue = data
            };

            return StatusCode((int)statusCode, response);
        }

        /// <summary>
        /// Valide l'autorisation de l'utilisateur
        /// </summary>
        protected IActionResult? ValidateUserAuthorization()
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return ErrorResponse<object>(HttpStatusCode.Unauthorized, "User unauthorized");
            }

            return null;
        }

        /// <summary>
        /// Valide l'autorisation de la compagnie
        /// </summary>
        protected IActionResult? ValidateCompanyAuthorization()
        {
            var companyId = GetCurrentCompanyId();
            if (companyId == null)
            {
                return ErrorResponse<object>(HttpStatusCode.Unauthorized, "Company unauthorized");
            }

            return null;
        }

        /// <summary>
        /// Log une action utilisateur pour audit
        /// </summary>
        protected void LogUserAction(string action, object? data = null)
        {
            var userId = GetCurrentUserId();
            var companyId = GetCurrentCompanyId();
            
            _logger.LogInformation("User action: {Action}, UserId: {UserId}, CompanyId: {CompanyId}, Data: {@Data}",
                action, userId, companyId, data);
        }

        /// <summary>
        /// Valide le modèle et retourne une erreur si invalide
        /// </summary>
        protected IActionResult? ValidateModel<T>()
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .Select(x => new { Field = x.Key, Errors = x.Value?.Errors.Select(e => e.ErrorMessage) })
                    .ToList();

                return ErrorResponse<T>(HttpStatusCode.BadRequest, $"Model validation failed: {string.Join(", ", errors.SelectMany(e => e.Errors ?? new string[0]))}");
            }

            return null;
        }

        /// <summary>
        /// Ajoute les headers de pagination pour mobile
        /// </summary>
        protected void AddPaginationHeaders(int totalCount, int pageSize, int currentPage)
        {
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            var hasNext = currentPage < totalPages;
            var hasPrevious = currentPage > 1;

            var paginationData = new
            {
                TotalCount = totalCount,
                PageSize = pageSize,
                CurrentPage = currentPage,
                TotalPages = totalPages,
                HasNext = hasNext,
                HasPrevious = hasPrevious
            };

            Response.Headers.Append("X-Pagination", System.Text.Json.JsonSerializer.Serialize(paginationData));
        }
    }
}
