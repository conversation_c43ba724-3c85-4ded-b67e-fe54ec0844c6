﻿using AutoMapper;
using <PERSON>unda.Worker;
using Platform.Dyno.AccessManagement.Business.IService.CashBack;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.CashBack;
using Platform.Dyno.AccessManagement.DataModel.CashBack;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;

namespace Platform.Dyno.AccessManagement.Business.Service.CashBack
{
    public class CashBackService : ICashBackService
    {
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IUnitOfWork<CashBackEntity> _cashBackRepository;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;
        private readonly string _cashBackCacheKey = RedisCacheKey.CashBackCacheKey;
        private readonly IHelper<TransactionToCompanyDTO> _helper;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly Configuration _configuration;

        public CashBackService(IUnitOfWork<CashBackEntity> cashBackRepository,
            ICompanyService companyService,
            IUserService userService,
            IMapper mapper,
            IRedisCacheService cache,
            IHelper<TransactionToCompanyDTO> helper,
            ConfigurationDefaultId configurationDefaultId ,
            Configuration configuration
            )
        {
            _cashBackRepository = cashBackRepository;
            _companyService = companyService;
            _userService = userService;
            _mapper = mapper;
            _cache = cache;
            _helper = helper;
            _configurationDefaultId = configurationDefaultId;
            _configuration = configuration;
        }

        #region Get
        public ResponseAPI<List<CashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<CashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<List<CashBackDTO>> response = new ResponseAPI<List<CashBackDTO>>();
            var cashBacksDTO = _cache.GetData<List<CashBackDTO>>(_cashBackCacheKey + companyId.ToString());
            try
            {
                if (cashBacksDTO == null || cashBacksDTO.Count() == 0)
                {
                    IList<CashBackEntity> cashBacksEntity;
                    if (isSuperAdmin == true)
                    {
                        cashBacksEntity = _cashBackRepository.Repository.GetAll(includes: new List<string> { "Company", "PaymentDetails" });
                    }
                    else
                    {
                        cashBacksEntity = _cashBackRepository.Repository.GetAll(cashBacks => cashBacks.CompanyId == companyId);
                    }
                    var cashBacksBM = _mapper.Map<List<CashBackBM>>(cashBacksEntity);
                    var cashBacksDTOList = _mapper.Map<List<CashBackDTO>>(cashBacksBM);
                    response.StatusCode = cashBacksDTOList == null || cashBacksDTOList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = cashBacksDTOList?.OrderByDescending(c => c.Code).ToList();
                    _cache.SetData(_cashBackCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }

        public ResponseAPI<PagedList<CashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<CashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<CashBackDTO>> response = new ResponseAPI<PagedList<CashBackDTO>>();
            var cashBacksDTO = _cache.GetData<List<CashBackDTO>>(_cashBackCacheKey + companyId.ToString());
            try
            {
                if (cashBacksDTO == null || cashBacksDTO.Count() == 0)
                {
                    var cashBacksDTOList = GetAll(companyId, isSuperAdmin).ObjectValue;
                    response.StatusCode = cashBacksDTO == null || cashBacksDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = PagedList<CashBackDTO>.ToGenericPagedList(cashBacksDTOList, pagedParameters);
                    _cache.SetData(_cashBackCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<CashBackDTO>> GetAllByStatus(Guid companyId, bool isSuperAdmin, CashBackStatus cashBackStatus, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<CashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<CashBackDTO>> response = new ResponseAPI<PagedList<CashBackDTO>>();
            var cashBacksDTO = _cache.GetData<List<CashBackDTO>>(_cashBackCacheKey + companyId.ToString());
            try
            {
                if (cashBacksDTO == null || cashBacksDTO.Count() == 0)
                {
                    IList<CashBackEntity> cashBacksEntity;
                    if (isSuperAdmin == true)
                    {
                        cashBacksEntity = _cashBackRepository.Repository.GetAll(cashBack => cashBack.Status == cashBackStatus, includes: new List<string> { "Company", "PaymentDetails" }, orderBy: cashBack => cashBack.OrderByDescending(cashBack => cashBack.LastModificationTime));
                    }
                    else
                    {
                        cashBacksEntity = _cashBackRepository.Repository.GetAll(cashBack => cashBack.CompanyId == companyId && cashBack.Status == cashBackStatus, includes: new List<string> { "Company", "PaymentDetails" }, orderBy: cashBack => cashBack.OrderByDescending(cashBack => cashBack.LastModificationTime));
                    }
                    var cashBacksBM = _mapper.Map<List<CashBackBM>>(cashBacksEntity);
                    var cashBacksDTOList = _mapper.Map<List<CashBackDTO>>(cashBacksBM);
                    PagedList<CashBackDTO> cashBacksDTOPagedList = PagedList<CashBackDTO>.ToGenericPagedList(cashBacksDTOList, pagedParameters);    
                    response.StatusCode = cashBacksDTOPagedList == null || cashBacksDTOPagedList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = cashBacksDTOPagedList;
                    _cache.SetData(_cashBackCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<CashBackDTO> Get(Guid companyId, bool isSuperAdmin, Guid cashBackId)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<CashBackDTO> response = new ResponseAPI<CashBackDTO>();
            try
            {
                var cashBackDTO = GetAll(companyId, isSuperAdmin).ObjectValue?.Where(cashBack => cashBack.Id == cashBackId).FirstOrDefault();
                response.StatusCode = cashBackDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = cashBackDTO;
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<List<CashBackDTO>> Get(Guid companyId, bool isSuperAdmin, Func<CashBackDTO, bool> expression)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<CashBackDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var cashBacksDTO = GetAll(companyId, isSuperAdmin).ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<CashBackDTO>>
            {
                StatusCode = cashBacksDTO == null || cashBacksDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK,
                ExceptionMessage = cashBacksDTO == null || cashBacksDTO.Count() == 0 ? $"cashBacks Not Found !" : null,
                ObjectValue = cashBacksDTO
            };
        }
        public ResponseAPI<List<int>> GetByPeriod(Guid companyId, bool isSuperAdmin)
        {
            try
            {
                DateTime endDate = DateTime.Now.Date;
                DateTime startDate = endDate.AddYears(-1).AddDays(1);
                var cachBackCounts = new List<int>();

                var salesOrders = GetAll(companyId, isSuperAdmin).ObjectValue?.ToList();

                for (int month = 1; month <= 12; month++)
                {
                    int count = salesOrders.Where(so => so.ValidationDate >= startDate && so.ValidationDate < startDate.AddMonths(1)).Count();

                    cachBackCounts.Add(count);
                    startDate = startDate.AddMonths(1);
                }

                return new ResponseAPI<List<int>>
                {
                    StatusCode = cachBackCounts == null ? HttpStatusCode.NotFound : HttpStatusCode.OK,
                    ObjectValue = cachBackCounts?.ToList()
                };
            }
            catch (Exception ex)
            {
                return new ResponseAPI<List<int>>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.Message,
                    ObjectValue = null
                };
            }
        }
        #endregion

        #region Create
        public ResponseAPI<CashBackDTO> Create(Guid companyId, CashBackDTO cashBackDTO, Guid creatorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(creatorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={creatorUserId} is not found. Please Contact IT support"
                };
            }
            var lastCashBack = _cashBackRepository.Repository.Get(orderBy: x => x.OrderByDescending(y => y.CreationTime));
            if (lastCashBack == null)
            {
                cashBackDTO.Code = "CB" + DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString() + DateTime.UtcNow.Day.ToString() + "-" + 1.ToString();
            }
            else
            {
                int lastCashBacknumber = int.Parse(lastCashBack.Code.Split("-")[1]);
                cashBackDTO.Code = "CB" + DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString() + DateTime.UtcNow.Day.ToString() + "-" + (lastCashBacknumber + 1).ToString();
            }
            cashBackDTO.Id = Guid.NewGuid();
            cashBackDTO.CompanyId = companyId;
            cashBackDTO.Company = null;
            #region Refdata
            RefDataService<CashBackDTO>.CreateRefData(cashBackDTO, creatorUserId, user.Email);
            cashBackDTO.Status = CashBackStatus.InProgress;
            #endregion
            CashBackBM cashBackBM = _mapper.Map<CashBackBM>(cashBackDTO);
            CashBackEntity cashBackEntity = _mapper.Map<CashBackEntity>(cashBackBM);
            _cashBackRepository.Repository.Insert(cashBackEntity);
            _cashBackRepository.Save();
            cashBackDTO.Id = cashBackEntity.Id;
            if (updateCache)
                _cache.RemoveData(_cashBackCacheKey + companyId.ToString());
            return new ResponseAPI<CashBackDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = cashBackDTO
            };
        }
        #endregion
        #region Update
        public ResponseAPI<CashBackDTO> Update(Guid companyId, bool isSuperAdmin, CashBackDTO cashBackDTO, Guid updateUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(updateUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={updateUserId} is not found. Please Contact IT support"
                };
            }
            var cashBackExistDTO = Get(companyId, isSuperAdmin, cashBackDTO.Id).ObjectValue;
            if (cashBackExistDTO != null)
            {
                #region Refdata
                RefDataService<CashBackDTO>.UpdateRefData(cashBackDTO, updateUserId, user.Email);
                cashBackDTO.CreatorUserId = cashBackExistDTO.CreatorUserId;
                cashBackDTO.CreationTime = cashBackExistDTO.CreationTime;
                cashBackDTO.CreatorUserEmail = cashBackExistDTO.CreatorUserEmail;
                #endregion
                cashBackDTO.CompanyId = companyId;
                cashBackDTO.Company = null;
                CashBackBM cashBackBM = _mapper.Map<CashBackBM>(cashBackDTO);
                CashBackEntity cashBackEntity = _mapper.Map<CashBackEntity>(cashBackBM);
                _cashBackRepository.Repository.Update(cashBackEntity);
                _cashBackRepository.Save();

                if (updateCache)
                    _cache.RemoveData(_cashBackCacheKey + companyId.ToString());
                return new ResponseAPI<CashBackDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = cashBackDTO
                };
            }

            return new ResponseAPI<CashBackDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {cashBackDTO.Id} Not Found !"
            };
        }
        public ResponseAPI<CashBackDTO> ValidateCashBackRequest(Guid companyId, bool isSuperAdmin, Guid  cashBackId, Guid updateUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(updateUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={updateUserId} is not found. Please Contact IT support"
                };
            }
            var cashBackExistDTO = Get(companyId, isSuperAdmin, cashBackId).ObjectValue;

            if (cashBackExistDTO != null)
            {
                Guid senderCompanyId = cashBackExistDTO.CompanyId;
                Guid receiverCompanyId = _configurationDefaultId.CompanyId;
                TransactionToCompanyDTO transactionToCompanyDTO = new TransactionToCompanyDTO()
                {
                    SenderCompanyId = senderCompanyId,
                    ReceiverCompanyId = receiverCompanyId,
                    Amount = cashBackExistDTO.DynoAmount
                };


                string Url = $"{_configuration.PaymentAddress}/Api/TransactionBlockchain/CreateTransactionFromCompanyToCompany";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                        {
                            {"pinCode",senderCompanyId.ToString()}
                    };
                ResponseAPI<TransactionToCompanyDTO>? response = _helper.Post(Url, transactionToCompanyDTO, queryParams);
                if (response?.StatusCode == HttpStatusCode.Created)
                {
                    #region Refdata
                    RefDataService<CashBackDTO>.UpdateRefData(cashBackExistDTO, updateUserId, user.Email);
                    #endregion
                    cashBackExistDTO.Status = CashBackStatus.Valid;
                    cashBackExistDTO.ValidationDate = DateTime.Now.ToUniversalTime();
                    CashBackBM cashBackBM = _mapper.Map<CashBackBM>(cashBackExistDTO);
                    CashBackEntity cashBackEntity = _mapper.Map<CashBackEntity>(cashBackBM);
                    _cashBackRepository.Repository.Update(cashBackEntity);
                    _cashBackRepository.Save();
                    if (updateCache)
                        _cache.RemoveData(_cashBackCacheKey + companyId.ToString());
                    return new ResponseAPI<CashBackDTO>
                    {
                        StatusCode = HttpStatusCode.OK,
                        ObjectValue = cashBackExistDTO
                    };
                }
                else
                {
                    return new ResponseAPI<CashBackDTO>
                    {
                        StatusCode = HttpStatusCode.BadRequest,
                        ExceptionMessage = $"Transaction failed. exeception : {response?.ExceptionMessage}"
                    };
                }
               
            }

            return new ResponseAPI<CashBackDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Cashback with id {cashBackId} Not Found !"
            };
        }
        #endregion
        #region Delete
        public ResponseAPI<CashBackDTO> Delete(Guid companyId, bool isSuperAdmin, Guid id, Guid deletorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(deletorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<CashBackDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={deletorUserId} is not found. Please Contact IT support"
                };
            }
            CashBackDTO? cashBackDTO = Get(companyId, isSuperAdmin, id)?.ObjectValue;
            if (cashBackDTO != null)
            {
                cashBackDTO.DeleterUserId = deletorUserId;
                cashBackDTO.DeletionTime = DateTime.UtcNow;
                if (isSuperAdmin)
                {
                    cashBackDTO.Status = CashBackStatus.Invalid;
                }
                else
                {
                    cashBackDTO.Status = CashBackStatus.Cancelled;
                }
                CashBackBM cashBackBM = _mapper.Map<CashBackBM>(cashBackDTO);
                CashBackEntity cashBackEntity = _mapper.Map<CashBackEntity>(cashBackBM);
                _cashBackRepository.Repository.Update(cashBackEntity);
                _cashBackRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_cashBackCacheKey + companyId.ToString());
                }
                return new ResponseAPI<CashBackDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = cashBackDTO
                };
            }
            return new ResponseAPI<CashBackDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {cashBackDTO?.Id} Not Found !"
            };
        }
        #endregion
    }
}
