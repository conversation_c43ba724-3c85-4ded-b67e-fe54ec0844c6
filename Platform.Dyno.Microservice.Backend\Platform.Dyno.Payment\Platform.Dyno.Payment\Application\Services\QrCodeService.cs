﻿using Application.IServices;
using Application.Models.BMs;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.QrCode;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Services
{
    public class QrCodeService : IQrCodeService
    {
        private readonly IMapper _mapper;
        private ILogger<TransactionService> _logger;
        public QrCodeService(IMapper mapper, ILogger<TransactionService> logger) 
        {
            _mapper = mapper;
            _logger = logger;
        }
        public ResponseAPI<string> GenerateTransactionQrCode([FromBody] TransactionQrCodeDTO transactionQrCodeDTO)
        {
            try
            {
                var transactionQrCodeBM = _mapper.Map<TransactionQrCodeBM>(transactionQrCodeDTO);
                var qrCode = QrCodeGenerator<TransactionQrCodeBM>.GenerateQrCode(transactionQrCodeBM);
                return new ResponseAPI<string> 
                { 
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = qrCode    
                };
            }
            catch(Exception ex) 
            {
                return new ResponseAPI<string> 
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = $"An error occured while generating QrCode, exception : {ex}"
                };
            }
        }

        public ResponseAPI<string> GenerateWalletQrCode([FromBody] WalletQrCodeDTO WalletQrCodeDTO)
        {
            try 
            { 
                var walletQrCodeBM = _mapper.Map<WalletQrCodeBM>(WalletQrCodeDTO);
                var stringQrCode = walletQrCodeBM.WalletId.ToString()+"|"+walletQrCodeBM.AssignedToName.ToString()+"|"+walletQrCodeBM.AssignedToFullAddress;
                var qrCode = QrCodeGenerator<String>.GenerateQrCode(stringQrCode);
                return new ResponseAPI<string>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = qrCode
                };
            }
            catch(Exception ex) 
            {
                return new ResponseAPI<string> 
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = $"An error occured while generating QrCode, exception : {ex}"
                };
}
        }
    }
}
