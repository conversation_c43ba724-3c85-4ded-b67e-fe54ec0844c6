﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>b79b736b-4520-401b-bc98-3d211fd7a12f</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Neo4j.Driver" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions.Http" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj" />
    <ProjectReference Include="..\..\..\Server.Kafka\Server.Kafka\Server.Kafka.csproj" />
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>
</Project>