﻿using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Ticket;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.BusinessModel.Company;

public class CompanyBM : ReferentialData
{
    public Guid Id { get; set; }
    public string RNECode { get; set; } = string.Empty;
    public string TaxCode { get; set; } = string.Empty;
    public string Name { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string? CountryCode { get; set; }
    public string? Picture { get; set; }
    public double? ClientFeePercentage { get; set; }
    public EnterpiseType EntrepriseType { get; set; }
    public ServiceType ServiceType { get; set; }
    public CategoryType? CategoryType { get; set; }
    public Guid WalletId { get; set; }
    public IList<UserBM>? Users { get; set; }
    public IList<AddressBM>? Addresses { get; set; }
    public IList<EmployeeEntity>? Employees { get; set; }
    public IList<TicketBM>? Tickets { get; set; }
    public IList<PaymentDetailsBM>? PaymentDetails { get; set; }


}
