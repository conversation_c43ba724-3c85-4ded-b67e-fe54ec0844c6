﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Group;
using Platform.Dyno.AccessManagement.BusinessModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Collections.Generic;
using System.Net;

namespace Platform.Dyno.AccessManagement.Business.Service.Group;

public class GroupService : IGroupService
{
    private readonly IUnitOfWork<GroupEntity> _groupRepository;
    private readonly IUnitOfWork<GroupTicketEntity> _groupTicketRepository;
    private readonly IMapper _mapper;
    private readonly IRedisCacheService _cache;
    private readonly string _groupCacheKey = RedisCacheKey.GroupCacheKey;
    private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("group");

    public GroupService(IUnitOfWork<GroupEntity> groupRepository,
        IUnitOfWork<GroupTicketEntity> groupTicketRepository,
        IMapper mapper, 
        IRedisCacheService cache)
    {
        _groupRepository = groupRepository;
        _groupTicketRepository = groupTicketRepository;
        _mapper = mapper;
        _cache = cache;
    }

    #region Get
    public ResponseAPI<List<GroupDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<GroupDTO>> response = new ResponseAPI<List<GroupDTO>>();

        try
        {
            if (userType == UserType.Client || userType == UserType.Cashier || userType == UserType.ShopOwner)
            {
                return new ResponseAPI<List<GroupDTO>>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = exceptionMessages.UnAuthorized
                };
            }

            var groupsDTO = _cache.GetData<List<GroupDTO>>(_groupCacheKey);
            if (groupsDTO == null || groupsDTO.Count() == 0)
            {
                var groupsEntity = _groupRepository.Repository.GetAll(includes: new List<string> { "Employees", "GroupTickets", "Company", "Employees.User", "GroupTickets.Ticket" });
                var groupsBM = _mapper.Map<List<GroupBM>>(groupsEntity);
                groupsDTO = _mapper.Map<List<GroupDTO>>(groupsBM);
                
                _cache.SetData(_groupCacheKey, groupsDTO, DateTimeOffset.UtcNow.AddDays(1));
            }

            response.StatusCode = groupsDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = groupsDTO?.OrderByDescending(c => c.LastModificationTime).ToList();

            if (userType == UserType.Company)
            {
                response.ObjectValue = groupsDTO?.Where(group => group.CompanyId == companyId)
                    .OrderByDescending(c => c.LastModificationTime)
                    .ToList();
            }

        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.BadRequest;
            response.ExceptionMessage = exceptionMessages.ReadError;
        }
        return response;
    }

    public ResponseAPI<PagedList<GroupDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<PagedList<GroupDTO>> response = new ResponseAPI<PagedList<GroupDTO>>();
        List<GroupDTO>? groupsDTO = _cache.GetData<List<GroupDTO>>(_groupCacheKey);
        try
        {
            if (groupsDTO == null || groupsDTO.Count() == 0)
            {
                groupsDTO = GetAll(companyId, userType).ObjectValue;              
                _cache.SetData(_groupCacheKey, groupsDTO, DateTimeOffset.UtcNow.AddDays(1));
            }
            response.StatusCode = groupsDTO == null || groupsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = PagedList<GroupDTO>.ToGenericPagedList(groupsDTO, pagedParameters);
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }

    public ResponseAPI<PagedList<GroupDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<PagedList<GroupDTO>> response = new ResponseAPI<PagedList<GroupDTO>>();
        var groupsDTO = _cache.GetData<List<GroupDTO>>(_groupCacheKey);
        try
        {
            if (groupsDTO == null || groupsDTO.Count() == 0)
            {
                groupsDTO = GetAll(companyId, userType).ObjectValue?.Where(group => group.Status == Status.Active).ToList();            
                _cache.SetData(_groupCacheKey, groupsDTO, DateTimeOffset.UtcNow.AddDays(1));
            }
            response.StatusCode = groupsDTO == null || groupsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = PagedList<GroupDTO>.ToGenericPagedList(groupsDTO, pagedParameters);
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<GroupDTO> Get(Guid id)
    {
        ResponseAPI<GroupDTO> response = new ResponseAPI<GroupDTO>();
        try
        {
            var groupDTO = GetAll().ObjectValue?.Where(group => group.GroupId == id).FirstOrDefault();
            response.StatusCode = groupDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = groupDTO;
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }

    public ResponseAPI<List<GroupDTO>> Get(Func<GroupDTO, bool> expression)
    {
        var groupsDTO = GetAll().ObjectValue?.Where(expression).ToList();
        return new ResponseAPI<List<GroupDTO>>
        {
            StatusCode = groupsDTO == null || groupsDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK,
            ExceptionMessage = groupsDTO == null || groupsDTO.Count() == 0 ? $"companies Not Found !" : null,
            ObjectValue = groupsDTO
        };
    }

    public PagedList<GroupDTO> sortData(PagedParameters pagedParameters, ResponseAPI<PagedList<GroupDTO>> groupsDTO)
    {
        PagedList<GroupDTO> sortedData = new PagedList<GroupDTO>();
        if (pagedParameters.SortBy != null)
        {
            var property = typeof(GroupDTO).GetProperty(pagedParameters.SortBy);
            if (property != null)
            {
                var sortedList = groupsDTO.ObjectValue.ToList();
                if (pagedParameters.SortDirection == "1")
                {
                    sortedList = sortedList.OrderBy(x => property.GetValue(x, null)).ToList();
                    groupsDTO.ObjectValue = new PagedList<GroupDTO>(sortedList, groupsDTO.ObjectValue.CurrentPage, groupsDTO.ObjectValue.PageSize, groupsDTO.ObjectValue.TotalCount);

                }
                else
                {
                    sortedList = sortedList.OrderByDescending(x => property.GetValue(x, null)).ToList();
                    groupsDTO.ObjectValue = new PagedList<GroupDTO>(sortedList, groupsDTO.ObjectValue.CurrentPage, groupsDTO.ObjectValue.PageSize, groupsDTO.ObjectValue.TotalCount);

                }
            }
        }
        else
        {
            return groupsDTO.ObjectValue;
        }

    return groupsDTO.ObjectValue;

    }
    public PagedList<GroupDTO> filterData(PagedParameters pagedParameters, ResponseAPI<PagedList<GroupDTO>> groupsDTO)
    {
        if (pagedParameters.Filters != null && pagedParameters.Filters.Any())
        {
            var filteredList = groupsDTO.ObjectValue.ToList();

            foreach (var filter in pagedParameters.Filters)
            {
                var property = typeof(GroupDTO).GetProperty(filter.Key);
                var propertyType =property.PropertyType;
                if (property != null)
                {
                    if(filter.Key.Equals("GroupTickets"))
                    {
                        filteredList = filteredList.Where(x => x.GroupTickets.Any(gt=>gt.Ticket.Name.ToUpper()
                        .Contains(filter.Value.ToString().ToUpper()) == true)).ToList();

                    }
                    else
                    {
                        if(propertyType.IsEnum)
                        {
                            filteredList = filteredList.Where(x => property.GetValue(x)?.ToString()?.ToUpper()
                            .Equals(filter.Value.ToString().ToUpper()) == true).ToList();
                        }
                        else
                        {
                            filteredList = filteredList.Where(x => property.GetValue(x)?.ToString()?.ToUpper()
                            .Contains(filter.Value.ToString().ToUpper()) == true).ToList();
                        }

                    }
    
                }
            }

            return new PagedList<GroupDTO>(filteredList, groupsDTO.ObjectValue.CurrentPage, groupsDTO.ObjectValue.PageSize, groupsDTO.ObjectValue.TotalCount);
        }

        return groupsDTO.ObjectValue;
    }
    #endregion

    #region Create
    public ResponseAPI<GroupDTO> Create(GroupDTO groupDTO, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
    {
        if(userType == UserType.Company && companyId != null)
        {
            #region Refdata
            groupDTO.GroupId = Guid.NewGuid();
            groupDTO.CompanyId = (Guid)companyId; 
            RefDataService<GroupDTO>.CreateRefData(groupDTO, creatorUserId);
            #endregion
            groupDTO.CompanyId = (Guid)companyId;
            GroupBM groupBM = _mapper.Map<GroupBM>(groupDTO);
            GroupEntity groupEntity = _mapper.Map<GroupEntity>(groupBM);
            _groupRepository.Repository.Insert(groupEntity);
            _groupRepository.Save();
            groupDTO.GroupId = groupEntity.GroupId;
            
            if (updateCache)
                _cache.RemoveData(_groupCacheKey);
            
            return new ResponseAPI<GroupDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = groupDTO
            };
        }

        return new ResponseAPI<GroupDTO>()
        {
            StatusCode = HttpStatusCode.Unauthorized,
            ExceptionMessage = exceptionMessages.UnAuthorized
        };

    }
    #endregion

    #region Update
    public ResponseAPI<GroupDTO> Update(GroupDTO groupDTO, Guid? updateUserId = null, bool updateCache = true)
    {
        GroupDTO? existingGroup = Get(groupDTO.GroupId).ObjectValue;
        
        if (existingGroup != null)
        {
            #region Refdata
            RefDataService<GroupDTO>.UpdateRefData(groupDTO, updateUserId);
            #endregion

            groupDTO.CompanyId = existingGroup.CompanyId;
            GroupBM groupBM = _mapper.Map<GroupBM>(groupDTO);
            GroupEntity groupEntity = _mapper.Map<GroupEntity>(groupBM);

            // Remove existing GroupTickets
            if(existingGroup.GroupTickets != null)
            {
                foreach (var existingGroupTicket in existingGroup.GroupTickets.ToList())
                {
                    existingGroup.GroupTickets.Remove(existingGroupTicket);
                    _groupTicketRepository.Repository.DeleteByExpression(gt => gt.TicketId == existingGroupTicket.TicketId && gt.GroupId == existingGroupTicket.GroupId);
                    _groupTicketRepository.Save();
                }
            }
            

            // Add new GroupTickets
            if(groupDTO.GroupTickets != null)
            {
                foreach (var groupTicketDTO in groupDTO.GroupTickets)
                {
                    var groupTicketEntity = _mapper.Map<GroupTicketEntity>(groupTicketDTO);                   
                    if (existingGroup.GroupTickets == null)
                        existingGroup.GroupTickets = new List<GroupTicketDTO>();
                    existingGroup.GroupTickets.Add(groupTicketDTO) ;

                    _groupTicketRepository.Repository.Insert(groupTicketEntity);
                    _groupTicketRepository.Save();
                }
            }
            _groupRepository.Repository.Update(groupEntity);
            _groupRepository.Save();

            if (updateCache)
                _cache.RemoveData(_groupCacheKey);

            return new ResponseAPI<GroupDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = groupDTO
            };
        }

        return new ResponseAPI<GroupDTO>
        {
            StatusCode = HttpStatusCode.BadRequest,
            ExceptionMessage = exceptionMessages.UpdateError
        };
    }
    #endregion

    #region Delete
    public ResponseAPI<GroupDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
    {
        GroupDTO? groupDTO = Get(id).ObjectValue;
        if (groupDTO != null)
        {
            #region Refdata
            RefDataService<GroupDTO>.DeleteRefData(groupDTO, deletorUserId);
            #endregion

            _groupRepository.Repository.Delete(id);
            _groupRepository.Save();
            if (updateCache)
            {
                _cache.RemoveData(_groupCacheKey);
            }
            return new ResponseAPI<GroupDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = groupDTO
            };
        }
        
        return new ResponseAPI<GroupDTO>
        {
            StatusCode = HttpStatusCode.BadRequest,
            ExceptionMessage = exceptionMessages.DeleteError
        };
    }
    #endregion
}
