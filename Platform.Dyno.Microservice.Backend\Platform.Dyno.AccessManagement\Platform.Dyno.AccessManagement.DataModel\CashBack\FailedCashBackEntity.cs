﻿using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.CashBack
{
    public class FailedCashBackEntity : ReferentialData
    {
        public Guid Id { get; set; }
        [ForeignKey("cashbackId")]
        public Guid CashbackId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public CashBackEntity? Cashback { get; set; }
        public new CashBackStatus Status { get; set; } = CashBackStatus.Cancelled;
    }
}
