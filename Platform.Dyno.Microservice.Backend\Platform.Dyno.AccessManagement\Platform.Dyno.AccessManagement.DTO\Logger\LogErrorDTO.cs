﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Logger
{
    public class LogErrorDTO
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public MicroserviceName Microservice { get; set; }
        public string API { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;
        public ErrorType Type { get; set; }
        public DateTime CreationDate { get; set; }
        public LogLevel Level { get; set; }
    }
}
