﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.User
{
    [Route("Api/[controller]")]
    [ApiController]
    public class AuthCashierController : ControllerBase
    {
        private readonly IAuthCashierService _authCashierService;
        private readonly IUserService _userService;
        private readonly ILogger<AuthCashierController> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthCashierController(IAuthCashierService authCashierService,
            IUserService userService,
            ILogger<AuthCashierController> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _authCashierService = authCashierService;
            _userService = userService;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        [Route("CheckCashierCode/{badge}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CheckCashierCode(string badge)
        {
            try
            {
                ResponseAPI<PhoneNumberDTO> response = _authCashierService.CheckCashierBadge(badge);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CheckCashierCode)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);
            }

        }

        [Route("Register")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Register([FromBody] ClientRegisterDTO user)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                ResponseAPI<AuthResponseDTO> response = _authCashierService.Register(user);
                if (response.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return BadRequest(response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Register)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("login")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public IActionResult Login([FromBody] ClientLoginDTO loginDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? acceptLanguage = _httpContextAccessor.HttpContext?.Request.Headers["Language"];
                LanguageType language = LanguageType.En;
                _ = Enum.TryParse(acceptLanguage, out language);

                ResponseAPI<AuthResponseDTO> response = _authCashierService.Login(loginDto, language);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Login)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }

        [Route("Add")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if(enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<UserDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });

                    }
                    ResponseAPI<UserDTO> response = _authCashierService.Add(new Guid(userId), new Guid(companyId));
                    if (response.StatusCode == System.Net.HttpStatusCode.Created)
                    {
                        return Ok(response);
                    }
                    else
                    {
                        return BadRequest(response);
                    }
                }

                return Unauthorized(new ResponseAPI<UserDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });



            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);
            }

        }


        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                ResponseAPI<UserDTO> response = _userService.Remove(id);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CheckCashierCode)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);
            }

        }

        [Route("GetCashierWallet")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCashierWallet([FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<List<CashierWalletDTO>> wallet = _authCashierService.GetCashierWallet(new Guid(userId));               
                return Ok(wallet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetCashierWallet)}");
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("ForgetPassword/{countryCode}/{phoneNumber}")]
        [HttpGet]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status302Found)]
        public async Task<IActionResult> ForgetPassword(string countryCode, string phoneNumber)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<AuthResponseDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<AuthResponseDTO> response =  _authCashierService.ForgetPassword(phoneNumber, countryCode);
                return Ok(response);
            }


            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ForgetPassword)}");
                ResponseAPI<AuthResponseDTO> response = new ResponseAPI<AuthResponseDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
    }
}
