﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Payment.DTO
{
    public class TransactionDTO
    {
        public Guid Id { get; set; }
        public Guid SenderWalletId { get; set; }
        public Guid ReceiverWalletId { get; set; }
        public Guid QrCodeId { get; set; } 
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        public double Amount { get; set; }
        public bool IsCredit { get; set; }
        public TransactionStatus Status { get; set; }

        public RefundStatus RefundStatus { get; set; }
    }
}
