﻿using System;
using System.Security.Cryptography.X509Certificates;
using Neo4j.Driver;

namespace Infrastructure.DatabaseConnection
{
    public static class Neo4jInit
    {
        public static IDriver CreateDriver(string uri, string username, string password)
        {
            var driver = GraphDatabase.Driver(
                uri,
                AuthTokens.Basic(username, password),
                o => o
                    .WithEncryptionLevel(EncryptionLevel.Encrypted)
            // dev only ; en prod, utilisez un certificat valide .WithTrustAllCertificates()
            );

            // Vérifie la connexion dès le startup
            driver.VerifyConnectivityAsync()
                  .GetAwaiter()
                  .GetResult();

            return driver;
        }
    }
}