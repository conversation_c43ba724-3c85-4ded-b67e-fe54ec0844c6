﻿using MimeKit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.DTO.Emailing
{
    public class Message
    {
        public List<MailboxAddress> To { get; set; }
        public string Subject { get; set; }
        public string Content { get; set; }
        public bool IsBodyHtml { get; set; }
        public Message(MessageEmailDTO message)
        {
            To = new List<MailboxAddress>();
            To.AddRange(message.To.Select(x => new MailboxAddress(x, x)));
            this.Subject = message.Subject;
            this.Content = message.Content;
        }
    }
}
