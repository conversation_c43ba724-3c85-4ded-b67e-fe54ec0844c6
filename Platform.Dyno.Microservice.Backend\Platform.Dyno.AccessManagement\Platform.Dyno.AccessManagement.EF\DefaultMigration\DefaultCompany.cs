﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.EF.DefaultMigration
{
    public class DefaultCompany : IEntityTypeConfiguration<CompanyEntity>
    {
        private readonly ConfigurationDefaultId _defaultId;
        private readonly Configuration _config;

        public DefaultCompany(ConfigurationDefaultId defaultId,
            Configuration config)
        {
            _defaultId = defaultId;
            _config = config;
        }

        public void Configure(EntityTypeBuilder<CompanyEntity> builder)
        {
            var companyDyno = new CompanyEntity
            {
                Id = _defaultId.CompanyId,
                Name = "Dyno & Motiva Systems",
                RNECode = "1803838P",
                TaxCode = "000 M A 1803838P",
                PhoneNumber = "+21699001000",
                Email= _config.Email,
                EntrepriseType = Shared.Enum.EnterpiseType.Enterprise,
                /*PhoneNumber = _config.PhoneNumber,*/
                ClientFeePercentage = 11
            };
            builder.HasData(companyDyno);
        }
    }
}
