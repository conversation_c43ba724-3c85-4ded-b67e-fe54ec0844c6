﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass.Security
{
    public static class SecurityProvider
    {
        public static byte[] HashKeyTo256Bits(byte[]key)
        {
            using (var sha256 = new SHA256Managed())
            {
                return sha256.ComputeHash(key);
            }
        }
        public static byte[] HashKeyTo128Bits(byte[]key)
        {
            using (var sha1 = new SHA1Managed())
            {
                byte[] hashBytes = sha1.ComputeHash(key);

                // Truncate to the first 16 bytes (128 bits)
                byte[] truncatedHash = new byte[16];
                Buffer.BlockCopy(hashBytes, 0, truncatedHash, 0, truncatedHash.Length);

                return truncatedHash;
            }
        }
        public static string EncryptKey(byte[] PrivateKeyBytes, byte[] KeyBytes, byte[] iv)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = KeyBytes;
                aesAlg.IV = iv;
                aesAlg.Padding = PaddingMode.Zeros;

                byte[] encryptedData;
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, aesAlg.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        // Encrypt the key directly without using StreamWriter
                        csEncrypt.Write(PrivateKeyBytes, 0, PrivateKeyBytes.Length);
                    }

                    encryptedData = msEncrypt.ToArray();

                }
                return Convert.ToBase64String(encryptedData);

            }
        }
        public static string DecryptKey(byte[] PrivateKeyBytes, byte[] KeyBytes, byte[] iv)
        {
            // Check arguments.
            if (PrivateKeyBytes == null || PrivateKeyBytes.Length <= 0)
                throw new ArgumentNullException("cipherText");
            if (KeyBytes == null || KeyBytes.Length <= 0)
                throw new ArgumentNullException("Key");
            if (iv == null || iv.Length <= 0)
                throw new ArgumentNullException("IV");
            var privateKey = "";
            var decryptedBytes = new List<byte>();
            // Create an Aes object
            // with the specified key and IV.
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = KeyBytes;
                aesAlg.IV = iv;
                aesAlg.Padding = PaddingMode.Zeros;

                // Create a decryptor to perform the stream transform.
                ICryptoTransform decryptor = aesAlg.CreateDecryptor();

                // Create the streams used for decryption.
                using (MemoryStream msDecrypt = new MemoryStream(PrivateKeyBytes))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        int data;
                        while ((data = csDecrypt.ReadByte()) != -1)
                        {
                            decryptedBytes.Add((byte)data);
                        }
                    }
                }
            }
            privateKey = Convert.ToBase64String(decryptedBytes.ToArray());
            
            return privateKey;
        }
    }
    
}
