﻿using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Address
{
    public class MacAddressEntity : ReferentialData
    {
        #region Data
        [Required]
        public Guid Id { get; set; }
        public string MacAddress { get; set; } = string.Empty;
        public bool IsConfirmed { get; set; } = false;
        public bool IsSaved { get; set; } = false;
        #endregion

        #region Structure

        [ForeignKey("UserId")]
        public UserEntity? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
