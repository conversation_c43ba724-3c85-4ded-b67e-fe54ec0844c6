﻿using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Ticket
{
    public interface ITicketService
    {
        public ResponseAPI<List<TicketDTO>> GetAll(Guid companyId);
        public ResponseAPI<PagedList<TicketDTO>> GetAll(Guid companyId, PagedParameters pagedParameters);
        public ResponseAPI<PagedList<TicketDTO>> GetAllActive(Guid companyId, PagedParameters pagedParameters);
        public ResponseAPI<List<TicketDTO>> GetTicketsByType(Guid companyId, WalletType ticketType);
        public ResponseAPI<TicketDTO> Get(Guid companyId, Guid id);
        public ResponseAPI<List<TicketDTO>> Get(Guid companyId, Func<TicketDTO, bool> expression);
        public ResponseAPI<TicketDTO> Create(Guid companyId, TicketDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true);
        public ResponseAPI<TicketDTO> Update(Guid companyId, TicketDTO dtoObject, Guid? updateUserId = null, bool updateCache = true);
        public ResponseAPI<TicketDTO> Delete(Guid companyId, Guid id, Guid? deletorUserId = null, bool updateCache = true);
    }
}
