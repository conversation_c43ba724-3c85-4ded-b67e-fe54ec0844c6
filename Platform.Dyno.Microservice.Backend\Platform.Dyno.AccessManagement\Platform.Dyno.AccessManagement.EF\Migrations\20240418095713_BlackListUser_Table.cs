﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class BlackListUser_Table : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1db3f662-80aa-4373-83b3-1a040b312965"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2bcaaff9-1c46-4d36-9363-5dded74fda55"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2f9facb1-2291-4acd-a753-dc1fbeca5a12"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3666fb12-a9cf-46e4-9282-55f01a0e3ee4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("37bbff10-04c0-41e9-b3e7-1c8949e64df6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50f3274f-c7ed-48fb-88c8-e6f1c642de89"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57189e50-82c8-4eac-a604-8ffcbd0d7142"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("58bef5e1-79ce-45c6-9575-be103795d67c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ddd7d9c-5a4c-4c2a-92bf-a24119b0ce7e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("608608e2-7edf-4599-8b11-ef9c2f65e841"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6b735ebc-a774-4da1-8ff0-6de78332c746"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7bbee4f9-1a50-4a07-a14a-37861c129811"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8b5018c2-e58b-46d7-aa86-c23a299e2ea1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9d1d7b60-446c-4e14-9a25-fbed8bc96dad"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a890dfaf-cd23-4f56-9b8d-049cb6636be3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b5c9b1a2-7fc7-4566-b85a-4c4a7707375b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7223cc0-774b-4ba5-9fe4-117f1de8ba43"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cf66edaa-cb00-4524-a0a1-bac0a4d2dae2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ded1362c-7406-410e-a8ad-1f1c106d7cfa"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e4b7bc84-bbf2-423a-8614-59e7e5b3e3d8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f78763e2-5d85-41cd-aa61-fa6d34add3dd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fa58580f-446b-4b2f-80c7-117cd5e1256f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fc05ec08-2bff-4e84-9b83-aa77f3399b9a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fcae0b52-9667-4abb-b561-c8b39ac1cc90"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"));

            migrationBuilder.CreateTable(
                name: "BlackListedUserEntity",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    BlockedUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    BlockedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UnblockedReason = table.Column<string>(type: "text", nullable: false),
                    UnblockedUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    UnblockedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BlackListedUserEntity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BlackListedUserEntity_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "b73daf13-c629-4a54-b7a8-f3e1f0326fa8", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2628) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a56ced52-2705-47c0-89d8-a280d9b87d87", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2600) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "362e1e97-1d36-4305-a611-97f65d685d57", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2610) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "acd068b0-8e5a-4a3b-a7ac-4da021b16a77", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2634) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "26f3be49-53e9-4c5a-a2ad-843d937877e9", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2623) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fffa58c2-afee-4089-b7aa-37f3b16e46af", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2617) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "3357e32c-dddc-4cdb-98dc-1cb924504561", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3100), new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3099), "AQAAAAEAACcQAAAAEIioYzLxCw9ey+k42CPIPBp3+kaOLV0ME0JBlFgmfiV82HnA7uBGLAOGNxxW+xuoHQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("0e21cfb3-60e4-409a-b438-a8b60d236473"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("135e4f25-022c-47a3-9b0f-79e95d75074e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2811492f-da88-4933-af5a-902094630cc1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2d283c82-87ad-4d20-b039-fc50734a07b7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("36fbce42-4c38-4a74-ac67-bb882983c80b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("42b51296-900b-429c-a89d-0cd0b5b7c9c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4b0eb34d-075a-4628-bd0b-dc52f7eec0a2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4bdfebd8-8b39-418b-9a57-b2e617d0dc92"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("50a35b63-cff0-46d6-9ea7-4c4119bf2147"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("592eef25-0a3a-4d97-bcea-98cf0ec25340"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5f43caba-f688-44ed-8ce1-a207e262a298"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("608648d7-8d75-486a-8b19-fa329b4ca5cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("68aa16e1-3dbc-4b69-828d-966e519faa06"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6e9bd497-728b-4101-bf4e-5c8c29cce61b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6fdbc3ad-b568-490d-a349-97a5796b5ffb"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("74af2506-958e-41c6-9e13-764ae5990d34"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("887be67b-1c47-4273-ac91-a4dcdecbd8ac"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b6186c1b-241e-48da-aff1-7880140bfbbf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b96f629c-5f5a-4945-902b-1ff7b3df6443"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c00d081d-6d82-4a47-a343-11ecf03cb199"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c2eb4422-ceb6-4209-b9d3-2d35ccdd0b9d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c5a8f77d-2716-463f-83f7-13424066f239"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ed688d1c-88bf-4035-836f-ac0ed8b93411"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f7c360f8-10ba-45bd-b1e8-0172e8c4b060"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_BlackListedUserEntity_UserId",
                table: "BlackListedUserEntity",
                column: "UserId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BlackListedUserEntity");

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0e21cfb3-60e4-409a-b438-a8b60d236473"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("135e4f25-022c-47a3-9b0f-79e95d75074e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2811492f-da88-4933-af5a-902094630cc1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2d283c82-87ad-4d20-b039-fc50734a07b7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("36fbce42-4c38-4a74-ac67-bb882983c80b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("42b51296-900b-429c-a89d-0cd0b5b7c9c3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4b0eb34d-075a-4628-bd0b-dc52f7eec0a2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4bdfebd8-8b39-418b-9a57-b2e617d0dc92"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50a35b63-cff0-46d6-9ea7-4c4119bf2147"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("592eef25-0a3a-4d97-bcea-98cf0ec25340"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5f43caba-f688-44ed-8ce1-a207e262a298"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("608648d7-8d75-486a-8b19-fa329b4ca5cf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("68aa16e1-3dbc-4b69-828d-966e519faa06"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6e9bd497-728b-4101-bf4e-5c8c29cce61b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6fdbc3ad-b568-490d-a349-97a5796b5ffb"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("74af2506-958e-41c6-9e13-764ae5990d34"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("887be67b-1c47-4273-ac91-a4dcdecbd8ac"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b6186c1b-241e-48da-aff1-7880140bfbbf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b96f629c-5f5a-4945-902b-1ff7b3df6443"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c00d081d-6d82-4a47-a343-11ecf03cb199"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c2eb4422-ceb6-4209-b9d3-2d35ccdd0b9d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c5a8f77d-2716-463f-83f7-13424066f239"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ed688d1c-88bf-4035-836f-ac0ed8b93411"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7c360f8-10ba-45bd-b1e8-0172e8c4b060"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1447408f-467f-4c15-88d8-df21affa0d07"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"));

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fc33819a-cf6b-4aac-992c-1338ea50fd38", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9383) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a9aa4b2a-4664-4f4e-a5c5-54717044811b", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9364) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "25f0821f-2bfa-43e3-ac83-c96eee0444bd", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9372) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "323713be-f909-4f95-b60c-46cf2ce9b7bc", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9391) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "d56c2296-2579-4015-b787-923dbe943402", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9380) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "099c10ca-9442-4dc6-ad9d-74091b78e997", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9376) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "93f5f46e-0cbd-4b07-a97d-24f606028e41", new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669), new DateTime(2024, 4, 9, 0, 46, 3, 40, DateTimeKind.Utc).AddTicks(9669), "AQAAAAEAACcQAAAAEHqL+5v6qGBES5d8090am7wSb/dCyRynxjXBvY03Pr/EDGlf6IhcpIMrh/G8GnlqUQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("9b782295-ac37-45df-a1f6-0b8f12b3e68f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("b7082047-2262-44d1-86c6-fe435e49b83a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("d2a5a207-3f31-4751-948f-a3a5e6f19dbd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0f59f5ed-6cec-415b-ab49-b5a74fa0156e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1db3f662-80aa-4373-83b3-1a040b312965"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2bcaaff9-1c46-4d36-9363-5dded74fda55"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2f9facb1-2291-4acd-a753-dc1fbeca5a12"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3666fb12-a9cf-46e4-9282-55f01a0e3ee4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("37bbff10-04c0-41e9-b3e7-1c8949e64df6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("50f3274f-c7ed-48fb-88c8-e6f1c642de89"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57189e50-82c8-4eac-a604-8ffcbd0d7142"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("58bef5e1-79ce-45c6-9575-be103795d67c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5ddd7d9c-5a4c-4c2a-92bf-a24119b0ce7e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("608608e2-7edf-4599-8b11-ef9c2f65e841"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6b735ebc-a774-4da1-8ff0-6de78332c746"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7bbee4f9-1a50-4a07-a14a-37861c129811"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8b5018c2-e58b-46d7-aa86-c23a299e2ea1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9d1d7b60-446c-4e14-9a25-fbed8bc96dad"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a890dfaf-cd23-4f56-9b8d-049cb6636be3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("3802d619-9e5f-4f75-b9ee-1cdeaae6c614"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b5c9b1a2-7fc7-4566-b85a-4c4a7707375b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b7223cc0-774b-4ba5-9fe4-117f1de8ba43"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("cf66edaa-cb00-4524-a0a1-bac0a4d2dae2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ded1362c-7406-410e-a8ad-1f1c106d7cfa"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("0abb4e04-0078-4702-b7ff-38c7a32ae9fb"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e4b7bc84-bbf2-423a-8614-59e7e5b3e3d8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("d7aada80-ca5a-43d9-9eeb-afec6a14d952"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f78763e2-5d85-41cd-aa61-fa6d34add3dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("866b64fb-4f55-4f69-96b9-33f6c95e639a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("fa58580f-446b-4b2f-80c7-117cd5e1256f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fc05ec08-2bff-4e84-9b83-aa77f3399b9a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("e7e67c0a-72ea-4a19-aea8-126bd28b46a0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fcae0b52-9667-4abb-b561-c8b39ac1cc90"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("22115b92-9205-4b7e-8126-1097d1a0fe0c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }
    }
}
