﻿using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.SalesOrder
{
    public interface ISalesOrderService
    {
        public ResponseAPI<List<SalesOrderDTO>> GetAll(Guid? companyId,bool isSuperAdmin);
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters);
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAllByStatus(Guid companyId, bool isSuperAdmin, List<SalesOrderStatus> salesOrderStatus, PagedParameters pagedParameters);
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAllByType( Guid companyId, bool isSuperAdmin, ProductType productType, PagedParameters pagedParameters);
        public ResponseAPI<List<int>> GetByPeriod(Guid companyId, bool isSuperAdmin);
        public ResponseAPI<SalesOrderDTO> Get(Guid? companyId,bool isSuperAdmin, Guid id);
        public ResponseAPI<List<SalesOrderDTO>> Get(Guid companyId, bool isSuperAdmin, Func<SalesOrderDTO, bool> expression);
        public ResponseAPI<SalesOrderDTO> Create(Guid companyId, SalesOrderDTO salesOrderDTO, Guid creatorUserId, bool updateCache = true);
        public ResponseAPI<SalesOrderDTO> Update(Guid companyId, bool isSuperAdmin, SalesOrderDTO salesOrderDTO, Guid updateUserId, bool updateCache = true);
        public ResponseAPI<SalesOrderDTO> UpdateStatus( Guid salesOrderId,SalesOrderStatus salesOrderStatus, bool isSuperAdmin,Guid? companyId=null, Guid? updateUserId=null, bool updateCache = true, Guid? documentId = null);
        public ResponseAPI<SalesInvoiceDTO> ValidateTransfertRequest(Guid companyId, bool isSuperAdmin, Guid salesOrderId, Guid updateUserId, bool updateCache = true);
        public ResponseAPI<SalesOrderDTO> Delete(Guid companyId, bool isSuperAdmin, Guid id, Guid deletorUserId, bool updateCache = true);
        public Task<ResponseAPI<string>> StartProcess(SalesOrderDTO salesOrderDTO);
        
        public ResponseAPI<SalesOrderDTO> AddInstanceToSO(Guid salesOrderId, string instantanceId,bool isSuperAdmin);
        public ResponseAPI<SalesOrderDTO> ReprintPDF(Guid? companyId, bool isSuperAdmin, Guid id);
        public ResponseAPI<SalesOrderDTO> ResendMail(Guid? companyId, bool isSuperAdmin, Guid id);
    }
}
