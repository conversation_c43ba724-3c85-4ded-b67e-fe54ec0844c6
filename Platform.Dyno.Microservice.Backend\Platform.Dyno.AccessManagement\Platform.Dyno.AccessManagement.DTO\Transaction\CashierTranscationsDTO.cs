﻿using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Transaction
{
    public class CashierTransactionsDTO
    {
        public Guid? UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? FullName { get; set; }
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public Gender? Gender { get; set; }
        public List<HistoriqueTransactionDetailsDTO>? Transactions { get; set; }
    }
}
