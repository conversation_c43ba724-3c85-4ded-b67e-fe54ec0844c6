﻿using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Login;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IAuthCashierService
    {
        ResponseAPI<AuthResponseDTO> Login(ClientLoginDTO loginDto, LanguageType language);
        ResponseAPI<UserDTO> Add(Guid? createdUserId, Guid companyId);
        ResponseAPI<AuthResponseDTO> Register(ClientRegisterDTO registerDto);
        ResponseAPI<PhoneNumberDTO> CheckCashierBadge(string badge);
        ResponseAPI<List<CashierWalletDTO>> GetCashierWallet(Guid id);
        ResponseAPI<AuthResponseDTO> ForgetPassword(string phoneNumber, string? countryCode = null);
    }
}
