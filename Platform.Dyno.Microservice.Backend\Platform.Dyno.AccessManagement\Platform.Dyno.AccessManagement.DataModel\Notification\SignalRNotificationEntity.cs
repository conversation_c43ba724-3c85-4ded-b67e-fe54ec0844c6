﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.Notification
{
    public class SignalRNotificationEntity : ReferentialData
    {
        #region Data
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsSeen { get; set; } = false;
        public UserType CreatorType { get; set; }

        #endregion

        #region Sturcture
        public ICollection<CompanyNotifEntity>? CompanyNotifs { get; set; } = new List<CompanyNotifEntity>();
        public virtual IEnumerable<CompanyEntity>? SendToId => CompanyNotifs != null ? CompanyNotifs?.Select(u => u.Company).ToList() : new List<CompanyEntity>();
        #endregion
        
    }
}
