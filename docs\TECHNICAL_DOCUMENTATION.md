# 📖 DOCUMENTATION TECHNIQUE - PLATFORM DYNO

## 🏗️ **ARCHITECTURE GÉNÉRALE**

### **Vue d'ensemble**
Platform.Dyno.All est une solution microservices développée en .NET 9.0 pour la gestion des avantages sociaux des employés via une application mobile.

### **Microservices**
```
Platform.Dyno.All/
├── AccessManagement/     # Gestion des utilisateurs et authentification
├── Payment/             # Gestion des paiements et wallets blockchain
├── Notification/        # Service de notifications push/email
├── WatchDog/           # Monitoring et surveillance
├── Server.Kafka/       # Gestion des messages asynchrones
└── Shared/             # Composants partagés
```

### **Technologies**
- **Backend :** .NET 9.0, ASP.NET Core Web API
- **Base de données :** PostgreSQL (principal), Neo4j (blockchain)
- **Cache :** Redis (AWS ElastiCache)
- **Messaging :** Apache Kafka
- **Cloud :** AWS (S3, <PERSON><PERSON>, <PERSON>astiCache)
- **Containerisation :** <PERSON><PERSON>, <PERSON><PERSON> Compose
- **Orchestration :** Camunda BPM

## 🔧 **COMPOSANTS PARTAGÉS**

### **1. GlobalExceptionMiddleware**
```csharp
// Utilisation dans Program.cs
app.UseMiddleware<GlobalExceptionMiddleware>();
```

**Fonctionnalités :**
- Gestion centralisée des exceptions
- Logging automatique des erreurs
- Réponses standardisées
- Sécurisation des messages d'erreur

### **2. MobileValidationHelper**
```csharp
// Validation numéro de téléphone
bool isValid = MobileValidationHelper.IsValidPhoneNumber("+33", "123456789");

// Validation email
bool isValidEmail = MobileValidationHelper.IsValidEmail("<EMAIL>");

// Validation pagination
var (isValid, pageSize, pageNumber) = MobileValidationHelper.ValidatePaginationParameters(20, 1);
```

### **3. MobileBaseController**
```csharp
[ApiController]
public class MyController : MobileBaseController
{
    public MyController(ILogger<MyController> logger) : base(logger) { }
    
    [HttpGet]
    public IActionResult GetData()
    {
        var userId = GetCurrentUserId();
        var validation = ValidateUserAuthorization();
        if (validation != null) return validation;
        
        // Logique métier
        return SuccessResponse(data);
    }
}
```

## 📱 **OPTIMISATIONS MOBILES**

### **DTOs Optimisés**
```csharp
// Réponse paginée mobile
public class MobilePagedResponseDTO<T>
{
    public HttpStatusCode StatusCode { get; set; }
    public List<T>? Data { get; set; }
    public MobilePaginationDTO? Pagination { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### **Pagination Mobile**
- **Limite par défaut :** 20 éléments
- **Maximum autorisé :** 50 éléments
- **Headers automatiques :** X-Pagination
- **Navigation :** HasNext/HasPrevious

### **Validation Renforcée**
- **Numéros de téléphone :** Support international
- **Emails :** Validation RFC compliant
- **PIN codes :** 4-6 chiffres
- **Montants :** Validation limites métier

## 🔐 **SÉCURITÉ**

### **Authentification JWT**
```json
{
  "Token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "RefreshToken": "base64-encoded-refresh-token",
  "ExpiredDate": "2024-12-31T23:59:59Z"
}
```

### **Claims JWT**
- **Id :** Identifiant utilisateur
- **Company :** Identifiant entreprise
- **UserType :** Type d'utilisateur (Client, Company, Admin)
- **Role :** Rôle utilisateur
- **Permission :** Permissions spécifiques

### **Autorisation**
```csharp
[Authorize("Role.Client")]
[Authorize("Permission.ReadTransactions")]
```

## 🗄️ **BASE DE DONNÉES**

### **PostgreSQL (Principal)**
```
Tables principales :
├── User                 # Utilisateurs
├── Company             # Entreprises
├── Transaction         # Transactions (métadonnées)
├── Wallet              # Portefeuilles (métadonnées)
├── Group               # Groupes d'employés
├── Ticket              # Tickets de support
└── LogError            # Logs d'erreurs
```

### **Neo4j (Blockchain)**
```
Nodes :
├── Wallet              # Portefeuilles blockchain
├── Transaction         # Transactions blockchain
└── User                # Utilisateurs blockchain
```

## 🚀 **DÉPLOIEMENT**

### **Docker Compose**
```yaml
services:
  accessmanagement-webapi:
    image: platformdynoaccessmanagementimage
    ports:
      - "7274:80"
  
  payment-webapi:
    image: platformdynopaymentimage
    ports:
      - "7018:80"
  
  notification-webapi:
    image: platformdynonotificationimage
    ports:
      - "7038:80"
```

### **Configuration AWS S3**
```csharp
// Récupération configuration depuis S3
var s3Client = new AmazonS3Client(accessKey, secretKey, RegionEndpoint.USEast1);
var config = await s3Client.GetObjectAsync("dynofiles", "config.json");
```

## 📊 **MONITORING**

### **Logging**
```csharp
// Logging structuré
_logger.LogInformation("User action: {Action}, UserId: {UserId}", action, userId);

// Logging d'erreur
_logger.LogError(ex, "Error in {Method}", nameof(MethodName));
```

### **Health Checks**
```csharp
services.AddHealthChecks()
    .AddDbContextCheck<ContextDB>()
    .AddRedis(connectionString)
    .AddCheck<Neo4jHealthCheck>("neo4j");
```

## 🔄 **PATTERNS UTILISÉS**

### **Repository Pattern**
```csharp
public interface IGenericRepository<T>
{
    IEnumerable<T> GetAll();
    T? Get(Expression<Func<T, bool>> expression);
    void Insert(T entity);
    void Update(T entity);
    void Delete(Guid id);
}
```

### **Unit of Work**
```csharp
public interface IUnitOfWork<T> : IDisposable
{
    IGenericRepository<T> Repository { get; }
    void Save();
}
```

### **Response Pattern**
```csharp
public class ResponseAPI<T>
{
    public HttpStatusCode StatusCode { get; set; }
    public T? ObjectValue { get; set; }
    public string? ExceptionMessage { get; set; }
}
```

## 🧪 **TESTS**

### **Structure des Tests**
```
Tests/
├── Unit/               # Tests unitaires
├── Integration/        # Tests d'intégration
└── Mobile/            # Tests spécifiques mobile
```

### **Exemple Test Unitaire**
```csharp
[Test]
public async Task CreateUser_ValidData_ReturnsSuccess()
{
    // Arrange
    var userDto = new UserDTO { Email = "<EMAIL>" };
    
    // Act
    var result = await _userService.CreateAsync(userDto);
    
    // Assert
    Assert.That(result.StatusCode, Is.EqualTo(HttpStatusCode.Created));
}
```

## 📈 **PERFORMANCE**

### **Cache Strategy**
- **Redis :** Cache distribué pour données partagées
- **Memory Cache :** Cache local pour données statiques
- **TTL :** 24h pour données métier, 1h pour données utilisateur

### **Optimisations**
- **Pagination :** Limite les résultats
- **Lazy Loading :** Chargement à la demande
- **Compression :** Réponses JSON compressées
- **CDN :** Assets statiques via S3/CloudFront

## 🔧 **MAINTENANCE**

### **Logs d'Erreurs**
```csharp
// Création d'un log d'erreur
var logError = new LogErrorDTO
{
    Microservice = MicroserviceName.AccessManagement,
    API = "User/Create",
    Error = exception.Message,
    Type = ErrorType.ValidationFailed,
    CreationDate = DateTime.UtcNow
};
```

### **Monitoring Kafka**
```bash
# Vérifier les topics
kafka-topics.sh --list --bootstrap-server localhost:9092

# Surveiller les messages
kafka-console-consumer.sh --topic user-events --bootstrap-server localhost:9092
```

### **Backup Base de Données**
```bash
# PostgreSQL
pg_dump -h accessmanagement.c9y0a2c0g03h.us-east-1.rds.amazonaws.com -U Dyno AccessManagement > backup.sql

# Neo4j
neo4j-admin dump --database=neo4j --to=/backups/neo4j-backup.dump
```

## 🚨 **TROUBLESHOOTING**

### **Problèmes Courants**

1. **Erreur de connexion base de données**
   ```
   Solution: Vérifier les credentials AWS S3 et la configuration
   ```

2. **Token JWT expiré**
   ```
   Solution: Utiliser le refresh token pour renouveler
   ```

3. **Cache Redis indisponible**
   ```
   Solution: Fallback automatique vers base de données
   ```

4. **Kafka consumer lag**
   ```
   Solution: Augmenter le nombre de partitions/consumers
   ```

### **Commandes Utiles**

```bash
# Vérifier l'état des services
docker-compose ps

# Logs en temps réel
docker-compose logs -f accessmanagement-webapi

# Redémarrer un service
docker-compose restart payment-webapi

# Nettoyer les containers
docker-compose down && docker-compose up -d
```

## 📋 **CHECKLIST DÉPLOIEMENT**

### **Pré-déploiement**
- [ ] Tests unitaires passent (>80% couverture)
- [ ] Tests d'intégration validés
- [ ] Configuration AWS S3 mise à jour
- [ ] Secrets rotationnés si nécessaire
- [ ] Base de données migrée
- [ ] Cache Redis vidé si nécessaire

### **Post-déploiement**
- [ ] Health checks OK
- [ ] Logs d'erreurs vérifiés
- [ ] Performance monitoring actif
- [ ] Tests de fumée mobiles
- [ ] Notification équipes

Cette documentation technique fournit une vue d'ensemble complète de l'architecture et des composants de Platform.Dyno.All.
