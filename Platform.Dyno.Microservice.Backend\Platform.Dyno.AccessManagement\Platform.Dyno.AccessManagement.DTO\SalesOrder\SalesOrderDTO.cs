﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.SalesOrder
{
    [ProtoContract()]
    public class SalesOrderDTO :ReferentialData
    {
        [ProtoMember(11)]
        public Guid Id { get; set; }
        [ProtoMember(12)]
        public string ProcessInstanceId { get; private set; } = string.Empty;
        [ProtoMember(15)]
        public string Code { get; set; } = string.Empty;
        [ProtoMember(16)]
        public double DynoAmount { get; set; }
        [ProtoMember(17)]
        public Currency Currency { get; set; }
        [ProtoMember(18)]
        public double NetAmount { get; set; }
        [ProtoMember(19)]
        public double VATAmount { get; set; }
        [ProtoMember(20)]
        public double TotalAmount { get; set; }
        [ProtoMember(21)]
        public PaymentMethod PaymentMethod { get; set; }
        [ProtoMember(22)]
        public ProductType ProductType { get; set; }
        [ProtoMember(23)]
        public DateTime Date { get; set; }
        [ProtoMember(24)]
        public new SalesOrderStatus Status { get; set; }
        [ProtoMember(25)]
        #region Structure
        public Guid? DocumentId { get; set; }
        public DocumentsDTO? Document { get; set; }
        public Guid CompanyId { get; set; }
        public CompanyDTO? Company { get; set; }
        #endregion

        public void AssociateWithProcessInstance(string processInstanceId)
        {
            this.ProcessInstanceId = processInstanceId;
        }
    }
}
