﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.User
{
    public class BlackListedUserEntity
    {
        #region Data
        public Guid Id { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        public Guid BlockedUserId { get; set; }
        public DateTime BlockedDate { get; set; }

        public string UnblockedReason { get; set; } = string.Empty;
        public Guid UnblockedUserId { get; set; }
        public DateTime UnblockedDate { get; set; }
        public BlacklistUserStatus Status { get; set; }
        public int NumberOfTimesBlocked { get; set; }
        #endregion

        #region Structure
        public UserEntity? User { get; set; }
        public Guid UserId { get; set; }
        #endregion
    }
}
