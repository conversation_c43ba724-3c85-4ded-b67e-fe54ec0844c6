﻿using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest.Register
{
    public class AdminRegisterDTO
    {
        #region User Identity Data

        [Required(ErrorMessage = "UserName is required")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Email should be in a valid format")]
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;

        public string Password { get; set; } = string.Empty;
        public UserType? UserType { get; set; }
        #endregion

        #region User Data
        public Gender? Gender { get; set; }

        [Required(ErrorMessage = "Date of birth is required")]
        [DataType(DataType.Date, ErrorMessage = "Date of birth should be a valid date")]
        public DateTime? DateOfBirth { get; set; }
        #endregion

        #region User Security
        public string? MacAddress { get; set; }
        #endregion

        #region Structure
        public ICollection<AddressDTO>? Addresses { get; set; }
        public ICollection<RoleDTO>? Roles { get; set; }

        public Guid CompanyId { get; set; }

        public UserType CreatorUserType { get; set; }
        #endregion
    }
}
