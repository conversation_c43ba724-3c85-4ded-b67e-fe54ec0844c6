﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.SalesOrder
{
    public class FailedSalesOrderService : IFailedSalesOrderService
    {
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IUnitOfWork<FailedSalesOrderEntity> _failedSalesOrderRepository;
        private readonly ICompanyService _companyService;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IUserService _userService;
        private readonly string _failedSalesOrderCacheKey = RedisCacheKey.FailedSalesOrderCacheKey;

        public FailedSalesOrderService(IUnitOfWork<FailedSalesOrderEntity> failedSalesOrderRepository,
            ISalesOrderService salesOrderService,
            ICompanyService companyService,
            IUserService userService,
            IMapper mapper,
            IRedisCacheService cache)
        {
            _failedSalesOrderRepository = failedSalesOrderRepository;
            _companyService = companyService;
            _salesOrderService = salesOrderService;
            _userService = userService;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get
        public ResponseAPI<List<FailedSalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<FailedSalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<List<FailedSalesOrderDTO>> response = new ResponseAPI<List<FailedSalesOrderDTO>>();
            var salesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_failedSalesOrderCacheKey + companyId.ToString());
            try
            {
                if (salesOrdersDTO == null || salesOrdersDTO.Count() == 0)
                {
                    IList<FailedSalesOrderEntity> failedSalesOrdersEntity;
                    if (isSuperAdmin == true)
                    {
                        failedSalesOrdersEntity = _failedSalesOrderRepository.Repository.GetAll(includes: new List<string> { "SalesOrder","SalesOrder.Company", "SalesOrder.Document" });
                    }
                    else
                    {
                        failedSalesOrdersEntity = _failedSalesOrderRepository.Repository.GetAll(failedSalesOrders => failedSalesOrders.SalesOrder.CompanyId == companyId, includes: new List<string> { "SalesOrder", "SalesOrder.Company", "SalesOrder.Document" });
                    }
                    var failedSalesOrdersBM = _mapper.Map<List<FailedSalesOrderBM>>(failedSalesOrdersEntity);
                    var failedSalesOrdersDTOList = _mapper.Map<List<FailedSalesOrderDTO>>(failedSalesOrdersBM);
                    response.StatusCode = failedSalesOrdersDTOList == null || failedSalesOrdersDTOList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = failedSalesOrdersDTOList?.OrderByDescending(c => c.SalesOrder.Code).ToList();
                    _cache.SetData(_failedSalesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<FailedSalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<FailedSalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<FailedSalesOrderDTO>> response = new ResponseAPI<PagedList<FailedSalesOrderDTO>>();
            var failedSalesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_failedSalesOrderCacheKey + companyId.ToString());
            try
            {
                if (failedSalesOrdersDTO == null || failedSalesOrdersDTO.Count() == 0)
                {
                    var failedSalesOrdersDTOList = GetAll(companyId, isSuperAdmin).ObjectValue;
                    if (failedSalesOrdersDTOList!=null)
                    {
                        response.StatusCode = failedSalesOrdersDTO == null || failedSalesOrdersDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                        response.ObjectValue = PagedList<FailedSalesOrderDTO>.ToGenericPagedList(failedSalesOrdersDTOList, pagedParameters);
                        _cache.SetData(_failedSalesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                    }
                    else
                    {
                        response.StatusCode = HttpStatusCode.NotFound;
                        response.ObjectValue = PagedList<FailedSalesOrderDTO>.ToGenericPagedList(new List<FailedSalesOrderDTO>(), pagedParameters);
                    }
                    
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        #endregion

        #region Create
        public ResponseAPI<FailedSalesOrderDTO> Create(Guid companyId, bool isSuperAdmin, FailedSalesOrderDTO failedSalesOrderDTO, Guid creatorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<FailedSalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(creatorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<FailedSalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={creatorUserId} is not found. Please Contact IT support"
                };
            }
            if(failedSalesOrderDTO.Reason.Length > 256)
            {
                return new ResponseAPI<FailedSalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"reason exceeds 256 caractere"
                };
            }
            var salesOrderDTO = _salesOrderService.Get(companyId, isSuperAdmin, failedSalesOrderDTO.SalesOrderId).ObjectValue;
            if (salesOrderDTO == null)
            {
                return new ResponseAPI<FailedSalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $"sales order by id {failedSalesOrderDTO.SalesOrderId} not found"
                };
            }
            failedSalesOrderDTO.Id = Guid.NewGuid();
            #region Refdata
            RefDataService<FailedSalesOrderDTO>.CreateRefData(failedSalesOrderDTO, creatorUserId, user.Email);
            failedSalesOrderDTO.Status = SalesOrderStatus.InProgress;
            #endregion
            FailedSalesOrderBM failedSalesOrderBM = _mapper.Map<FailedSalesOrderBM>(failedSalesOrderDTO);
            FailedSalesOrderEntity failedSalesOrderEntity = _mapper.Map<FailedSalesOrderEntity>(failedSalesOrderBM);
            _failedSalesOrderRepository.Repository.Insert(failedSalesOrderEntity);
            _failedSalesOrderRepository.Save();
            failedSalesOrderDTO.Id = failedSalesOrderEntity.Id;     
            if (isSuperAdmin)
            {
                RefDataService<SalesOrderDTO>.DeleteRefData(salesOrderDTO, creatorUserId, user.Email);
                salesOrderDTO.Status = SalesOrderStatus.Rejected;
            }
            else
            {
                RefDataService<SalesOrderDTO>.DeleteRefData(salesOrderDTO, creatorUserId, user.Email);
                salesOrderDTO.Status = SalesOrderStatus.Cancelled;
            }
            var SalesOrder = _salesOrderService.Update(companyId, isSuperAdmin, salesOrderDTO, creatorUserId);
            if (SalesOrder.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<FailedSalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = SalesOrder.ExceptionMessage
                };
            }
                        
            if (updateCache)
                _cache.RemoveData(_failedSalesOrderCacheKey + companyId.ToString());
            return new ResponseAPI<FailedSalesOrderDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = failedSalesOrderDTO
            };
        }

        #endregion
    }
}
