# Test simple d'authentification
Write-Host "Test d'authentification Platform Dyno" -ForegroundColor Green

$url = "http://localhost:7274/api/AuthAdmin/login"
$body = @{
    email = "<EMAIL>"
    password = "Admin123!"
    macAddress = "00:11:22:33:44:55"
} | ConvertTo-Json

Write-Host "URL: $url" -ForegroundColor Yellow
Write-Host "Body: $body" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri $url -Method POST -Body $body -ContentType "application/json"
    Write-Host "SUCCESS - Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
