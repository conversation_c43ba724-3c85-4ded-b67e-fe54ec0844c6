﻿using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.CashBack
{
    public interface ICashBackService
    {
        public ResponseAPI<List<CashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin);
        public ResponseAPI<PagedList<CashBackDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters);
        public ResponseAPI<PagedList<CashBackDTO>> GetAllByStatus(Guid companyId, bool isSuperAdmin, CashBackStatus salesOrderStatus, PagedParameters pagedParameters);
        public ResponseAPI<List<int>> GetByPeriod(Guid companyId, bool isSuperAdmin);
        public ResponseAPI<CashBackDTO> Get(Guid companyId, bool isSuperAdmin, Guid id);
        public ResponseAPI<List<CashBackDTO>> Get(Guid companyId, bool isSuperAdmin, Func<CashBackDTO, bool> expression);
        public ResponseAPI<CashBackDTO> Create(Guid companyId, CashBackDTO cashBackDTO, Guid creatorUserId, bool updateCache = true);
        public ResponseAPI<CashBackDTO> Update(Guid companyId, bool isSuperAdmin, CashBackDTO cashBackDTO, Guid updateUserId, bool updateCache = true);
        public ResponseAPI<CashBackDTO> ValidateCashBackRequest(Guid companyId, bool isSuperAdmin, Guid cashBackId, Guid updateUserId, bool updateCache = true);
        public ResponseAPI<CashBackDTO> Delete(Guid companyId, bool isSuperAdmin, Guid id, Guid deletorUserId, bool updateCache = true);
    }
}
