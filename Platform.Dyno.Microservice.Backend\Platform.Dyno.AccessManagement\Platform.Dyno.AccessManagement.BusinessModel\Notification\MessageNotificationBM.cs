﻿using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.BusinessModel.Notification
{
    public class MessageNotificationBM : ReferentialData
    {
        public Guid Id { get; set; }
        public string Topic { get; set; } = string.Empty;
        public string TopicArn { get; set; } = string.Empty;
        public string MessageGroupId { get; set; } = string.Empty;
        public string Protocol { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Rule { get; set; } = string.Empty;
        public List<SubscriberDeviceBM> Subscribers { get; set; } = new List<SubscriberDeviceBM>();
    }
}
