using Confluent.Kafka;
using Confluent.Kafka.Admin;
//using ThirdParty.Json.LitJson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Server.Kafka
{
    public class Producer<T> : IProducer<T> where T : class, new()
    {
        private IProducer<Null, byte[]> _kafkaProducer;

        public string Topic { get; }

        public string BoostrapServer { get; set; }


        public Producer(string topic,
            string boostrapServer)
        {
            Console.WriteLine("Using Kafka Server Parameters for Producer: " + boostrapServer + "and Topic : " + topic);
            Topic = topic;
            var config = new AdminClientConfig
            {
                BootstrapServers = BoostrapServer,
                //ApiVersionRequest = true,
                SecurityProtocol = SecurityProtocol.Plaintext


            };
            _kafkaProducer = new ProducerBuilder<Null, byte[]>(config).Build();

            Task.Run(() =>
            {
                Task taskKafkaProducer = CreateTopicAsync(BoostrapServer, Topic);
            });
        }

        public async Task sendMessage(T message, CancellationToken cancellationToken)
        {

            await _kafkaProducer.ProduceAsync(Topic, new Message<Null, byte[]>()
            {
                Value = KafkaMessage<T>.TCSerialize(message)
            }, cancellationToken);

            _kafkaProducer.Flush(TimeSpan.FromSeconds(10));
        }

        public void stop()
        {
            _kafkaProducer?.Dispose();
        }


        public async Task CreateTopicAsync(string bootstrapServers, string topicName)
        {
            using (var adminClient = new AdminClientBuilder(new AdminClientConfig { BootstrapServers = bootstrapServers }).Build())
            {
                try
                {
                    await adminClient.CreateTopicsAsync(new TopicSpecification[] {
                        new TopicSpecification { Name = topicName, ReplicationFactor = 1, NumPartitions = 1 } });
                }
                catch (CreateTopicsException e)
                {
                    Console.WriteLine($"An error occurred creating topic {e.Results[0].Topic}: {e.Results[0].Error.Reason}");
                }
            }
        }
    }
}
