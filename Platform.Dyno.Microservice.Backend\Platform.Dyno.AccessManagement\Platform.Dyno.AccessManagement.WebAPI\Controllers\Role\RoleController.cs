﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using StackExchange.Redis;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Role
{
    [Route("api/[controller]")]
    [ApiController]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;
        private readonly IUserService _userService;
        private readonly ILogger<RoleController> _logger;
        private readonly ISortingUtility _sortingUtility;

        public RoleController(IRoleService roleService, 
            IUserService userService,
            ILogger<RoleController> logger,
            ISortingUtility sortingUtility)
        {
            _roleService = roleService;
            _userService = userService;
            _logger = logger;
            _sortingUtility = sortingUtility;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<List<RoleDTO>> roles = _roleService.GetAll(new Guid(companyId), enumUserType);
                    return Ok(roles);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("GetAllPaged")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromBody] PagedParameters pagedParameters)
        {
            try
            {
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<PagedList<RoleDTO>> roles = _roleService.GetAll(pagedParameters, new Guid(companyId), enumUserType);

                    if (roles.ObjectValue != null)
                    {
                        roles.ObjectValue = _sortingUtility.SortData(pagedParameters, roles);
                        roles.ObjectValue = _sortingUtility.FilterData(pagedParameters, roles);
                        PaginationData metadata = new PaginationData
                        {
                            TotalCount = roles.ObjectValue.TotalCount,
                            PageSize = roles.ObjectValue.PageSize,
                            CurrentPage = roles.ObjectValue.CurrentPage,
                            TotalPages = roles.ObjectValue.TotalPages,
                            HasNext = roles.ObjectValue.HasNext,
                            HasPrevious = roles.ObjectValue.HasPrevious
                        };

                        Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                    }

                    return Ok(roles);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<RoleDTO> role = _roleService.Get(id);
                return Ok(role);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Create

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] RoleDTO role)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? createUserId = HttpContext.User.FindFirstValue("Id");
               

                if (createUserId == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    ResponseAPI<UserDTO> user = _userService.Get(new Guid(createUserId));
                    if (user.ObjectValue == null)
                    {
                        return Unauthorized(new ResponseAPI<RoleDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    role.Users = new List<UserDTO> { user.ObjectValue };
                    ResponseAPI<RoleDTO> response = _roleService.Create(role, creatorUserId:new Guid(createUserId), companyId:new Guid(companyId), userType:enumUserType);
                    if (response.StatusCode == System.Net.HttpStatusCode.Created)
                    {
                        return Ok(response);
                    }
                    else { return BadRequest(response); }
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });

                
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Update

        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] RoleDTO role)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? updateUserId = HttpContext.User.FindFirstValue("Id");

                if (updateUserId == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<UserDTO> user = _userService.Get(new Guid(updateUserId));
                if (user.ObjectValue == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                role.Users = new List<UserDTO> { user.ObjectValue };

                ResponseAPI<RoleDTO> response = _roleService.Update(role, new Guid(updateUserId));
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else { return BadRequest(response); }


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion

        #region Delete

        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? deleteUserId = HttpContext.User.FindFirstValue("Id");

                if (deleteUserId == null)
                {
                    return Unauthorized(new ResponseAPI<RoleDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                ResponseAPI<RoleDTO> response = _roleService.Delete(id, new Guid(deleteUserId));
                return Ok(response); 
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<RoleDTO>> response = new ResponseAPI<List<RoleDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #endregion
    }
}
