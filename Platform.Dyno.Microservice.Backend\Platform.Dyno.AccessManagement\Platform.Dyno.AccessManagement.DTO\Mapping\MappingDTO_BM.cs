﻿using AutoMapper;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.CashBack;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Group;
using Platform.Dyno.AccessManagement.BusinessModel.Logger;
using Platform.Dyno.AccessManagement.BusinessModel.Notification;
using Platform.Dyno.AccessManagement.BusinessModel.Reporting;
using Platform.Dyno.AccessManagement.BusinessModel.Role;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.BusinessModel.Ticket;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.Ticket;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Logger;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Ticket;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;


namespace Platform.Dyno.AccessManagement.DTO.Mapping
{
    public class MappingDTO_BM : Profile
    {
        public MappingDTO_BM() {

            #region Company
            CreateMap<CompanyDTO, CompanyBM>().ReverseMap();
            //   .ForMember(dest => dest.Users, opt => opt.MapFrom(src => src.Users))
            //    .ForMember(dest => dest.Employees, opt => opt.MapFrom(src => src.Employees));
            CreateMap<TicketDTO, TicketEntity>().ReverseMap();
            CreateMap<EmployeeDTO, EmployeeEntity>().ReverseMap();
            CreateMap<GroupTicketDTO, GroupTicketEntity>().ReverseMap()
            .ForMember(g => g.Group, opt => opt.Ignore());
           // .ForMember(g => g.Ticket, opt => opt.Ignore());
            CreateMap<PaymentDetailsDTO, PaymentDetailsBM>().ReverseMap();
            #endregion

            #region CashBack
            CreateMap<CashBackDTO, CashBackBM>().ReverseMap();
            CreateMap<FailedCashBackDTO, FailedCashBackBM>().ReverseMap();
            #endregion

            #region Group
            CreateMap<GroupDTO, GroupBM>().ReverseMap();
            #endregion
            
            #region User
            CreateMap<UserDTO, UserBM>().ReverseMap();
             //   .ForMember(dest => dest.Roles, opt => opt.MapFrom(src => src.Roles))
             //   .ForMember(dest => dest.Addresses, opt => opt.MapFrom(src=>src.Addresses)).ReverseMap();
            CreateMap<UserTokenDTO, UserTokenBM>().ReverseMap();
            CreateMap<UserOTPDTO, UserOTPBM>().ReverseMap();
            CreateMap<UserDTO, UserEntity>().ReverseMap();
            CreateMap<BlackListedUserDTO, BlackListedUserBM>().ReverseMap();
            #endregion

            #region Role
            CreateMap<RoleDTO, RoleBM>().ReverseMap();
                //.ForMember(dest => dest.Users, opt => opt.MapFrom(src => src.Users));
            CreateMap<PermissionDTO, PermissionBM>().ReverseMap();
            #endregion

            #region Address
            CreateMap<AddressDTO, AddressBM>().ReverseMap();
            CreateMap<MacAddressDTO, MacAddressBM>().ReverseMap();
            #endregion

            #region Notification
            CreateMap<SubscriberDeviceDTO, SubscriberDeviceBM>().ReverseMap();
            CreateMap<SignalRNotificationDTO, SignalRNotificationBM>().ReverseMap();
            #endregion

            #region Sales Order 
            CreateMap<SalesOrderDTO,SalesOrderBM>().ReverseMap();
            CreateMap<FailedSalesOrderDTO, FailedSalesOrderBM>().ReverseMap();
            CreateMap<SalesInvoiceDTO, SalesInvoiceBM>().ReverseMap();
            #endregion

            #region Ticket
            CreateMap<TicketDTO, TicketBM>().ReverseMap();
            #endregion

            #region Reporting
            CreateMap<DocumentsDTO, DocumentsBM>().ReverseMap();
            #endregion

            #region Logger
            CreateMap<LogErrorDTO, LogErrorBM>().ReverseMap();
            #endregion
        }
    }
}
