﻿
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.Shared.RefData;
using System.ComponentModel.DataAnnotations.Schema;

namespace Platform.Dyno.AccessManagement.BusinessModel.Notification
{
    public class SubscriberDeviceBM : ReferentialData
    {
        public Guid Id { get; set; }
        public string EndPoint { get; set; } = string.Empty;
        public string EndPointArn { get; set; } = string.Empty;
        public string TargetArn { get; set; } = string.Empty;
        public string SubscriptionArn { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;

        [ForeignKey("UserEntityId")]
        public UserBM? User { get; set; }
        public Guid? UserEntityId { get; set; }
        public List<MessageNotificationBM> MessageNotifications { get; set; } = new List<MessageNotificationBM>();
    }
}
