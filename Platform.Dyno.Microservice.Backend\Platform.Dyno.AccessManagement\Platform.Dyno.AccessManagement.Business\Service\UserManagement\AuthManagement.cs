﻿using Microsoft.IdentityModel.Tokens;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.Authentification
{
    public static class AuthManagement
    {
        public static string CreateToken(UserDTO? user, CompanyDTO? company, List<PermissionDTO>? permissions, string key,
            string tokenLifeTime, string issuer)
        {
            SigningCredentials signingCredentials = GetSigningCredentials(key);
            List<Claim> claims = GetClaims(user, company, permissions);
            JwtSecurityToken tokenOptions = GenerateTokenOption(signingCredentials, claims, tokenLifeTime, issuer);

            return new JwtSecurityTokenHandler().WriteToken(tokenOptions);
        }
        public static SigningCredentials GetSigningCredentials(string key)
        {
            var secret = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key));
            return new SigningCredentials(secret, SecurityAlgorithms.HmacSha256);
        }

        public static List<Claim> GetClaims(UserDTO? user, CompanyDTO? company, List<PermissionDTO>? permissions)
        {
            var claims = new List<Claim>();
            if (user != null)
            {
                claims.Add(new Claim("Id", user?.Id.ToString() ?? ""));              
                claims.Add(new Claim("Company", user?.CompanyId.ToString() ?? ""));
                claims.Add(new Claim("UserType", user?.UserType.ToString() ?? ""));
                if(company!= null)
                {
                    claims.Add(new Claim("EntrepriseType", company.EntrepriseType.ToString()));
                }    
                if(user?.Roles != null && user.Roles.Count > 0)
                {
                    foreach (var role in user.Roles)
                    {
                        claims.Add(new Claim("Role", role?.Name ?? ""));
                    }
                }             
            }

            if (permissions != null && permissions.Count > 0)
            {
                foreach (var permission in permissions)
                {
                    if(permission.Permissions != null)
                    {
                        foreach (var partialPermission in permission.Permissions)
                        {
                            if(partialPermission.Permissions != null)
                            {
                                foreach (var leafPermission in partialPermission.Permissions) 
                                {
                                    claims.Add(new Claim("Permission", leafPermission.Name));
                                }
                            }
                            
                        }
                    }               
                }
            }

            return claims;
        }

        public static JwtSecurityToken GenerateTokenOption(SigningCredentials signingCredentials, 
            List<Claim> claims, string tokenLifeTime, string issuer)
        {
            DateTime expiration = DateTime.Now.AddMinutes(Convert.ToDouble(tokenLifeTime));

            JwtSecurityToken token = new JwtSecurityToken(
                issuer: issuer,
                claims: claims,
                expires: expiration,
                signingCredentials: signingCredentials
            );

            return token;
        }

        public static string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            var generator = new RNGCryptoServiceProvider();
            generator.GetBytes(randomNumber);

            return Convert.ToBase64String(randomNumber);

        }

        public static ClaimsPrincipal? GetPrincipalFromExpiredToken(string? token, string issuer, string key)
        {
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = false,
                ValidateLifetime = false,
                ValidateIssuerSigningKey = true,
                ValidIssuer = issuer,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key)),
                ClockSkew = TimeSpan.Zero
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out SecurityToken securityToken);
            if (securityToken is not JwtSecurityToken jwtSecurityToken || !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                throw new SecurityTokenException("Invalid token");

            return principal;
        }

    }
}

