﻿using AutoMapper;
using Camunda.Worker;
using Microsoft.EntityFrameworkCore;
using Platform.Dyno.AccessManagement.Business.Bpmn;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.SalesOrder;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Reporting;
using Platform.Dyno.AccessManagement.DataModel.SalesOrder;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.EmailTemplate;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;
using System.Transactions;
using Configuration = Platform.Dyno.Shared.Configuration;

namespace Platform.Dyno.AccessManagement.Business.Service.SalesOrder
{
    public class SalesOrderService : ISalesOrderService
    {
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IUnitOfWork<SalesOrderEntity> _salesOrderRepository;
        private readonly IUnitOfWork<CompanyEntity> _companyRepository;
        private readonly IUnitOfWork<DocumentsEntity> _documentsRepository;
        private readonly ICompanyService _companyService;
        private readonly IUserService _userService;
        private readonly string _salesOrderCacheKey = RedisCacheKey.SalesOrderCacheKey;
        private readonly BpmnService _bpmnService;
        private readonly Configuration _configuration;
        private readonly IEmailingService _emailingService;
        private readonly ISalesInvoiceService _salesInvoiceService;

        public SalesOrderService(IUnitOfWork<SalesOrderEntity> salesOrderRepository,
            IUnitOfWork<CompanyEntity> companyRepository,
            IUnitOfWork<DocumentsEntity> documentsRepository,
            ICompanyService companyService,
            IUserService userService,
            IMapper mapper,
            IRedisCacheService cache,
            BpmnService bpmnService,
            Configuration configuration,
            IEmailingService emailingService,
            ISalesInvoiceService salesInvoiceService)
        {
            _salesOrderRepository = salesOrderRepository;
            _companyRepository = companyRepository;
            _documentsRepository = documentsRepository;
            _companyService = companyService;
            _userService = userService;
            _mapper = mapper;
            _cache = cache;
            _bpmnService = bpmnService;
            _configuration = configuration;
            _emailingService = emailingService;
            _salesInvoiceService = salesInvoiceService;
            
        }

        #region Get
        public ResponseAPI<List<SalesOrderDTO>> GetAll(Guid? companyId, bool isSuperAdmin)
        {
            if (companyId != null)
            {
                var company = _companyService.Get((Guid)companyId).ObjectValue;
                if (company == null)
                {
                    return new ResponseAPI<List<SalesOrderDTO>>()
                    {
                        StatusCode = HttpStatusCode.NotFound,
                        ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                    };
                }
            }
            
            ResponseAPI<List<SalesOrderDTO>> response = new ResponseAPI<List<SalesOrderDTO>>();
            var salesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_salesOrderCacheKey + companyId.ToString());
            try
            {
                if (salesOrdersDTO == null || salesOrdersDTO.Count() == 0)
                {
                    IList<SalesOrderEntity> salesOrdersEntity ;
                    if (isSuperAdmin == true)
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(includes: new List<string> { "Document" ,"Company"});
                    }
                    else
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(salesOrders => salesOrders.CompanyId == companyId, includes: new List<string> { "Document" });
                    }
                    var salesOrdersBM = _mapper.Map<List<SalesOrderBM>>(salesOrdersEntity);
                    var salesOrdersDTOList = _mapper.Map<List<SalesOrderDTO>>(salesOrdersBM);

                    response.StatusCode = salesOrdersDTOList == null || salesOrdersDTOList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = salesOrdersDTOList?.OrderByDescending(c => c.Code).ToList();
                    _cache.SetData(_salesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
       
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAll(Guid companyId, bool isSuperAdmin, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<SalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<SalesOrderDTO>> response = new ResponseAPI<PagedList<SalesOrderDTO>>();
            var salesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_salesOrderCacheKey + companyId.ToString());
            try
            {
                if (salesOrdersDTO == null || salesOrdersDTO.Count() == 0)
                {
                    var salesOrdersDTOList = GetAll(companyId,isSuperAdmin).ObjectValue;
                    response.StatusCode = salesOrdersDTOList == null || salesOrdersDTOList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = PagedList<SalesOrderDTO>.ToGenericPagedList(salesOrdersDTOList, pagedParameters); 
                    _cache.SetData(_salesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAllByStatus(Guid companyId, bool isSuperAdmin, List<SalesOrderStatus> salesOrderStatus, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<SalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<SalesOrderDTO>> response = new ResponseAPI<PagedList<SalesOrderDTO>>();
            var salesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_salesOrderCacheKey + companyId.ToString());
            try
            {
                if (salesOrdersDTO == null || salesOrdersDTO.Count() == 0)
                {
                    IList<SalesOrderEntity> salesOrdersEntity;
                    if (isSuperAdmin == true)
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(salesOrder =>  salesOrderStatus.Contains(salesOrder.Status), includes: new List<string> { "Document","Company" }, orderBy: salesOrder => salesOrder.OrderByDescending(salesOrder => salesOrder.LastModificationTime));
                    }
                    else
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(salesOrder => salesOrder.CompanyId == companyId && salesOrderStatus.Contains(salesOrder.Status), includes: new List<string> { "Document","Company" }, orderBy: salesOrder => salesOrder.OrderByDescending(salesOrder => salesOrder.LastModificationTime));
                    }
                    var salesOrdersBM = _mapper.Map<List<SalesOrderBM>>(salesOrdersEntity);
                    var salesOrdersDTOList = _mapper.Map<List<SalesOrderDTO>>(salesOrdersBM);
                    PagedList<SalesOrderDTO> salesOrdersDTOPagedList = new PagedList<SalesOrderDTO>();


                    if (salesOrdersDTOList != null && salesOrdersDTOList.Count() > 0)
                    {
                        salesOrdersDTOPagedList = PagedList<SalesOrderDTO>.ToGenericPagedList(salesOrdersDTOList, pagedParameters);
                    }
                    response.StatusCode = salesOrdersDTOPagedList == null || salesOrdersDTOPagedList.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = salesOrdersDTOPagedList;
                    _cache.SetData(_salesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<PagedList<SalesOrderDTO>> GetAllByType(Guid companyId, bool isSuperAdmin, ProductType productType, PagedParameters pagedParameters)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<PagedList<SalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            ResponseAPI<PagedList<SalesOrderDTO>> response = new ResponseAPI<PagedList<SalesOrderDTO>>();
            var salesOrdersDTO = _cache.GetData<List<SalesOrderDTO>>(_salesOrderCacheKey + companyId.ToString());
            try
            {
                if (salesOrdersDTO == null || salesOrdersDTO.Count() == 0)
                {
                    IList<SalesOrderEntity> salesOrdersEntity;
                    if (isSuperAdmin == true)
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(salesOrder => salesOrder.ProductType == productType, includes: new List<string> { "Document","Company" });
                    }
                    else
                    {
                        salesOrdersEntity = _salesOrderRepository.Repository.GetAll(salesOrder => salesOrder.CompanyId == companyId && salesOrder.ProductType == productType, includes: new List<string> { "Document","Company" });
                    }
                    var salesOrdersBM = _mapper.Map<List<SalesOrderBM>>(salesOrdersEntity);
                    var salesOrdersDTOList = _mapper.Map<List<SalesOrderDTO>>(salesOrdersBM);
                    PagedList<SalesOrderDTO> salesOrdersDTOPagedList = new PagedList<SalesOrderDTO>();


                    if (salesOrdersDTOList != null && salesOrdersDTOList.Count() > 0)
                    {
                        salesOrdersDTOPagedList = PagedList<SalesOrderDTO>.ToGenericPagedList(salesOrdersDTOList, pagedParameters);
                    }
                    response.StatusCode = salesOrdersDTO == null || salesOrdersDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                    response.ObjectValue = salesOrdersDTOPagedList;
                    _cache.SetData(_salesOrderCacheKey + companyId.ToString(), response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<SalesOrderDTO> Get(Guid? companyId, bool isSuperAdmin, Guid salesOrderId)
        {            
            ResponseAPI<SalesOrderDTO> response = new ResponseAPI<SalesOrderDTO>();
            try
            {
                var salesOrderDTO = GetAll(companyId, isSuperAdmin).ObjectValue?.Where(salesOrder => salesOrder.Id == salesOrderId).FirstOrDefault();
                response.StatusCode = salesOrderDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = salesOrderDTO;
            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.InternalServerError;
                response.ExceptionMessage = ex.Message;
            }
            return response;
        }
        public ResponseAPI<List<SalesOrderDTO>> Get(Guid companyId, bool isSuperAdmin, Func<SalesOrderDTO, bool> expression)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<List<SalesOrderDTO>>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var salesOrdersDTO = GetAll(companyId, isSuperAdmin).ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<SalesOrderDTO>>
            {
                StatusCode = salesOrdersDTO == null || salesOrdersDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK,
                ExceptionMessage = salesOrdersDTO == null || salesOrdersDTO.Count() == 0 ? $"salesOrders Not Found !" : null,
                ObjectValue = salesOrdersDTO
            };
        }
        public ResponseAPI<List<int>> GetByPeriod(Guid companyId, bool isSuperAdmin)
        {
            try
            {
                DateTime endDate = DateTime.Now.Date;
                DateTime startDate = endDate.AddYears(-1).AddDays(1);
                var salesOrderCounts = new List<int>();

                var salesOrders = GetAll(companyId, isSuperAdmin).ObjectValue?.ToList();

                for (int month = 1; month <= 12; month++)
                {
                    int count = salesOrders.Where(so => so.Date >= startDate && so.Date < startDate.AddMonths(1)).Count();

                    salesOrderCounts.Add(count);
                    startDate = startDate.AddMonths(1);
                }

                return new ResponseAPI<List<int>>
                {
                    StatusCode = salesOrderCounts == null ? HttpStatusCode.NotFound : HttpStatusCode.OK,
                    ObjectValue = salesOrderCounts?.ToList()
                };
            }
            catch (Exception ex)
            {
                return new ResponseAPI<List<int>>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.Message,
                    ObjectValue = null
                };
            }
        }
        #endregion

        #region Create
        public ResponseAPI<SalesOrderDTO> Create(Guid companyId, SalesOrderDTO salesOrderDTO, Guid creatorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            #region Elimine Url Image from Company Dto
            string url = $"{_configuration.AWSS3URL}/Images/";
            // Find the index where string2 ends in string1
            int? index = company.Picture?.IndexOf(url);
            if (index != null && index != -1)
            {
                // Get the substring starting from the index where string2 ends to the end of string1
                company.Picture = company.Picture?.Substring((int)(index + url.Length));
            };
            #endregion

            var user = _userService.Get(creatorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={creatorUserId} is not found. Please Contact IT support"
                };
            }
            var lastSalesOrder = _salesOrderRepository.Repository.Get(x=>x.Date.Date==DateTime.UtcNow.Date,orderBy: x => x.OrderByDescending(y=>y.Date));
            if (lastSalesOrder == null)
            {
                salesOrderDTO.Code = "BC"+DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString("00") + DateTime.UtcNow.Day.ToString("00")+"-"+1.ToString();
            }
            else
            {
                int lastSalesOrdernumber = int.Parse( lastSalesOrder.Code.Split("-")[1]);
                salesOrderDTO.Code= "BC" + DateTime.UtcNow.Year.ToString() + DateTime.UtcNow.Month.ToString("00") + DateTime.UtcNow.Day.ToString("00") + "-" + (lastSalesOrdernumber + 1).ToString();
            }
            salesOrderDTO.Id = Guid.NewGuid();
            salesOrderDTO.CompanyId = companyId;
            salesOrderDTO.Date = DateTime.UtcNow;
            #region Refdata
            RefDataService<SalesOrderDTO>.CreateRefData(salesOrderDTO, creatorUserId,user.Email);
            salesOrderDTO.Status=SalesOrderStatus.InProgress;
            #endregion
            SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderDTO);
            SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
            _salesOrderRepository.Repository.Insert(salesOrderEntity);
            _salesOrderRepository.Save();
            salesOrderDTO.Id = salesOrderEntity.Id;
            if (updateCache)
                _cache.RemoveData(_salesOrderCacheKey + companyId.ToString());
            return new ResponseAPI<SalesOrderDTO>()
            {
                StatusCode = HttpStatusCode.Created,
                ObjectValue = salesOrderDTO
            };
        }
        #endregion

        #region Update
        public ResponseAPI<SalesOrderDTO> Update(Guid companyId, bool isSuperAdmin, SalesOrderDTO salesOrderDTO, Guid updateUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(updateUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={updateUserId} is not found. Please Contact IT support"
                };
            }
            var salesOrderExistDTO = Get(companyId, isSuperAdmin, salesOrderDTO.Id).ObjectValue;
            if (salesOrderExistDTO != null)
            {
                #region Refdata
                RefDataService<SalesOrderDTO>.UpdateRefData(salesOrderDTO, updateUserId,user.Email);
                salesOrderDTO.CreatorUserId = salesOrderExistDTO.CreatorUserId;
                salesOrderDTO.CreationTime = salesOrderExistDTO.CreationTime;
                salesOrderDTO.CreatorUserEmail = salesOrderExistDTO.CreatorUserEmail;
                #endregion
                salesOrderDTO.CompanyId = companyId;
                SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderDTO);
                SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
                _salesOrderRepository.Repository.Update(salesOrderEntity);
                _salesOrderRepository.Save();

                if (updateCache)
                    _cache.RemoveData(_salesOrderCacheKey + companyId.ToString());
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesOrderDTO
                };
            }

            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {salesOrderDTO.Id} Not Found !"
            };
        }
        public ResponseAPI<SalesOrderDTO> UpdateStatus(Guid salesOrderId, SalesOrderStatus salesOrderStatus, bool isSuperAdmin, Guid? companyId = null, Guid? updateUserId = null,  bool updateCache = true, Guid? documentId = null)
        {
            if (companyId==null && isSuperAdmin==false)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }

            var salesOrderExistDTO = Get(companyId, isSuperAdmin, salesOrderId).ObjectValue;
            if (salesOrderExistDTO != null)
            {
                if (documentId!=null)
                {
                    salesOrderExistDTO.DocumentId = (Guid)documentId;
                }
                
                if (salesOrderExistDTO.Status != SalesOrderStatus.Valid)
                {
                    #region Refdata
                    salesOrderExistDTO.Status = salesOrderStatus;
                    RefDataService<SalesOrderDTO>.UpdateRefData(salesOrderExistDTO, null);
                    #endregion
                    SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderExistDTO);
                    SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
                    _salesOrderRepository.Repository.Update(salesOrderEntity);
                    _salesOrderRepository.Save();
                }
                
                if (updateCache)
                    _cache.RemoveData(_salesOrderCacheKey + salesOrderExistDTO.CompanyId.ToString());
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesOrderExistDTO
                };
            }

            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {salesOrderId} Not Found !"
            };

        }

        #endregion
        
        #region Validate request
        public ResponseAPI<SalesInvoiceDTO> ValidateTransfertRequest(Guid companyId, bool isSuperAdmin, Guid salesOrderId, Guid updateUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<SalesInvoiceDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }

            #region Elimine Url Image from Company Dto
            string url = $"{_configuration.AWSS3URL}/Images/";
            // Find the index where string2 ends in string1
            int? index = company.Picture?.IndexOf(url);
            if (index != null && index != -1)
            {
                // Get the substring starting from the index where string2 ends to the end of string1
                company.Picture = company.Picture?.Substring((int)(index + url.Length));
            };
            #endregion

            var user = _userService.Get(updateUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<SalesInvoiceDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={updateUserId} is not found. Please Contact IT support"
                };
            }
            var salesOrderExistDTO = Get(companyId, isSuperAdmin, salesOrderId).ObjectValue;
            if (salesOrderExistDTO != null)
            {
                #region Refdata
                RefDataService<SalesOrderDTO>.UpdateRefData(salesOrderExistDTO, updateUserId, user.Email);

                #endregion
                salesOrderExistDTO.Status = SalesOrderStatus.ValidationInProgress;
                salesOrderExistDTO.Date = DateTime.UtcNow;
                SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderExistDTO);
                SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
                var salesInvoiceExist = _salesInvoiceService.GetSalesInvoiceBySalesOrderId(salesOrderId);
                if (salesInvoiceExist != null)
                {
                    using (var transaction = _salesOrderRepository.Repository.BeginTransaction())
                    {
                        try
                        {
                            _salesOrderRepository.Repository.Update(salesOrderEntity);
                            _salesOrderRepository.Save();
                            _salesOrderRepository.Repository.Detach(salesOrderEntity);

                            salesInvoiceExist.Status=InvoiceStatus.InProgress;
                            _salesInvoiceService.Update(salesInvoiceExist);
                            transaction.Commit();
                            if (updateCache)
                            {
                                _cache.RemoveData(_salesOrderCacheKey + companyId.ToString());
                                _cache.RemoveData(_salesOrderCacheKey + salesOrderExistDTO.CompanyId.ToString());

                            }
                            return new ResponseAPI<SalesInvoiceDTO>
                            {
                                StatusCode = HttpStatusCode.OK,
                                ObjectValue = salesInvoiceExist
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            return new ResponseAPI<SalesInvoiceDTO>
                            {
                                StatusCode = HttpStatusCode.InternalServerError,
                                ExceptionMessage = $"An error occured"
                            };
                        }
                    }                    
                }

                using (var transaction = _salesOrderRepository.Repository.BeginTransaction())
                {
                    try
                    {
                        _salesOrderRepository.Repository.Update(salesOrderEntity);
                        _salesOrderRepository.Save();
                        _salesOrderRepository.Repository.Detach(salesOrderEntity);
                        if (salesOrderEntity.Company != null)
                        {
                            _companyRepository.Repository.Detach(salesOrderEntity.Company);
                        }
                        if (salesOrderEntity.Document != null)
                        {
                            _documentsRepository.Repository.Detach(salesOrderEntity.Document);
                        }
                        

                        SalesInvoiceDTO salesInvoiceDTO = _mapper.Map<SalesInvoiceDTO>(salesOrderExistDTO);
                        salesInvoiceDTO.AssociateWithProcessInstance("");
                        salesInvoiceDTO.Status = InvoiceStatus.InProgress;
                        salesInvoiceDTO.Date = DateTime.UtcNow;
                        salesInvoiceDTO.SalesOrderId = salesOrderExistDTO.Id;
                        salesInvoiceDTO.DeleterUserEmail = null;
                        salesInvoiceDTO.DeleterUserId = null;
                        salesInvoiceDTO.DeletionTime = null;
                        salesInvoiceDTO.LastModifierUserEmail = null;
                        salesInvoiceDTO.LastModifierUserId = null;
                        salesInvoiceDTO.LastModificationTime = null;
                        salesInvoiceDTO.Company = null;
                        salesInvoiceDTO.Document = null;
                        var responseSalesInvoice = _salesInvoiceService.Create(salesInvoiceDTO, updateUserId, true, companyId, UserType.SuperAdmin);

                        transaction.Commit();
                        if (updateCache)
                        {
                            _cache.RemoveData(_salesOrderCacheKey + companyId.ToString());
                            _cache.RemoveData(_salesOrderCacheKey + salesOrderExistDTO.CompanyId.ToString());

                        }
                        return new ResponseAPI<SalesInvoiceDTO>
                        {
                            StatusCode = HttpStatusCode.OK,
                            ObjectValue = responseSalesInvoice.ObjectValue
                        };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ResponseAPI<SalesInvoiceDTO>
                        {
                            StatusCode = HttpStatusCode.InternalServerError,
                            ExceptionMessage = $"An error occured"
                        };
                    }
                }
               
            }

            return new ResponseAPI<SalesInvoiceDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"transfert request by Id : {salesOrderId} not found"
            };
        }
        #endregion
        #region Delete
        public ResponseAPI<SalesOrderDTO> Delete(Guid companyId, bool isSuperAdmin, Guid id, Guid deletorUserId, bool updateCache = true)
        {
            var company = _companyService.Get(companyId).ObjectValue;
            if (company == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"Company by id ={companyId} is not found. Please Contact IT support"
                };
            }
            var user = _userService.Get(deletorUserId).ObjectValue;
            if (user == null)
            {
                return new ResponseAPI<SalesOrderDTO>()
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $"User by id ={deletorUserId} is not found. Please Contact IT support"
                };
            }
            SalesOrderDTO? salesOrderDTO = Get(companyId, isSuperAdmin, id)?.ObjectValue;
            if (salesOrderDTO != null)
            {
                salesOrderDTO.DeleterUserId = deletorUserId;
                salesOrderDTO.DeletionTime = DateTime.UtcNow; 
                if (isSuperAdmin)
                {
                    salesOrderDTO.Status = SalesOrderStatus.Invalid;
                }
                else
                {
                    salesOrderDTO.Status=SalesOrderStatus.Cancelled;
                }
                SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderDTO);
                SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
                _salesOrderRepository.Repository.Update(salesOrderEntity);
                _salesOrderRepository.Save();
                if (updateCache)
                {
                    _cache.RemoveData(_salesOrderCacheKey + companyId.ToString());
                }
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesOrderDTO
                };
            }
            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $"Company with id {salesOrderDTO?.Id} Not Found !"
            };
        }
        #endregion

        #region start process 
        public async Task<ResponseAPI<string>> StartProcess(SalesOrderDTO salesOrderDTO)
        {
            try
            {
                var processInstanceId = await _bpmnService.StartProcessForSalesOrder(salesOrderDTO);
                salesOrderDTO.AssociateWithProcessInstance(processInstanceId);
                return new ResponseAPI<string>
                {
                    StatusCode = HttpStatusCode.OK,
                    ObjectValue = salesOrderDTO.ProcessInstanceId
            };
                
            }
            catch (Exception ex) {
                return new ResponseAPI<string>
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = ex.Message
                };

            }
            
        }
        #endregion
        #region add instance to so
        public ResponseAPI<SalesOrderDTO> AddInstanceToSO(Guid salesOrderId, string instantanceId, bool isSuperAdmin)
        {
            var salesOrderExistDTO = Get(null, isSuperAdmin,salesOrderId).ObjectValue ;
            if (salesOrderExistDTO == null)
            {
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $" sales order with id {salesOrderId} is not found"

                };
            }
            salesOrderExistDTO.AssociateWithProcessInstance(instantanceId);
            SalesOrderBM salesOrderBM = _mapper.Map<SalesOrderBM>(salesOrderExistDTO);
            SalesOrderEntity salesOrderEntity = _mapper.Map<SalesOrderEntity>(salesOrderBM);
            _salesOrderRepository.Repository.Update(salesOrderEntity);
            _salesOrderRepository.Save();
            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = salesOrderExistDTO
            };
        }
        #endregion

        #region reprint pdf
        public ResponseAPI<SalesOrderDTO> ReprintPDF(Guid? companyId, bool isSuperAdmin, Guid SalesOrderId)
        {
            var salesOrderExist = Get(companyId, isSuperAdmin, SalesOrderId);
            if (salesOrderExist.ObjectValue == null || salesOrderExist.StatusCode!= HttpStatusCode.OK)
            {
                return new ResponseAPI<SalesOrderDTO> 
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage =$" sales order with id {SalesOrderId} is not found"

                };
            };

            var response = StartProcess(salesOrderExist.ObjectValue).GetAwaiter().GetResult();

            if (response.StatusCode!= HttpStatusCode.OK)
            {
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    ExceptionMessage = $" failed to start process"

                };
            }
            var res = UpdateStatus(SalesOrderId, SalesOrderStatus.Reprinting, true);

            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = salesOrderExist.ObjectValue

            };

        }
        #endregion

        #region resend mail
        public ResponseAPI<SalesOrderDTO> ResendMail(Guid? companyId, bool isSuperAdmin, Guid id)
        {
            var salesOrderExist = Get(companyId, isSuperAdmin, id);
            if (salesOrderExist.ObjectValue == null || salesOrderExist.StatusCode != HttpStatusCode.OK)
            {
                return new ResponseAPI<SalesOrderDTO>
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = $" sales order with id {id} is not found"

                };
            };

            var company = _companyService.Get(salesOrderExist.ObjectValue.CompanyId);
            if (company.ObjectValue != null && salesOrderExist.ObjectValue.Document?.Path!=null)
            {
                var emailBody = EmailTemplateEN.SalesOrderEmailbody;
                var content = emailBody.Replace("#URL#", _configuration.AWSS3URL + salesOrderExist.ObjectValue.Document.Path).Replace("[type_of_action]", "Dynos");
                var response = _emailingService.sendSalesOrderEmail(company.ObjectValue.Email, content);
                if (response != null && response.StatusCode == System.Net.HttpStatusCode.Accepted)
                {
                    var updateStatusResponse = UpdateStatus(id, SalesOrderStatus.EmailSent,true);
                    if (updateStatusResponse != null && updateStatusResponse.StatusCode==HttpStatusCode.OK)
                    {
                        return new ResponseAPI<SalesOrderDTO>()
                    {
                        StatusCode = HttpStatusCode.Accepted,
                        ObjectValue = salesOrderExist.ObjectValue

                    };
                    }
                    else
                    {
                        return new ResponseAPI<SalesOrderDTO>
                        {
                            StatusCode = HttpStatusCode.NotFound,
                            ExceptionMessage = $" failed to send mail"

                        };
                }
                }
                else
                {
                    return new ResponseAPI<SalesOrderDTO>
                    {
                        StatusCode = HttpStatusCode.NotFound,
                        ExceptionMessage = $" failed to send mail"

                    };
                }
            }
            return new ResponseAPI<SalesOrderDTO>
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = $" failed to send mail"

            };

        }
        #endregion
    }
}
