﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.Business.Service.RoleManagement;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared;
using AutoMapper;
using Platform.Dyno.AccessManagement.DTO.User;
using System.Security.Claims;
using Platform.Dyno.AccessManagement.Business.IService.Wallet;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Wallet
{
    [Route("Api/[controller]")]
    [ApiController]
    public class WalletController : ControllerBase
    {
        private readonly ILogger<WalletController> _logger;
        private readonly IHelper<WalletDTO> _helper;
        private readonly IMapper _mapper;
        private readonly Platform.Dyno.Shared.Configuration _configuration;
        private readonly IWalletService _walletService;
        private readonly IUserService _userService;
        public WalletController(ILogger<WalletController> logger,
            IHelper<WalletDTO> helper, 
            Platform.Dyno.Shared.Configuration configuration,
            IMapper mapper,
            IWalletService walletService,
            IUserService userService)
        {
            _mapper = mapper;
            _helper = helper;
            _logger = logger;
            _configuration = configuration;
            _walletService = walletService;
            _userService = userService;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                _logger.LogInformation("Var : configuration: PaymentAddress/Api/WalletBlockchain/GetAll : " + _configuration.PaymentAddress);
                ResponseAPI<List<WalletDTO>>? response = _helper.GetAll($"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetAll");
                
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if(response?.StatusCode== HttpStatusCode.BadRequest)
                {
                        BadRequest(response);               
                }
                else if (response?.ObjectValue != null)
                {
                    response.ObjectValue = _walletService.GetWalletsAssignedToName(response.ObjectValue);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }



        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetAllPaged?PageNumber={pagedParameters.PageNumber}&PageSize={pagedParameters.PageSize}";
                ResponseAPI<List<WalletDTO>>? response = _helper.GetAll(Url);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.BadRequest)
                {
                    BadRequest(response);

                }
                else if (response?.ObjectValue != null)
                {
                    response.ObjectValue = _walletService.GetWalletsAssignedToName(response.ObjectValue);
                }
                return Ok(response);
               
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetTotalBalanceByUserType")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetTotalBalanceByUserType([FromQuery] UserType userType)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Wallet/GetTotalBalanceByUserType";
                _logger.LogDebug($"l URL de service est : {Url}");
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                    { "userType", userType.ToString()}
                    };
                ResponseAPI<decimal> response = _helper.GetTotalBalanceByUserType(Url, queryParams);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<decimal>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else if (response?.StatusCode == HttpStatusCode.OK)
                {
                    return Ok(response);

                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetTotalBalanceByUserType)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                string Url = $"{_configuration.PaymentAddress}/Api/Wallet/Get";

                ResponseAPI<WalletDTO>? response = _helper.Get(Url,id);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in PaymentAddress Micro-Service"
                    };
                    return StatusCode(500, response);
                }
                else
                {
                    if (response?.StatusCode == HttpStatusCode.OK)
                    {
                        if (response?.ObjectValue != null)
                        {
                            response.ObjectValue = _walletService.GetWalletAssignedToName(response.ObjectValue);
                        }
                        return Ok(response);
                    }
                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("GetUserWallets")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetUserWallets([FromHeader] string authorization)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized!"
                    });

                string Url = $"{_configuration.PaymentAddress}/Api/WalletBlockchain/GetUserWallets";
                _logger.LogInformation($"Calling payment service at: {Url}");

                // Vérification de l'URL du service de paiement
                if (string.IsNullOrEmpty(_configuration.PaymentAddress))
                {
                    _logger.LogError("Payment service address is not configured");
                    return StatusCode(500, new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = HttpStatusCode.InternalServerError,
                        ExceptionMessage = "Payment service configuration is missing"
                    });
                }

                Dictionary<string, string> queryParams = new()
                {
                    { "userId", userId }
                };

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

                // Ajout du logging détaillé
                try
                {
                    var response = await _helper.GetAllAsync(Url, queryParams, cts.Token);
                    
                    if (response == null)
                    {
                        _logger.LogError("Null response from payment service");
                        return StatusCode(500, new ResponseAPI<List<WalletDTO>>
                        {
                            StatusCode = HttpStatusCode.InternalServerError,
                            ExceptionMessage = "No response from payment service"
                        });
                    }

                    _logger.LogInformation($"Payment service response status: {response.StatusCode}");

                    if (response.StatusCode == HttpStatusCode.OK && response.ObjectValue != null)
                    {
                        response.ObjectValue = _walletService.GetWalletsAssignedToName(response.ObjectValue);
                        return Ok(response);
                    }

                    return StatusCode((int)response.StatusCode, response);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogError("Request to payment service timed out");
                    return StatusCode(408, new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = HttpStatusCode.RequestTimeout,
                        ExceptionMessage = "Payment service request timed out"
                    });
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "Failed to communicate with payment service");
                return StatusCode(503, new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = HttpStatusCode.ServiceUnavailable,
                    ExceptionMessage = $"Payment service is unavailable: {ex.Message}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while processing wallet request");
                return StatusCode(500, new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An unexpected error occurred while processing your request"
                });
            }
        }


        [Route("GetUserDetailsByWallet/{walletId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserDetailsByWallet(Guid walletId)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    string Url = $"{_configuration.PaymentAddress}/Api/Wallet/Get/{walletId}";
                    ResponseAPI<WalletDTO>? response = _helper.Get(Url);
                    if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                    {
                        return StatusCode(500, new ResponseAPI<UserDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.InternalServerError,
                            ExceptionMessage = "An error occurred in payment Micro-Service"
                        });

                    }
                    else if (response?.StatusCode == HttpStatusCode.BadRequest)
                    {
                        BadRequest(response);
                    }
                    ResponseAPI<UserDTO> user = _userService.Get(response.ObjectValue.AssignedToId);
                    return Ok(user);
                }

                return Unauthorized(new ResponseAPI<MacAddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetUserDetailsByWallet)}");
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        #endregion

        #region Update
        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromHeader] string authorization,WalletDTO walletDTO)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }
                string Url = $"{_configuration.PaymentAddress}/Api/Wallet/Update";
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                    {
                        { "userId", userId }
                    };
                ResponseAPI<WalletDTO>? response = _helper.Patch(Url, walletDTO);
                if (response == null || response.StatusCode == HttpStatusCode.InternalServerError)
                {
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.InternalServerError,
                        ExceptionMessage = "An error occurred in payment Micro-Service"
                    };
                    return StatusCode(500, response);

                }
                else if (response?.StatusCode == HttpStatusCode.BadRequest)
                {
                    BadRequest(response);
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion
    }
}
