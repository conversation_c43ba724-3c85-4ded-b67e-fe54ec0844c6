@echo off
echo 🔧 TEST DE COMPILATION - SERVICE DE TRANSACTION DISTRIBUÉE
echo =========================================================

echo 📁 Répertoire actuel: %CD%

echo.
echo 🔨 Compilation en cours...
dotnet build --verbosity normal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ COMPILATION RÉUSSIE!
    echo.
    echo 🚀 Démarrage du service...
    start /B dotnet run
    
    echo ⏳ Attente du démarrage (10 secondes)...
    timeout /t 10 /nobreak >nul
    
    echo.
    echo 🔍 Vérification du service...
    netstat -an | findstr ":7018"
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ SERVICE DÉMARRÉ AVEC SUCCÈS!
        echo 🌐 Service disponible sur: http://localhost:7018
        echo.
        echo 📋 Endpoints de test:
        echo   - GET  /api/Wallet/GetMyWallets
        echo   - POST /api/DistributedTransaction/ExecuteSecure
        echo   - GET  /api/DistributedTransaction/Status/{id}
    ) else (
        echo ❌ Le service ne semble pas écouter sur le port 7018
    )
) else (
    echo.
    echo ❌ ERREURS DE COMPILATION DÉTECTÉES
    echo 🔧 Vérifiez les erreurs ci-dessus
)

echo.
echo 📊 Test terminé.
pause
