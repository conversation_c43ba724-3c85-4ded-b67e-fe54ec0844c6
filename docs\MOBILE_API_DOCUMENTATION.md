# 📱 DOCUMENTATION APIs MOBILES - PLATFORM DYNO

## 🎯 **OVERVIEW**

Cette documentation détaille les APIs optimisées pour l'application mobile Platform Dyno, permettant aux employés d'accéder à leurs avantages sociaux.

## 🔐 **AUTHENTIFICATION**

### **Base URL**
```
Production: https://api.dyno-motiva.com
Staging: https://staging-api.dyno-motiva.com
Development: http://localhost:7274
```

### **Headers Requis**
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
Language: en|fr
User-Agent: DynoMobile/1.0.0 (iOS|Android)
```

## 🚀 **ENDPOINTS AUTHENTIFICATION**

### **1. Vérification Numéro de Téléphone**
```http
GET /api/AuthClient/CheckNumber/{countryCode}/{phoneNumber}/{macAddress}
```

**Paramètres :**
- `countryCode` : Code pays (ex: +33, +216)
- `phoneNumber` : Numéro sans code pays
- `macAddress` : Adresse MAC du device

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": true,
  "exceptionMessage": "The phone number found successfully!"
}
```

### **2. Inscription Utilisateur**
```http
POST /api/AuthClient/Register
```

**Body :**
```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "phoneNumber": "123456789",
  "countryCode": "+33",
  "password": "SecurePass123!",
  "pinCode": "1234",
  "dateOfBirth": "1990-01-01",
  "gender": "Male",
  "macAddress": "AA:BB:CC:DD:EE:FF"
}
```

**Réponse :**
```json
{
  "statusCode": 201,
  "objectValue": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "base64-refresh-token",
    "expiredDate": "2024-12-31T23:59:59Z",
    "userProfile": {
      "id": "uuid",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+33123456789",
      "picture": null,
      "isVerified": false
    }
  }
}
```

### **3. Connexion**
```http
POST /api/AuthClient/login
```

**Body :**
```json
{
  "phoneNumber": "123456789",
  "countryCode": "+33",
  "password": "SecurePass123!",
  "macAddress": "AA:BB:CC:DD:EE:FF"
}
```

### **4. Connexion Biométrique**
```http
GET /api/AuthClient/BiometricLogin/{password}
```

**Headers :** `Authorization: Bearer {token}`

### **5. Déconnexion**
```http
GET /api/AuthClient/Logout
```

### **6. Refresh Token**
```http
POST /api/AuthClient/RefreshToken
```

**Body :**
```json
{
  "token": "current-jwt-token",
  "refreshToken": "refresh-token",
  "expiredDate": "2024-12-31T23:59:59Z"
}
```

### **7. Mot de Passe Oublié**
```http
GET /api/AuthClient/ForgetPassword/{countryCode}/{phoneNumber}
```

### **8. Vérification Code OTP**
```http
POST /api/AuthClient/VerifyOTPCode
```

**Body :**
```json
{
  "phoneNumber": "123456789",
  "countryCode": "+33",
  "code": "123456"
}
```

### **9. Réinitialisation Mot de Passe**
```http
POST /api/AuthClient/ResetPassword
```

**Body :**
```json
{
  "phoneNumber": "123456789",
  "countryCode": "+33",
  "newPassword": "NewSecurePass123!",
  "code": "123456"
}
```

## 💰 **ENDPOINTS TRANSACTIONS**

### **1. Liste des Transactions (Paginée)**
```http
GET /api/Transaction/GetAllPaged?pageSize=20&pageNumber=1&order=DESC
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": [
    {
      "id": "uuid",
      "amount": 25.50,
      "currency": "TND",
      "type": "Purchase",
      "status": "Completed",
      "description": "Achat chez Carrefour",
      "transactionDate": "2024-01-15T14:30:00Z",
      "merchantName": "Carrefour",
      "reference": "TXN-123456"
    }
  ]
}
```

**Headers de Réponse :**
```http
X-Pagination: {
  "totalCount": 150,
  "pageSize": 20,
  "currentPage": 1,
  "totalPages": 8,
  "hasNext": true,
  "hasPrevious": false
}
```

### **2. Transactions par Wallet**
```http
GET /api/Transaction/GetWalletTransactions?walletId={uuid}&sort=TransactionDate&order=DESC
```

### **3. Créer Transaction**
```http
POST /api/Transaction/Create?pinCode={pinCode}
```

**Body :**
```json
{
  "amount": 50.00,
  "description": "Achat restaurant",
  "merchantId": "uuid",
  "walletId": "uuid"
}
```

## 💳 **ENDPOINTS WALLETS**

### **1. Mes Wallets**
```http
GET /api/Wallet/GetMyWallets
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": [
    {
      "id": "uuid",
      "balance": 150.75,
      "currency": "TND",
      "type": "Main",
      "isActive": true,
      "lastUpdated": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### **2. Solde Total**
```http
GET /api/Wallet/GetTotalBalance
```

## 🏢 **ENDPOINTS ENTREPRISES**

### **1. Entreprises Récentes (Paginée)**
```http
GET /api/Company/GetRecents?pageSize=20&pageNumber=1
```

**Réponse :**
```json
{
  "statusCode": 200,
  "objectValue": {
    "data": [
      {
        "id": "uuid",
        "name": "Carrefour Tunisia",
        "logo": "https://dynofiles.s3.amazonaws.com/Images/carrefour-logo.png",
        "description": "Grande surface alimentaire",
        "category": "Retail",
        "cashbackRate": 5.0,
        "isActive": true,
        "address": "Avenue Habib Bourguiba, Tunis",
        "phoneNumber": "+216 71 123 456"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalPages": 5,
      "totalCount": 95,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

## 🔔 **ENDPOINTS NOTIFICATIONS**

### **1. Mes Notifications**
```http
GET /api/Notification/GetMyNotifications?pageSize=20&pageNumber=1
```

### **2. Marquer comme Lu**
```http
PUT /api/Notification/MarkAsRead/{notificationId}
```

## 👤 **ENDPOINTS PROFIL UTILISATEUR**

### **1. Mon Profil**
```http
GET /api/User/GetMyProfile
```

### **2. Mettre à Jour Profil**
```http
PUT /api/User/UpdateProfile
```

**Body :**
```json
{
  "fullName": "John Doe Updated",
  "email": "<EMAIL>",
  "dateOfBirth": "1990-01-01",
  "picture": "base64-image-data"
}
```

Cette documentation couvre les endpoints essentiels pour l'application mobile Platform Dyno.
