﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Interfaces
{
    public interface IGenericRepository : IAsyncDisposable
    {
        #region read
        Task<List<string>> ExecuteReadListAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null);

        Task<List<Dictionary<string, object>>> ExecuteReadDictionaryAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null);

        Task<T> ExecuteReadScalarAsync<T>(string query, IDictionary<string, object>? parameters = null);
        #endregion
        #region write
        Task<List<Dictionary<string, object>>> ExecuteWriteDictionaryAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null);
        Task<T> ExecuteWriteTransactionAsync<T>(string query, IDictionary<string, object>? parameters = null);
        Task<List<T>> ExecuteWriteRangeTransactionAsync<T>(string query, string returnObjectKey, IDictionary<string, object>? parameters = null);
        #endregion
    }
}

