﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.MultiLanguageConfig
{
    public class EmailContentFR
    {
        public string NewDeviceEmail { get; set; } = string.Empty;
        public string ResetPasswordEmail { get; set; } = string.Empty;

        public string NewUserAdminEmail { get; set; } = string.Empty;

        public EmailContentFR()
        {
            string jsonFilePath = "../../../Configuration/emailContentFR.json";

            if (File.Exists(jsonFilePath))
            {
                var json = File.ReadAllText(jsonFilePath);
                EmailEN jsonData = JsonConvert.DeserializeObject<EmailEN>(json);

                NewDeviceEmail = jsonData.NewDeviceEmail;
                ResetPasswordEmail = jsonData.ResetPasswordEmail;
                NewUserAdminEmail = jsonData.NewUserAdminEmail;
            }
            else
            {
                // Handle the case when the JSON file doesn't exist.
                // You can set default values or throw an exception.
            }
        }

        private class EmailEN
        {
            public string NewDeviceEmail { get; set; } = string.Empty;
            public string ResetPasswordEmail { get; set; } = string.Empty;
            public string NewUserAdminEmail { get; set; } = string.Empty;
        }
    }
}
