﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Notification
{
    [Route("api/[controller]")]
    [ApiController]
    public class SubscriberDeviceController : ControllerBase
    {
        private readonly ISubscriberDeviceService _subscriberDeviceService;
        private readonly ILogger<SubscriberDeviceController> _logger;

        public SubscriberDeviceController(ISubscriberDeviceService subscriberDeviceService,
            ILogger<SubscriberDeviceController> logger)
        {
            _subscriberDeviceService = subscriberDeviceService;
            _logger = logger;
        }


        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Add([FromBody] SubscriberDeviceDTO subscriberDevice)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? createUserId = HttpContext.User.FindFirstValue("Id");

                ResponseAPI<SubscriberDeviceDTO> response = _subscriberDeviceService.Create(subscriberDevice, creatorUserId: createUserId != null ? new Guid(createUserId) : null);
                return Ok(response);


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("RegistrationDevice")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult RegistrationDevice([FromBody] SubscriberDeviceDTO subscriberDevice)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<UserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? userId = HttpContext.User.FindFirstValue("Id");

                subscriberDevice.UserEntityId = userId != null ? new Guid(userId) : null;

                ResponseAPI<SubscriberDeviceDTO> response = _subscriberDeviceService.RegistrationDevice(subscriberDevice);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
    }
}
