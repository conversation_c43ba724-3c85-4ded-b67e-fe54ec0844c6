﻿using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DataModel.User
{
    [Table("UserOTP")]
    public class UserOTPEntity : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Code is required")]
        [RegularExpression(@"^\d{4}$", ErrorMessage = "The 'Code' must be a 4-digit number.")]
        public int Code { get; set; }
        public bool IsConfirmed { get; set; }
        #endregion

        #region Structure
        [ForeignKey("UserId")]
        public UserEntity? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion
    }
}
