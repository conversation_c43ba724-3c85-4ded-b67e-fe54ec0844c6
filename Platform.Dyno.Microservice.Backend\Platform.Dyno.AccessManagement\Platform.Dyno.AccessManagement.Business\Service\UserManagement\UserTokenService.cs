﻿using AutoMapper;
using Platform.Dyno.AccessManagement.Business.IService;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class UserTokenService : IUserTokenService
    {
        private readonly IUnitOfWork<UserTokenEntity> _userTokenRepository;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly string _userTokenCacheKey = RedisCacheKey.UserTokenCacheKey;
        public UserTokenService(IUnitOfWork<UserTokenEntity> userTokenRepository,
            IMapper mapper,
            IRedisCacheService cache) 
        { 
            _userTokenRepository = userTokenRepository;
            _mapper = mapper;
            _cache = cache;
        }

        #region Get

        public ResponseAPI<List<UserTokenDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            List<UserTokenDTO>? userTokensDTO = _cache.GetData<List<UserTokenDTO>>(_userTokenCacheKey);
            if (userTokensDTO == null || userTokensDTO.Count == 0)
            {
                List<UserTokenEntity> userTokenEntities = (List<UserTokenEntity>)_userTokenRepository.Repository.GetAll(includes: new List<string> { "User" }, orderBy: user => user.OrderByDescending(user => user.LastModificationTime));
                List<UserTokenBM> userTokens = _mapper.Map<List<UserTokenBM>>(userTokenEntities);
                userTokensDTO = _mapper.Map<List<UserTokenDTO>>(userTokens);

            }
            _cache.SetData(_userTokenCacheKey, userTokensDTO, DateTimeOffset.UtcNow.AddDays(1));

            ResponseAPI<List<UserTokenDTO>> response = new ResponseAPI<List<UserTokenDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = userTokensDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<UserTokenDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<UserTokenDTO>> userTokensDTO = GetAll(companyId, userType);
            PagedList<UserTokenDTO>? pagedList = null;
            if (userTokensDTO.ObjectValue != null)
            {
                pagedList = PagedList<UserTokenDTO>.ToGenericPagedList(userTokensDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<UserTokenDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<UserTokenDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<UserTokenDTO>> userTokensDTO = GetAll(companyId, userType);
            PagedList<UserTokenDTO>? pagedList = null;
            if (userTokensDTO.ObjectValue != null)
            {
                pagedList = PagedList<UserTokenDTO>.ToGenericPagedList(userTokensDTO.ObjectValue.Where(userToken => userToken.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<UserTokenDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<UserTokenDTO> Get(Guid id)
        {
            UserTokenDTO? userTokenDTO = GetAll().ObjectValue?.Where(userToken => userToken.Id == id).FirstOrDefault();
            return new ResponseAPI<UserTokenDTO>()
            {
                StatusCode = userTokenDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = userTokenDTO == null ? $"Token with id {id} Not Found !" : null,
                ObjectValue = userTokenDTO
            };
        }

        public ResponseAPI<List<UserTokenDTO>> Get(Func<UserTokenDTO, bool> expression)
        {
            List<UserTokenDTO>? userTokensDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<UserTokenDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = userTokensDTO
            };
        }

        

        #endregion

        #region Create
        public ResponseAPI<UserTokenDTO> Create(UserTokenDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            UserTokenBM userToken = _mapper.Map<UserTokenBM>(dtoObject);

            #region changed data
            userToken.Id = Guid.NewGuid();
            RefDataService<UserTokenBM>.CreateRefData(userToken, userToken.Id);
            #endregion

            UserTokenEntity userEntity = _mapper.Map<UserTokenEntity>(userToken);
            _userTokenRepository.Repository.Insert(userEntity);
            _userTokenRepository.Save();
            dtoObject.Id = userEntity.Id;

            if (updateCache)
            {
                _cache.RemoveData(_userTokenCacheKey);
            }
            return new ResponseAPI<UserTokenDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        #endregion

        #region Update
        public ResponseAPI<UserTokenDTO> Update(UserTokenDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (Get(dtoObject.Id).ObjectValue != null)
            {

                UserTokenBM userToken = _mapper.Map<UserTokenBM>(dtoObject);

                #region RefData
                RefDataService<UserTokenBM>.UpdateRefData(userToken, userToken.Id);
                #endregion

                UserTokenEntity userEntity = _mapper.Map<UserTokenEntity>(userToken);
                _userTokenRepository.Repository.Update(userEntity);
                _userTokenRepository.Save();
                dtoObject.Id = userEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_userTokenCacheKey);
                }

                return new ResponseAPI<UserTokenDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<UserTokenDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Token with id {dtoObject.Id} Not Found !"
            };
        }
        #endregion

        #region Delete

        public ResponseAPI<UserTokenDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            UserTokenDTO? userTokenDTO = Get(id).ObjectValue;
            if (userTokenDTO != null)
            {
                UserTokenBM userToken = _mapper.Map<UserTokenBM>(userTokenDTO);

                #region Refdata
                RefDataService<UserTokenBM>.DeleteRefData(userToken, userToken.Id);
                #endregion

                UserTokenEntity userTokenEntity = _mapper.Map<UserTokenEntity>(userToken);
                _userTokenRepository.Repository.Update(userTokenEntity);
                _userTokenRepository.Save();

                return new ResponseAPI<UserTokenDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userTokenDTO
                };
            }

            return new ResponseAPI<UserTokenDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Token with id {id} Not Found !"
            };
        }

        public ResponseAPI<UserTokenDTO> Remove(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            UserTokenDTO? userTokenDTO = Get(id).ObjectValue;
            if (userTokenDTO != null)
            {
                _userTokenRepository.Repository.Delete(id);
                _userTokenRepository.Save();

                return new ResponseAPI<UserTokenDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userTokenDTO
                };
            }

            return new ResponseAPI<UserTokenDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"Token with id {id} Not Found !"
            };
        }


        #endregion
    }
}
