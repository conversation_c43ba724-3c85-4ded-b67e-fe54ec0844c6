﻿using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.BusinessModel.CashBack
{
    public class FailedCashBackBM : ReferentialData
    {
        public Guid Id { get; set; }
        public Guid CashbackId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public CashBackBM? Cashback { get; set; }
        public new CashBackStatus Status { get; set; } = CashBackStatus.Cancelled;
    }
}
