﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Platform.Dyno.Shared.RefData;

namespace Platform.Dyno.AccessManagement.BusinessModel.Role
{
    public class PermissionBM : ReferentialData
    {
        #region Data
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public bool IsLeaf { get; set; } = false;
        public bool IsRoot { get; set; } = false;
        public bool? PartialSelected { get; set; }
        #endregion

        #region Structure
        public RoleBM? Role { get; set; }
        public Guid? RoleId { get; set; }
        public ICollection<PermissionBM>? Permissions { get; set; }
        #endregion
    }
}
