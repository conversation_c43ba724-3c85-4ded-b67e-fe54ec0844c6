﻿using PhoneNumbers;
using static QRCoder.PayloadGenerator;

namespace Platform.Dyno.Shared.SharedClass.PhoneNumber;

public class PhoneNumberValidator
{
    private PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.GetInstance();
    public bool IsValidPhoneNumber(string phoneNumber, string countryCode)
    {
        try
        {
            var number = phoneNumberUtil.Parse(phoneNumber, countryCode);
            return phoneNumberUtil.IsValidNumberForRegion(number, countryCode);
        }
        catch (NumberParseException)
        {
            return false;
        }
    }
}
