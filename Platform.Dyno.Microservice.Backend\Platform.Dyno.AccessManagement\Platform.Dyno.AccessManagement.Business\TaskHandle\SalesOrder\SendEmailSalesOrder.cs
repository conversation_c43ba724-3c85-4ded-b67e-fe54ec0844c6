﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using static Camunda.Api.Client.Filter.FilterInfo;
using Platform.Dyno.Shared.SharedClass.EmailTemplate;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("Send_Email_SO")]
    public class SendEmailSalesOrder : IExternalTaskHandler
    {
        private readonly ILogger<SendEmailSalesOrder> _logger;
        private readonly IEmailingService _emailingService;
        private readonly ICompanyService _companyService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public SendEmailSalesOrder(
        ILogger<SendEmailSalesOrder> logger,
        IEmailingService emailingService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        ICompanyService companyService)

        {
            _logger = logger;
            _emailingService = emailingService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _companyService = companyService;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
                //SalesOrderDTO salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

                string? error = null;

                if (externalTask.Variables.TryGetValue("Error_Send_Email_SO", out var errorVar))
                {
                    if (errorVar is StringVariable stringVar)
                    {
                        error = stringVar.Value;
                    }
                    else if (errorVar is JsonVariable jsonVar)
                    {
                        error = (string?)jsonVar.Value; // Si l'erreur est stockée en JSON
                    }
                    else
                    {
                        _logger.LogWarning("Type inattendu pour Error_Send_Email_SO : {Type}", errorVar.GetType().Name);
                    }
                }
                else
                {
                    _logger.LogDebug("Aucune erreur trouvée dans Error_Send_Email_SO");
                }
                // string? PDFLink = externalTask.Variables["PDFLink"].Value?.ToString();
                string? PDFLink = null;

                if (externalTask.Variables.TryGetValue("PDFLink", out var varItem))
                {
                    if (varItem is StringVariable strVar && !string.IsNullOrWhiteSpace(strVar.Value))
                    {
                        PDFLink = strVar.Value;
                        _logger.LogInformation("Lien PDF récupéré : {PDFLink}", PDFLink);
                    }
                    else
                    {
                        _logger.LogWarning("PDFLink présent mais format invalide");
                    }
                }
                else
                {
                    _logger.LogError("Variable PDFLink absente");
                }


                if (salesOrderDTO == null)
                {
                    outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(false));
                    outputVariables.Add("Error_Send_Email_SO", new StringVariable("json conversion failed"));

                }
                else if (PDFLink == null)
                {
                    outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(false));
                    outputVariables.Add("Error_Send_Email_SO", new StringVariable("PDF link conversion failed"));
                }
                else
                {
                    var company = _companyService.Get(salesOrderDTO.CompanyId);
                    if (company.ObjectValue != null)
                    {
                        var emailBody = EmailTemplateEN.SalesOrderEmailbody;
                        var content = emailBody.Replace("#URL#", PDFLink).Replace("[type_of_action]", "Dynos");
                        var response = _emailingService.sendSalesOrderEmail(company.ObjectValue.Email, content);
                        if (response != null && response.StatusCode == System.Net.HttpStatusCode.Accepted)
                        {
                            outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(true));
                        }
                        else
                        {
                            outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(false));
                            outputVariables.Add("Error_Send_Email_SO", new JsonVariable(JsonSerializer.Serialize(response)));
                        }
                    }
                    else
                    {
                        outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(false));
                        outputVariables.Add("Error_Send_Email_SO", new JsonVariable(JsonSerializer.Serialize(company)));
                    }

                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_Send_Email_SO", new BooleanVariable(false));
                outputVariables.Add("Error_Send_Email_SO", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
