#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["Platform.Dyno.Notification/Platform.Dyno.Notification.csproj", "Platform.Dyno.Notification/"]
RUN dotnet restore "Platform.Dyno.Notification/Platform.Dyno.Notification.csproj"
COPY . .
WORKDIR "/src/Platform.Dyno.Notification"
RUN dotnet build "Platform.Dyno.Notification.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Platform.Dyno.Notification.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Platform.Dyno.Notification.dll"]