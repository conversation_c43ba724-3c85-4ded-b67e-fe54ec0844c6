﻿using Camunda.Worker;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SI_Notification_Transaction_Failed")]
    public class SendNotifTransactionFailed : IExternalTaskHandler
    {
        private readonly ISignalRNotificationService _signalRNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ConfigurationDefaultId _configuration;
        public SendNotifTransactionFailed(ISignalRNotificationService signalRNotificationService,
            ICompanyService companyService,
            ConfigurationDefaultId configuration)
        {
            _signalRNotificationService = signalRNotificationService;
            _companyService = companyService;
            _configuration = configuration;
        }
        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
            SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
            //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());

            if (salesInvoiceDTO != null)
            {
                CompanyDTO? superCompany = _companyService.Get(_configuration.CompanyId).ObjectValue;
                if (superCompany != null)
                {
                    SignalRNotificationDTO notificationDTO = new SignalRNotificationDTO
                    {
                        Title = "Transaction Failed",
                        Message = "Transaction Failed in Sales invoice !",
                        SendToId = new List<CompanyDTO> { superCompany }

                    };
                    _signalRNotificationService.Create(notificationDTO, companyId: superCompany.Id);
                }

            }

            var completeResult = new CompleteResult();
            return completeResult;
        }
    }
}
