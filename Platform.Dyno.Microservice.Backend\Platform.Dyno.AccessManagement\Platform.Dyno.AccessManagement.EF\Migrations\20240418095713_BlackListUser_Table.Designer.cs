﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Platform.Dyno.AccessManagement.EF;

#nullable disable

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    [DbContext(typeof(ContextDB))]
    [Migration("20240418095713_BlackListUser_Table")]
    partial class BlackListUser_Table
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("MessageNotificationsId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SubscribersId")
                        .HasColumnType("uuid");

                    b.HasKey("MessageNotificationsId", "SubscribersId");

                    b.HasIndex("SubscribersId");

                    b.ToTable("MessageNotificationEntitySubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FullAddress")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSaved")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("MacAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("MacAddress");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("FeeAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PaymentDetailsId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("ValidationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("WalletBallance")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PaymentDetailsId");

                    b.ToTable("CashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CashbackId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CashbackId");

                    b.ToTable("FailedCashBack");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("CategoryType")
                        .HasColumnType("integer");

                    b.Property<double?>("ClientFeePercentage")
                        .HasColumnType("double precision");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EntrepriseType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("RNECode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ServiceType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TaxCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("Company");

                    b.HasData(
                        new
                        {
                            Id = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            ClientFeePercentage = 11.0,
                            Email = "<EMAIL>",
                            EntrepriseType = 0,
                            Name = "Dyno & Motiva Systems",
                            PhoneNumber = "+21699001000",
                            RNECode = "1803838P",
                            ServiceType = 0,
                            Status = 0,
                            TaxCode = "000 M A 1803838P",
                            WalletId = new Guid("00000000-0000-0000-0000-000000000000")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("PaymentDelay")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("RIB")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("ShopFeePercentage")
                        .HasColumnType("double precision");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.Property<Guid>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("EmployeeId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("GroupId");

                    b.HasIndex("UserId");

                    b.ToTable("Employee");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Property<Guid>("GroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("GroupId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Group");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.Property<Guid?>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uuid");

                    b.HasKey("GroupId", "TicketId");

                    b.HasIndex("TicketId");

                    b.ToTable("GroupTicket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Logger.LogErrorEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("API")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Error")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<int>("Microservice")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("LogError");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.HasKey("CompanyId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("CompanyNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MessageGroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Protocol")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Rule")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TopicArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MessageNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatorType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsSeen")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SignalRNotification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EndPoint")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EndPointArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TargetArn")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserEntityId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserEntityId");

                    b.ToTable("SubscriberDeviceEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsLeaf")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool?>("PartialSelected")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("PermissionEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PermissionEntityId");

                    b.HasIndex("RoleId");

                    b.ToTable("Permission");

                    b.HasData(
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea4"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b32f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f5252a"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e0f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6c42303e-9466-40a3-9f04-59db7efd39c3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ee424456-2dd5-448b-a24a-a4ffa23db15f"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("91ee99a2-9756-4fdd-acf8-dc6aceca52a7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("d5cae7c2-e640-417b-98d5-d559aaa87539"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c697a4f3-c352-4279-8845-f7f0ca040b14"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c186861f-08c1-4463-8d63-5484c5652918"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f20b2983-6d9b-4fe0-8ee8-5a0255d5a2e0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a9ccc7d0-0765-4a94-9bd4-385631825d50"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("e2bf1834-becd-408f-bc84-56b826165271"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PermissionEntityId = new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea0"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1706f554-764c-4314-91ba-56ab40d0b320"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("fd100341-f14e-4e90-908b-5fefe8f52520"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("a0568a01-ae4e-4055-9262-3a817fb76e00"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PermissionEntityId = new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("25e5a90f-6287-4033-829a-ecc417e54343"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ceceb370-cb0d-49ab-a458-301078a9d126"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("77b55a22-850d-4177-9a00-974f6d7107e2"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0d2c1dc2-5405-4b5d-bb08-f04a90922e10"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PermissionEntityId = new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PermissionEntityId = new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("602aa4d2-2f12-40c2-bb07-1a22621d725d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("709b16ea-18c1-4a87-92bb-e9c9c88dbcee"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7dec5f33-050e-42fc-9643-08461b2f878d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("3c0e20f8-fd44-43d9-8ed8-b9ec25a7add3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PermissionEntityId = new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"),
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("1447408f-467f-4c15-88d8-df21affa0d07"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c5a8f77d-2716-463f-83f7-13424066f239"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("1447408f-467f-4c15-88d8-df21affa0d07"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4bdfebd8-8b39-418b-9a57-b2e617d0dc92"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("1447408f-467f-4c15-88d8-df21affa0d07"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("36fbce42-4c38-4a74-ac67-bb882983c80b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("1447408f-467f-4c15-88d8-df21affa0d07"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("42b51296-900b-429c-a89d-0cd0b5b7c9c3"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("1447408f-467f-4c15-88d8-df21affa0d07"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4b0eb34d-075a-4628-bd0b-dc52f7eec0a2"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("135e4f25-022c-47a3-9b0f-79e95d75074e"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("68aa16e1-3dbc-4b69-828d-966e519faa06"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b96f629c-5f5a-4945-902b-1ff7b3df6443"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("ed688d1c-88bf-4035-836f-ac0ed8b93411"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("608648d7-8d75-486a-8b19-fa329b4ca5cf"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("592eef25-0a3a-4d97-bcea-98cf0ec25340"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("0e21cfb3-60e4-409a-b438-a8b60d236473"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"),
                            RoleId = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Company Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("2811492f-da88-4933-af5a-902094630cc1"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("f7c360f8-10ba-45bd-b1e8-0172e8c4b060"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c00d081d-6d82-4a47-a343-11ecf03cb199"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6fdbc3ad-b568-490d-a349-97a5796b5ffb"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Company",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = true,
                            Name = "Auth Management",
                            PartialSelected = false,
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("2d283c82-87ad-4d20-b039-fc50734a07b7"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("b6186c1b-241e-48da-aff1-7880140bfbbf"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Users",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("c2eb4422-ceb6-4209-b9d3-2d35ccdd0b9d"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("5f43caba-f688-44ed-8ce1-a207e262a298"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete User",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = false,
                            IsRoot = false,
                            Name = "Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("74af2506-958e-41c6-9e13-764ae5990d34"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Create New Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("50a35b63-cff0-46d6-9ea7-4c4119bf2147"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "View All Roles",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("887be67b-1c47-4273-ac91-a4dcdecbd8ac"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Update Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        },
                        new
                        {
                            Id = new Guid("6e9bd497-728b-4101-bf4e-5c8c29cce61b"),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            IsLeaf = true,
                            IsRoot = false,
                            Name = "Delete Role",
                            PartialSelected = false,
                            PermissionEntityId = new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"),
                            RoleId = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            Status = 0
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                            ConcurrencyStamp = "a56ced52-2705-47c0-89d8-a280d9b87d87",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2600),
                            Name = "Default",
                            NormalizedName = "DEFAULT",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            ConcurrencyStamp = "362e1e97-1d36-4305-a611-97f65d685d57",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2610),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "SuperAdmin",
                            NormalizedName = "SUPERADMIN",
                            Status = 0,
                            UserType = 3
                        },
                        new
                        {
                            Id = new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                            ConcurrencyStamp = "fffa58c2-afee-4089-b7aa-37f3b16e46af",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2617),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Admin",
                            NormalizedName = "ADMIN",
                            Status = 0,
                            UserType = 1
                        },
                        new
                        {
                            Id = new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                            ConcurrencyStamp = "26f3be49-53e9-4c5a-a2ad-843d937877e9",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2623),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Shop Admin",
                            NormalizedName = "SHOP ADMIN",
                            Status = 0,
                            UserType = 0
                        },
                        new
                        {
                            Id = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                            ConcurrencyStamp = "b73daf13-c629-4a54-b7a8-f3e1f0326fa8",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2628),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Client",
                            NormalizedName = "CLIENT",
                            Status = 0,
                            UserType = 4
                        },
                        new
                        {
                            Id = new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                            ConcurrencyStamp = "acd068b0-8e5a-4a3b-a7ac-4da021b16a77",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2634),
                            CreatorUserId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            Name = "Cashier",
                            NormalizedName = "CASHIER",
                            Status = 0,
                            UserType = 2
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("RoleId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("RoleUser");

                    b.HasData(
                        new
                        {
                            RoleId = new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                            UserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71")
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SalesOrderId");

                    b.ToTable("FailedSalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<Guid>("SalesOrderId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.HasIndex("SalesOrderId")
                        .IsUnique();

                    b.ToTable("SalesInvoice");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<double>("DynoAmount")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<double>("NetAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessInstanceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProductType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<double>("VATAmount")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.ToTable("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Property<Guid>("TicketId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<Guid?>("CompanyEntityId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutomatic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("PeriodType")
                        .HasColumnType("integer");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<double>("TotalAmount")
                        .HasColumnType("double precision");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("TicketId");

                    b.HasIndex("CompanyEntityId");

                    b.ToTable("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.BlackListedUserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("BlockedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("BlockedUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UnblockedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UnblockedReason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("UnblockedUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("BlackListedUserEntity");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<int>("AuthentificationSource")
                        .HasColumnType("integer");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<int?>("Gender")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("text");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Picture")
                        .HasColumnType("text");

                    b.Property<string>("PinCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            AccessFailedCount = 0,
                            AuthentificationSource = 0,
                            CompanyId = new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                            ConcurrencyStamp = "3357e32c-dddc-4cdb-98dc-1cb924504561",
                            CountryCode = "",
                            CreationTime = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3100),
                            CreatorUserId = new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                            DateOfBirth = new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3099),
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            FullName = "Dyno Root",
                            Gender = 2,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "CFBC54C0-97CE-40FF-9281-79D09713CB71",
                            PasswordHash = "AQAAAAEAACcQAAAAEIioYzLxCw9ey+k42CPIPBp3+kaOLV0ME0JBlFgmfiV82HnA7uBGLAOGNxxW+xuoHQ==",
                            PhoneNumberConfirmed = true,
                            PinCode = "ZFxn5dSGlaMFoTfAET4RbyhYkvs39oCKFHsXjahuXSg=",
                            Status = 0,
                            TwoFactorEnabled = false,
                            UserName = "cfbc54c0-97ce-40ff-9281-79d09713cb71",
                            UserType = 3
                        });
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Code")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserOTP");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatorUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeleterUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiredDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifierUserEmail")
                        .HasColumnType("text");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("RefreshToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserToken");
                });

            modelBuilder.Entity("MessageNotificationEntitySubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.MessageNotificationEntity", null)
                        .WithMany()
                        .HasForeignKey("MessageNotificationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", null)
                        .WithMany()
                        .HasForeignKey("SubscribersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.AddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Addresses")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("Addresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Address.MacAddressEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("MacAddresses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", "PaymentDetails")
                        .WithMany("CashBacks")
                        .HasForeignKey("PaymentDetailsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("PaymentDetails");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.CashBack.FailedCashBackEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.CashBack.CashBackEntity", "Cashback")
                        .WithMany()
                        .HasForeignKey("CashbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cashback");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("PaymentDetails")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Employee.EmployeeEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("Employees")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("Employees")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupTicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", "Group")
                        .WithMany("GroupTickets")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", "Ticket")
                        .WithMany("GroupTickets")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Ticket");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.CompanyNotifEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", "Notification")
                        .WithMany("CompanyNotifs")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SubscriberDeviceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("SubscriberDevices")
                        .HasForeignKey("UserEntityId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", null)
                        .WithMany("Permissions")
                        .HasForeignKey("PermissionEntityId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("Permissions")
                        .HasForeignKey("RoleId");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleUserEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", "Role")
                        .WithMany("RoleUsers")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("RoleUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.FailedSalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithMany()
                        .HasForeignKey("SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesInvoices")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "DocumentId");

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "SalesOrder")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesInvoiceEntity", "SalesOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Document");

                    b.Navigation("SalesOrder");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", "Company")
                        .WithMany("SalesOrders")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Reporting.DocumentsEntity", "Document")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.SalesOrder.SalesOrderEntity", "DocumentId");

                    b.Navigation("Company");

                    b.Navigation("Document");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", null)
                        .WithMany("Tickets")
                        .HasForeignKey("CompanyEntityId");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.BlackListedUserEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithOne()
                        .HasForeignKey("Platform.Dyno.AccessManagement.DataModel.User.BlackListedUserEntity", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserOTPEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("UserOTPs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserTokenEntity", b =>
                {
                    b.HasOne("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", "User")
                        .WithMany("UserTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.CompanyEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CompanyNotifs");

                    b.Navigation("Employees");

                    b.Navigation("PaymentDetails");

                    b.Navigation("SalesInvoices");

                    b.Navigation("SalesOrders");

                    b.Navigation("Tickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Company.PaymentDetailsEntity", b =>
                {
                    b.Navigation("CashBacks");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Group.GroupEntity", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Notification.SignalRNotificationEntity", b =>
                {
                    b.Navigation("CompanyNotifs");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.PermissionEntity", b =>
                {
                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Role.RoleEntity", b =>
                {
                    b.Navigation("Permissions");

                    b.Navigation("RoleUsers");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.Ticket.TicketEntity", b =>
                {
                    b.Navigation("GroupTickets");
                });

            modelBuilder.Entity("Platform.Dyno.AccessManagement.DataModel.User.UserEntity", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("MacAddresses");

                    b.Navigation("RoleUsers");

                    b.Navigation("SubscriberDevices");

                    b.Navigation("UserOTPs");

                    b.Navigation("UserTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
