﻿using Application.Interfaces;
using Application.IServices;
using Application.Models.BMs;
using Application.Security;
using AutoMapper;
using Domain.Entities;
using Platform.Dyno.Shared.Enum;
using Domain.Enums.Query;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.ResponseAPI;
using System;

namespace Application.Services
{
    public class WalletService : IWalletService
    {
        private readonly IGenericRepository _Repository;
        private readonly IMapper _mapper;

        private ILogger<WalletService> _logger;

        public WalletService(IGenericRepository Repository, ILogger<WalletService> logger, IMapper mapper)
        {
            _Repository = Repository;
            _logger = logger;
            _mapper = mapper;
        }

        #region get
        public ResponseAPI<List<WalletDTO>> GetAll(string sort = "",
            Ordering order = Ordering.Asc, int limit = 6, int skip = 0)
        {

            var query = @$"MATCH (w:Wallet) 
                                RETURN w{{.* }}
                                ORDER BY w.{sort} {order.ToString().ToUpper()}
                                SKIP $skip
                                LIMIT $limit";

            IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit }
            };

            var wallets = _Repository.ExecuteReadDictionaryAsync(query, "w", parameters).Result;
            List<WalletDTO> walletsDtO = wallets.Select(w =>
            {
                var walletEntity = _mapper.Map<WalletEntity>(w);
                var walletsBM = _mapper.Map<WalletBM>(walletEntity);
                return _mapper.Map<WalletDTO>(walletsBM);
            }).ToList();
            ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = walletsDtO,
            };
            return response;
        }

        public ResponseAPI<List<WalletDTO>> GetAllByUserType(UserType userType, string sort = "",
            Ordering order = Ordering.Asc, int limit = 6, int skip = 0)
        {

            var query = @$"MATCH (w:Wallet{{AssignedToType:$userType}}) 
                                RETURN w{{.* }}
                                ORDER BY w.{sort} {order.ToString().ToUpper()}
                                SKIP $skip
                                LIMIT $limit";

        IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "sort", sort },
                { "order", order.ToString().ToUpper() },
                { "skip", skip },
                { "limit", limit },
                { "userType",userType.ToString() }
            };

        var wallets = _Repository.ExecuteReadDictionaryAsync(query, "w", parameters).Result;
        List<WalletDTO> walletsDtO = wallets.Select(w =>
        {
            var walletEntity = _mapper.Map<WalletEntity>(w);
            var walletsBM = _mapper.Map<WalletBM>(walletEntity);
            var wallets= _mapper.Map<WalletDTO>(walletsBM);
            return wallets;

        }).ToList();
        ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
        {
            StatusCode = System.Net.HttpStatusCode.OK,
            ObjectValue = walletsDtO,
        };
            return response;
        }

        public ResponseAPI<decimal> GetTotalBalanceByUserType(UserType userType)
        {
            var query = @$"MATCH (w:Wallet{{AssignedToType:$userType}})
                   RETURN SUM(w.Balance) AS totalBalance
            ";

            var parameters = new Dictionary<string, object>
            {
                { "userType", userType.ToString()}
            };

            var result = _Repository.ExecuteReadScalarAsync<decimal>(query, parameters).Result;

            if (result != null)
            {
                ResponseAPI<decimal> response = new ResponseAPI<decimal>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = result,
                };
                return response;
            }
            else
            {
                // Handle the case when no wallets are found.
                return new ResponseAPI<decimal>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ObjectValue = 0, // You can set this to a default value or handle it accordingly.
                };
            }
        }


        public ResponseAPI<WalletDTO> Get(Guid id)
        {
            try
            {
                var query = @$"MATCH (w:Wallet{{Id: $walletId}}) 
                                RETURN w{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "walletId",id.ToString() }
            };

                var wallet = _Repository.ExecuteReadScalarAsync<Dictionary<string, object>>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Wallet with id {id} is not found",
                };
                if (wallet != null)
                {
                    WalletEntity walletEntity = _mapper.Map<WalletEntity>(wallet);
                    WalletBM walletBM = _mapper.Map<WalletBM>(walletEntity);
                    WalletDTO walletDTO = _mapper.Map<WalletDTO>(walletBM);
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.OK,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }
                return response;

            }

            catch (Exception ex)
            {
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Wallet with id {id} is not found",
                };
                return response;
            }

        }
        public ResponseAPI<List<WalletDTO>> GetUserWallets(Guid userId)
        {
            try
            {
                var query = @$"MATCH (w:Wallet{{AssignedToId: $userId}}) 
                                RETURN w{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                    { "userId",userId.ToString() }
                };

                var wallets = _Repository.ExecuteReadDictionaryAsync(query, "w", parameters).Result;
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"User with id {userId} does not have any wallet",
                };
                if (wallets != null && wallets.Count>0)
                {
                    List<WalletDTO> walletsDtO = wallets.Select(w =>
                    {
                        var walletEntity = _mapper.Map<WalletEntity>(w);
                        var walletsBM = _mapper.Map<WalletBM>(walletEntity);
                        return _mapper.Map<WalletDTO>(walletsBM);
                    }).ToList();
                    response = new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.OK,
                        ObjectValue = walletsDtO,
                    };
                    return response;
                }
                return response;

            }

            catch (Exception ex)
            {
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Error occured while getting user wallets, exception : {ex}",
                };
                return response;
            }

        }
        public ResponseAPI<WalletDTO> GetUserWalletByType(Guid userId, WalletType walletType)
        {
            try
            {
                var query = @$"MATCH (w:Wallet{{AssignedToId: $userId,WalletType:$walletType}}) 
                                RETURN w{{.* }}";

                IDictionary<string, object> parameters = new Dictionary<string, object> {
                { "userId",userId.ToString() },
                { "walletType",walletType.ToString() }
            };

                var wallet = _Repository.ExecuteReadScalarAsync<Dictionary<string, object>>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"User with id {userId} does not have any wallet with type {walletType.ToString()}",
                };
                if (wallet != null )
                {
                    var walletEntity = _mapper.Map<WalletEntity>(wallet);
                    var walletsBM = _mapper.Map<WalletBM>(walletEntity);
                    var walletDTO = _mapper.Map<WalletDTO>(walletsBM);
                    return new ResponseAPI<WalletDTO>()
                    {
                        StatusCode=System.Net.HttpStatusCode.OK,
                        ObjectValue=walletDTO,

                    };
                }
                return response;

            }

            catch (Exception ex)
            {
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NotFound,
                    ExceptionMessage = $"Error occured while getting user wallets, exception : {ex}",
                };
                return response;
            }
        }
        #endregion

        #region create
        public ResponseAPI<WalletDTO> Create(WalletDTO walletDTO, Guid UserId, string pinCode)
        {
            if (walletDTO != null)
            {
                var walletBM = _mapper.Map<WalletBM>(walletDTO);
                var keys = KeyManager.GenerateSecuredKeys(pinCode);
                if (keys== null)
                {
                    return new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "wallet is not created: error while generating wallet keys",
                    };
                }
                var query = @"
                    CREATE (w:Wallet {
                        Id: $walletId,
                        PrivateKey:$PrivateKey,
                        PublicKey:$PublicKey,
                        Balance: $balance,
                        WalletType:$WalletType,
                        AssignedToType: $AssignedToType,
                        AssignedToId: $AssignedToId,
                        Status: $Status
                    })
                    RETURN w as w;";
                walletBM.Id = Guid.NewGuid();
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "walletId",walletBM.Id.ToString()  },
                    { "PrivateKey",keys.EncryptedPrivateKey  },
                    { "PublicKey",keys.PublicKey  },
                    { "balance", walletBM.Balance },
                    { "WalletType", walletBM.WalletType.ToString()},
                    { "AssignedToType", walletBM.AssignedToType.ToString() },
                    { "AssignedToId", walletBM.AssignedToId.ToString() },
                    { "Status", walletBM.Status.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync <INode> (query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not created",
                };
                if (result != null)
                {
                     var walletEntity = _mapper.Map<WalletEntity>(result);
                     var walletsBM = _mapper.Map<WalletBM>(walletEntity);
                    walletDTO= _mapper.Map<WalletDTO>(walletsBM);
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(walletDTO), "wallet must not be null");
            }
        }
        public ResponseAPI<List<WalletDTO>> CreateDefaultWallets(WalletDTO walletDTO, Guid CreateUserId, string pinCode)
        {
            if (walletDTO != null) {
                var walletBM = _mapper.Map<WalletBM>(walletDTO);
                var wallets = new List<WalletBM>();
                for (int i = 0; i < 4; i++)
                {
                    var keys = KeyManager.GenerateSecuredKeys(pinCode);
                    if (keys == null)
                    {
                        return new ResponseAPI<List<WalletDTO>>
                        {
                            StatusCode = System.Net.HttpStatusCode.BadRequest,
                            ExceptionMessage = "wallet is not created: error while generating wallet keys",
                        };
                    }
                    var walletBm = new WalletBM();
                    walletBm.Id = Guid.NewGuid();
                    walletBm.AssignedToId = walletBM.AssignedToId;
                    walletBm.AssignedToType = walletBM.AssignedToType;
                    walletBm.PrivateKey = keys.EncryptedPrivateKey;
                    walletBm.PublicKey = keys.PublicKey;
                    walletBm.WalletType = (WalletType)i;
                    walletBm.Status = (WalletStatus)0;
                    wallets.Add(walletBm);
                }
                var query = @"UNWIND $wallets AS walletData
                    CREATE (w:Wallet {
                        Id: walletData.walletId,
                        PrivateKey:walletData.PrivateKey,
                        PublicKey:walletData.PublicKey,
                        Balance: walletData.balance,
                        WalletType:walletData.WalletType,
                        AssignedToType: walletData.AssignedToType,
                        AssignedToId: walletData.AssignedToId,
                        Percentage: walletData.Percentage,
                        IsDefault: walletData.IsDefault,
                        Status: walletData.Status
                    })
                    RETURN w AS w;";

                var parameters = new Dictionary<string, object>
{
                    { "wallets", wallets.Select(wallet =>  new  Dictionary<string, object> {
                                    { "walletId",wallet.Id.ToString()  },
                                    { "PrivateKey",wallet.PrivateKey  },
                                    { "PublicKey",wallet.PublicKey  },
                                    { "balance", wallet.Balance },
                                    { "WalletType", wallet.WalletType.ToString()},
                                    { "AssignedToType", wallet.AssignedToType.ToString() },
                                    { "AssignedToId", wallet.AssignedToId.ToString() },
                                    { "Status", wallet.Status.ToString() }
                                })
                    }
                };
                var result = _Repository.ExecuteWriteRangeTransactionAsync<INode>(query, "w",parameters).Result;
                ResponseAPI<List<WalletDTO>> response = new ResponseAPI<List<WalletDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not created",
                };
                if (result != null)
                {
                    List<WalletDTO> walletsDtO = result.Select(w =>
                    {
                        var walletsEntity = _mapper.Map<WalletEntity>(w);
                        var walletsBM = _mapper.Map<WalletBM>(walletsEntity);
                        return _mapper.Map<WalletDTO>(walletsBM);
                    }).ToList();
                    response = new ResponseAPI<List<WalletDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletsDtO,
                    };
                    return response;
                }
                    return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(walletDTO), "wallet must not be null");
            } 

        }
        #endregion

        #region update
        public ResponseAPI<WalletDTO> Update(WalletDTO walletDTO, Guid UserId)
        {
            if (walletDTO != null)
            {
                var walletBM = _mapper.Map<WalletBM>(walletDTO);
                var query = @$"
                    MATCH (w:Wallet {{Id: $WalletId}})
                    SET w.Status = $Status ,
                    w.IsDefault = $IsDefault
                    RETURN w;";
                IDictionary<string, object> parameters = new Dictionary<string, object>
                {
                    { "WalletId",walletBM.Id.ToString()  },
                    { "Status", walletBM.Status.ToString() }
                };
                var result = _Repository.ExecuteWriteTransactionAsync<INode>(query, parameters).Result;
                ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.BadRequest,
                    ExceptionMessage = "wallet is not updated",
                };
                if (result != null)
                {
                    response = new ResponseAPI<WalletDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Created,
                        ObjectValue = walletDTO,
                    };
                    return response;
                }
                return response;
            }
            else
            {
                throw new System.ArgumentNullException(nameof(walletDTO), "wallet must not be null");
            }
        }
        #endregion

        #region Delete
        public ResponseAPI<WalletDTO> Delete(Guid id, Guid DeleteUserId)
        {
            var query = @$"
                MATCH (w:Wallet {{Id: $WalletId}})
                SET w.Status = $Status                       
                RETURN w;";
            IDictionary<string, object> parameters = new Dictionary<string, object>
            {
                { "WalletId",id.ToString()  },
                { "Status", WalletStatus.Deleted.ToString() }
            };
            var result = _Repository.ExecuteWriteTransactionAsync<INode>(query, parameters).Result;
            ResponseAPI<WalletDTO> response = new ResponseAPI<WalletDTO>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "wallet is not deleted",
            };
            if (result != null)
            {
                WalletEntity walletEntity = _mapper.Map<WalletEntity>(result.Properties);
                WalletBM walletBM = _mapper.Map<WalletBM>(walletEntity);
                WalletDTO walletDTO = _mapper.Map<WalletDTO>(walletBM);
                response = new ResponseAPI<WalletDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.NoContent,
                    ObjectValue = walletDTO,
                };
                return response;
            }
            return response;
        }
        #endregion

    }
}
