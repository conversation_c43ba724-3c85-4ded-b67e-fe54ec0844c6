﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.AccessManagement.DTO.User.EmailConfirmation;
using Platform.Dyno.AccessManagement.DTO.User.UserOTP;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Platform.Dyno.AccessManagement.Business.Service.UserManagement
{
    public class UserOTPService : IUserOTPService
    {
        private readonly IUnitOfWork<UserOTPEntity> _userOTPRepository;
        private readonly IUserService _userService;
        private readonly IMapper _mapper;
        private readonly IRedisCacheService _cache;
        private readonly IMacAddressService _macAddressService; 
        private readonly string _userOTPCacheKey = RedisCacheKey.UserCacheKey;
        private readonly UserManager<UserEntity> _userManager;

        public UserOTPService(IUnitOfWork<UserOTPEntity> userOTPRepository,
            IUserService userService,
            IMapper mapper, 
            IRedisCacheService cache,
            IMacAddressService macAddressService, 
            UserManager<UserEntity> userManager)
        {
            _userOTPRepository = userOTPRepository;
            _userService = userService;
            _mapper = mapper;
            _cache = cache;
            _macAddressService = macAddressService;
            _userManager = userManager;
        }

        #region Get
        public ResponseAPI<List<UserOTPDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
        {
            List<UserOTPDTO>? userOTPsDTO = _cache.GetData<List<UserOTPDTO>>(_userOTPCacheKey);
            if (userOTPsDTO == null || userOTPsDTO.Count == 0)
            {
                List<UserOTPEntity> userOTPEntities = (List<UserOTPEntity>)_userOTPRepository.Repository.GetAll(includes: new List<string> { "User" }, orderBy: user => user.OrderByDescending(user => user.LastModificationTime));
                List<UserOTPBM> userOTPs = _mapper.Map<List<UserOTPBM>>(userOTPEntities);
                userOTPsDTO = _mapper.Map<List<UserOTPDTO>>(userOTPs);

                _cache.SetData(_userOTPCacheKey, userOTPsDTO, DateTimeOffset.UtcNow.AddDays(1));

            }

            ResponseAPI<List<UserOTPDTO>> response = new ResponseAPI<List<UserOTPDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = userOTPsDTO,
            };
            return response;
        }

        public ResponseAPI<PagedList<UserOTPDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<UserOTPDTO>> userOTPsDTO = GetAll();
            PagedList<UserOTPDTO>? pagedList = null;
            if (userOTPsDTO.ObjectValue != null)
            {
                pagedList = PagedList<UserOTPDTO>.ToGenericPagedList(userOTPsDTO.ObjectValue, pagedParameters);
            }
            return new ResponseAPI<PagedList<UserOTPDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }

        public ResponseAPI<PagedList<UserOTPDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
        {
            ResponseAPI<List<UserOTPDTO>> userOTPsDTO = GetAll();
            PagedList<UserOTPDTO>? pagedList = null;
            if (userOTPsDTO.ObjectValue != null)
            {
                pagedList = PagedList<UserOTPDTO>.ToGenericPagedList(userOTPsDTO.ObjectValue.Where(user => user.Status == Shared.Enum.Status.Active).ToList(), pagedParameters);
            }
            return new ResponseAPI<PagedList<UserOTPDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = pagedList
            };
        }
        public ResponseAPI<UserOTPDTO> Get(Guid id)
        {
            UserOTPDTO? userOTPDTO = GetAll().ObjectValue?.Where(userOTP => userOTP.Id == id).FirstOrDefault();
            return new ResponseAPI<UserOTPDTO>()
            {
                StatusCode = userOTPDTO == null ? System.Net.HttpStatusCode.NotFound : System.Net.HttpStatusCode.OK,
                ExceptionMessage = userOTPDTO == null ? $"User OTP with id {id} Not Found !" : null,
                ObjectValue = userOTPDTO
            };
        }

        public ResponseAPI<List<UserOTPDTO>> Get(Func<UserOTPDTO, bool> expression)
        {
            List<UserOTPDTO>? userOTPsDTO = GetAll().ObjectValue?.Where(expression).ToList();
            return new ResponseAPI<List<UserOTPDTO>>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ObjectValue = userOTPsDTO
            };
        }
        #endregion

        #region Create
        public ResponseAPI<UserOTPDTO> Create(UserOTPDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
        {
            UserOTPBM userOTP = _mapper.Map<UserOTPBM>(dtoObject);

            #region changed data
            userOTP.Id = Guid.NewGuid();
            userOTP.GenerateCode();
            RefDataService<UserOTPBM>.CreateRefData(userOTP, creatorUserId);
            #endregion

            UserOTPEntity userOTPEntity = _mapper.Map<UserOTPEntity>(userOTP);

            _userOTPRepository.Repository.Insert(userOTPEntity);
            _userOTPRepository.Save();

            dtoObject.Id = userOTPEntity.Id;
            dtoObject.Code = userOTPEntity.Code;
            if (updateCache)
            {
                _cache.RemoveData(_userOTPCacheKey);
            }

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Created,
                ObjectValue = dtoObject
            };
        }

        public ResponseAPI<UserOTPDTO> Create(string email, Guid? creatorUserId = null, bool updateCache = true)
        {
            ResponseAPI<List<UserDTO>> userDto = _userService.Get(userDto => userDto.NormalizedEmail == email.ToUpper());
            if(userDto.StatusCode == System.Net.HttpStatusCode.OK)
            {

                UserOTPBM userOTP = new UserOTPBM();
                userOTP.Id = Guid.NewGuid();
                userOTP.GenerateCode();
                userOTP.UserId = userDto.ObjectValue?.FirstOrDefault()?.Id;
                RefDataService<UserOTPBM>.CreateRefData(userOTP, creatorUserId);

                UserOTPEntity userOTPEntity = _mapper.Map<UserOTPEntity>(userOTP);
                _userOTPRepository.Repository.Insert(userOTPEntity);
                _userOTPRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_userOTPCacheKey);
                }

                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Created,
                    ObjectValue = new UserOTPDTO
                    {
                        Id = userOTPEntity.Id,
                        Code = userOTP.Code,
                        IsConfirmed = userOTPEntity.IsConfirmed,
                    }
                };
            }
            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = userDto.ExceptionMessage
            };
        }

        public ResponseAPI<UserOTPDTO> SendOTP(UserOTPDTO dtoObject, Guid? creatorUserId = null, bool updateCache = true)
        {
            ResponseAPI<UserOTPDTO> response = Create(dtoObject);
            if(response.StatusCode == System.Net.HttpStatusCode.Created)
            {
                //Send OTP

                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = response.ObjectValue
                };
            }

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = response.StatusCode,
                ExceptionMessage= response.ExceptionMessage
            };
        }
        #endregion

        #region Update
        public ResponseAPI<UserOTPDTO> Update(UserOTPDTO dtoObject, Guid? updateUserId = null, bool updateCache = true)
        {
            if (Get(dtoObject.Id).ObjectValue != null)
            {
                UserOTPBM userOTP = _mapper.Map<UserOTPBM>(dtoObject);

                #region RefData
                RefDataService<UserOTPBM>.UpdateRefData(userOTP, updateUserId);
                #endregion

                UserOTPEntity userOTPEntity = _mapper.Map<UserOTPEntity>(userOTP);
                _userOTPRepository.Repository.Update(userOTPEntity);
                _userOTPRepository.Save();
                
                dtoObject.Id = userOTPEntity.Id;

                if (updateCache)
                {
                    _cache.RemoveData(_userOTPCacheKey);
                }

                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = dtoObject
                };
            }

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {dtoObject.Id} Not Found !"
            };
        }
        #endregion

        #region Delete
        public ResponseAPI<UserOTPDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            UserOTPDTO? userOTPDTO = Get(id).ObjectValue;
            if (userOTPDTO != null)
            {
                UserOTPBM userOTP = _mapper.Map<UserOTPBM>(userOTPDTO);

                #region Refdata
                RefDataService<UserOTPBM>.DeleteRefData(userOTP, deletorUserId);
                #endregion

                UserOTPEntity userOTPEntity = _mapper.Map<UserOTPEntity>(userOTP);
                _userOTPRepository.Repository.Update(userOTPEntity);
                _userOTPRepository.Save();

                if (updateCache)
                {
                    _cache.RemoveData(_userOTPCacheKey);
                }

                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userOTPDTO
                };
            }

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {id} Not Found !"
            };
        }

        public ResponseAPI<UserOTPDTO> Remove(Guid id, Guid? deletorUserId = null, bool updateCache = true)
        {
            UserOTPDTO? userOTPDTO = Get(id).ObjectValue;
            if (userOTPDTO != null)
            {
                _userOTPRepository.Repository.Delete(id);
                _userOTPRepository.Save();

                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ObjectValue = userOTPDTO
                };
            }

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.NotFound,
                ExceptionMessage = $"User with id {id} Not Found !"
            };
        }
        #endregion

        #region Verifier
        public ResponseAPI<UserOTPDTO> VerifierCode(UserOTPDTO userOTPDTO)
        {
            ResponseAPI<List<UserOTPDTO>> usersOTP = Get(userOTP => 
            userOTP.UserId == userOTPDTO.UserId &&
            userOTP.IsConfirmed == false &&
            userOTP.Code == userOTPDTO.Code
            );

            if (usersOTP.ObjectValue == null || usersOTP.ObjectValue.Count <= 0)
            {
                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Code Unfound"
                };
            }
            DateTime? day = DateTime.UtcNow.AddMinutes(-60);
            
            if (usersOTP.ObjectValue.FirstOrDefault()?.CreationTime < day)
            {
                return new ResponseAPI<UserOTPDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Code is Expired, please renvoyer other code."
                };
            }

            usersOTP.ObjectValue.FirstOrDefault().IsConfirmed = true;
            ResponseAPI<UserOTPDTO> updateUserOtp = Update(usersOTP.ObjectValue.FirstOrDefault());

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.OK,
                ExceptionMessage = "Code verified successfully"
            };
        }

        public ResponseAPI<UserOTPDTO> VerifierDevice(ClientOtpDTO userOtp, bool updateCache = true)
        {
            UserDTO? user = _userService.Get(user => user.PhoneNumber== userOtp.PhoneNumber &&
            user.CountryCode == userOtp.CountryCode &&
            user.Status == Status.Active &&
            user.UserType == UserType.Client).ObjectValue?.FirstOrDefault();
            if (user != null)
            {
                ResponseAPI<List<UserOTPDTO>> response = Get(otp => otp.Code == userOtp.Code &&
                otp.User?.PhoneNumber == userOtp.PhoneNumber &&
                otp.IsConfirmed == false);

                if (response.StatusCode == System.Net.HttpStatusCode.OK
                    && response.ObjectValue != null && response.ObjectValue.Count > 0)
                {
                    UserOTPDTO userOTPDTO = response.ObjectValue.FirstOrDefault() ?? new UserOTPDTO();

                    ResponseAPI<UserOTPDTO> updateUserOtp = Remove(userOTPDTO.Id);

                    if (userOtp.IsSaved)
                    {
                        ResponseAPI<MacAddressDTO> saveMacAddress = _macAddressService.SaveDevice(user.Id, userOtp.MacAddress, userOtp.IsSaved);
                    }
                    else
                    {
                        ResponseAPI<MacAddressDTO> removeMacAddress = _macAddressService.Remove(userOtp.MacAddress);

                    }

                    return new ResponseAPI<UserOTPDTO>
                    {
                        StatusCode = updateUserOtp.StatusCode,
                        ExceptionMessage = updateUserOtp.ExceptionMessage != null ? updateUserOtp.ExceptionMessage : $"User code {userOtp.Code} verifier successfully",
                    };
                }
            }
            

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized",
            };
        }

        public ResponseAPI<UserOTPDTO> VerifierDevice(AdminOtpDTO userOtp,  bool updateCache = true)
        {
            UserDTO? user = _userService.Get(user => user.Email == userOtp.Email &&
            user.UserType != UserType.Client &&
            user.UserType != UserType.Cashier &&
            user.Status == Status.Active).ObjectValue?.FirstOrDefault();

            if(user != null)
            {
                ResponseAPI<List<UserOTPDTO>> response = Get(otp => otp.Code == userOtp.Code &&
                otp.User?.NormalizedEmail == userOtp.Email.ToUpper() &&
                otp.IsConfirmed == false);

                if (response.StatusCode == System.Net.HttpStatusCode.OK
                    && response.ObjectValue != null && response.ObjectValue.Count > 0)
                {
                    UserOTPDTO userOTPDTO = response.ObjectValue.FirstOrDefault();
                    userOTPDTO.IsConfirmed = true;

                    ResponseAPI<UserOTPDTO> updateUserOtp = Remove(userOTPDTO.Id);

                    if (userOtp.IsSaved)
                    {
                        ResponseAPI<MacAddressDTO> saveMacAddress = _macAddressService.SaveDevice(user.Id, userOtp.MacAddress, userOtp.IsSaved);
                    }else
                    {
                        ResponseAPI<MacAddressDTO> removeMacAddress = _macAddressService.Remove(userOtp.MacAddress);

                    }

                    return new ResponseAPI<UserOTPDTO>
                    {
                        StatusCode = updateUserOtp.StatusCode,
                        ExceptionMessage = updateUserOtp.ExceptionMessage != null ? updateUserOtp.ExceptionMessage : $"User code {userOtp.Code} verifier successfully",
                    };
                }
            }
            

            return new ResponseAPI<UserOTPDTO>
            {
                StatusCode = System.Net.HttpStatusCode.Unauthorized,
                ExceptionMessage = "User unauthorized",
            };
        }

        public async Task <ResponseAPI<EmailConfirmationDTO>> EmailConfirmation(EmailConfirmationDTO emailConfirmationDTO)
        {

            ResponseAPI<List<UserDTO>> responseUser =
               _userService.Get(user => user.NormalizedEmail == emailConfirmationDTO.Email.ToUpper() && 
               user.UserType != UserType.Client &&
               user.UserType != UserType.Cashier &&
               user.Status == Status.Active);

            if(responseUser == null || responseUser.ObjectValue == null || responseUser.ObjectValue.Count <= 0) 
            {
                return new ResponseAPI<EmailConfirmationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Email not found"
                };
            }
            UserBM user = _mapper.Map<UserBM>(responseUser.ObjectValue.FirstOrDefault());
            UserEntity userEntity = _mapper.Map<UserEntity>(user);

            var result = await _userManager.ConfirmEmailAsync(userEntity, emailConfirmationDTO.Token);
            if (result.Succeeded) {
               
                return new ResponseAPI<EmailConfirmationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = "Mail verified successfully"
                };
            }
            else
            {
                return new ResponseAPI<EmailConfirmationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "Invalid Token"
                };
}
        }
        #endregion
    }
}
