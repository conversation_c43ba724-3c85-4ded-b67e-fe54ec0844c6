﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Platform.Dyno.Shared.RefData;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.BusinessModel.User
{
    public class UserTokenBM : ReferentialData
    {
        #region Data
        [Required]
        public Guid Id { get; set; }
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiredDate { get; set; }
        #endregion

        #region Structure
        public UserBM? User { get; set; }
        public Guid? UserId { get; set; }
        #endregion

        public bool IsAccessTokenExpired(Claim expClaim)
        {
            if (expClaim != null)
            {
                var expirationTimeUnix = long.Parse(expClaim.Value);
                var expirationTimeUtc = DateTimeOffset.FromUnixTimeSeconds(expirationTimeUnix).UtcDateTime;

                return DateTime.UtcNow >= expirationTimeUtc;
            }

            return true; 
        }


    }
}
