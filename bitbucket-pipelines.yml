image: mcr.microsoft.com/dotnet/sdk:8.0 # Use the official .NET SDK image
options:
  docker: true
pipelines:
  default:
    - step:
        name: Build and Publish
        size: 2x
        caches:
          - dotnetcore
        script:
          - cd Platform.Dyno.Microservice.Backend/Platform.Dyno.AccessMangement/Platform.Dyno.AccessManagement.WebAPI
          - dotnet restore --disable-parallel
          - dotnet publish -c Release -o /app/published-app --no-restore

    - step:
        name: Build Docker Image
        services:
          - docker
        script:
          - docker build -t access .
          - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
          - docker tag access "$DOCKER_USERNAME/access:latest"
          - docker push "$DOCKER_USERNAME/access"

    - step:
        name: Deploy using Docker Compose
        services:
          - docker
        script:
          - docker-compose up -d
