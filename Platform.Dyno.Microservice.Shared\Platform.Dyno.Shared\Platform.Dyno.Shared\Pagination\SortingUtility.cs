﻿using Amazon.S3.Model;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Z.EntityFramework.Extensions;

namespace Platform.Dyno.Shared.Pagination
{
    public class SortingUtility : ISortingUtility
    {
        public PagedList<TDTO> SortData<TDTO>(PagedParameters pagedParameters, ResponseAPI<PagedList<TDTO>> dtoList) where TDTO : class
        {
            PagedList<TDTO> sortedData = new PagedList<TDTO>();
            if (pagedParameters.SortBy != null && dtoList.ObjectValue !=null)
            {
                var property = typeof(TDTO).GetProperty(pagedParameters.SortBy);
                if (property != null)
                {
                    var sortedList = dtoList.ObjectValue.ToList();
                    if (pagedParameters.SortDirection == "1")
                    {
                        sortedList = sortedList.OrderBy(x => property.GetValue(x, null)).ToList();
                        dtoList.ObjectValue = new PagedList<TDTO>(sortedList, dtoList.ObjectValue.CurrentPage, dtoList.ObjectValue.PageSize, dtoList.ObjectValue.TotalCount);

                    }
                    else
                    {
                        sortedList = sortedList.OrderByDescending(x => property.GetValue(x, null)).ToList();
                        dtoList.ObjectValue = new PagedList<TDTO>(sortedList, dtoList.ObjectValue.CurrentPage, dtoList.ObjectValue.PageSize, dtoList.ObjectValue.TotalCount);

                    }
                }
            }
            else if(dtoList.ObjectValue != null)
            {
                return dtoList.ObjectValue;
            }
            else {
                throw new InvalidOperationException($"Property '{pagedParameters.SortBy}' there is no data  .");
            }
            return dtoList.ObjectValue;

        }

        public PagedList<T> FilterData<T>(PagedParameters pagedParameters, ResponseAPI<PagedList<T>> data) where T : class
        {
            if (pagedParameters.Filters != null && pagedParameters.Filters.Any() && data.ObjectValue != null)
            {

                var filteredList = data.ObjectValue.ToList();

                foreach (var filter in pagedParameters.Filters)
                {
                    filteredList = ApplyFilter(filter.Key, filter.Value, filteredList);
                }

                return new PagedList<T>(filteredList, data.ObjectValue.CurrentPage, data.ObjectValue.PageSize, data.ObjectValue.TotalCount);
            }
            else if(data.ObjectValue ==null)
            {
                throw new InvalidOperationException($"Property '{pagedParameters.SortBy}' not found .");
            }

            return data.ObjectValue;
        }
        //Mission failed. We are so sorry to hear that another soldier has died. If you didn't complete the mission,
        //increment the number of days and personnel. Personnel: 2, Days: 5.
        private List<T> ApplyFilter<T>(string filterKey, object filterValue, IEnumerable<T> sourceList) where T : class
        {
            var type = typeof(T);
            var parts = filterKey.Split('.');
            PropertyInfo[] propertyInfoArray = new PropertyInfo[parts.Length];
            for (int i = 0; i < parts.Length; i++)
            {
                propertyInfoArray[i] = type.GetProperty(parts[i]);
                if (propertyInfoArray[i] == null)
                {
                    break;
                }
                if (propertyInfoArray[i].PropertyType.IsGenericType)
                {
                    type = propertyInfoArray[i].PropertyType.GetGenericArguments().FirstOrDefault();
                }
                else
                {
                    type = propertyInfoArray[i].PropertyType;
                }
                
            }


            return sourceList.Where(x =>
            {
                List<object>? values = new List<object> { x };
                foreach (var property in propertyInfoArray)
                {
                    List<object>? newValues = new List<object>();
                    foreach (var value in values)
                    {

                        if (property != null)
                        {
                            var result = property.GetValue(value);
                            if (result != null)
                            {
                                if (result is IEnumerable enumerableResult)
                                {
                                    if (enumerableResult.GetType().IsGenericType && enumerableResult.GetType().GetGenericTypeDefinition() == typeof(List<>))
                                    {
                                        newValues.AddRange((IEnumerable<object>)result);
                                    }
                                    else
                                    {
                                        newValues.Add(result);
                                    }
                                }
                                else
                                {
                                    newValues.Add(result);
                                }
                            }
                            else
                                break;
                        }
                    }
                    values = newValues;

                }
                return values != null && values.Any(x =>
          x?.ToString()?.IndexOf(filterValue?.ToString(), StringComparison.OrdinalIgnoreCase) >= 0);
            }).ToList();
        }

       
    }
}

