﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.Service.Reporting
{
    public class InvoiceModel
    {
        public string InvoiceNumber { get; set; }
        public DateTime IssueDate { get; set; }
        public DateTime DueDate { get; set; }
        public string Title { get; set; }

        public CompanyDetails SellerAddress { get; set; }
        public CompanyDetails CustomerAddress { get; set; }

        public List<OrderItem> Items { get; set; }
        
        public double TotalPrice { get; set; }
        public string TotalPriceText { get; set; }

        public PaiementDetails paiementDetails { get; set; }
        public string Comments { get; set; }
    }

    public class OrderItem
    {
        public string ProductType { get; set; }
        public int Quantity { get; set; }
        public double VAT { get; set; }
        public double NET { get; set; }
        public double Total { get; set; }
    }

    public class CompanyDetails
    {
        public string CompanyName { get; set; }
        public string? RNE { get; set; }
        public string? VAT { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
    }

    public class PaiementDetails
    {
        public string Bank { get; set; }
        public string BIC { get; set; }
        public string IBAN { get; set; }
    }
}
