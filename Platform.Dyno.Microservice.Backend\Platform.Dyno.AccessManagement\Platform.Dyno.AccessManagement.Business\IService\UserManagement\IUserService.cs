﻿using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement;

public interface IUserService : IGenericService<UserDTO>
{
    ResponseAPI<UserProfileDTO> GetUserProfile(Guid id);
    ResponseAPI<UserProfileDTO> UpdateUserProfile(UserProfileDTO userProfileDTO);
    ResponseAPI<PagedList<UserDTO>> GetUsersCreatedByAdmin(PagedParameters pagedParameters, Guid id, Guid? companyId, UserType userType);
    ResponseAPI<List<UserDTO>> GetAllByRole(Guid roleId, Guid? companyId = null, UserType? userType = null);
    ResponseAPI<List<UserDTO>> GetAllByDefaultRole(string defaultRole, Guid? companyId = null);
    ResponseAPI<bool> CheckUserPinCode(Guid userId, string pinCode);
    ResponseAPI<bool> CheckUserIsSuperAdmin(Guid userId);
    ResponseAPI<UserDTO> Remove(Guid id, bool updateCache = true);
    ResponseAPI<UserDTO> GetUserByPhoneNumber(string phoneNumber);
    Task<ResponseAPI<UserDTO>> Delete(Guid id);





}
