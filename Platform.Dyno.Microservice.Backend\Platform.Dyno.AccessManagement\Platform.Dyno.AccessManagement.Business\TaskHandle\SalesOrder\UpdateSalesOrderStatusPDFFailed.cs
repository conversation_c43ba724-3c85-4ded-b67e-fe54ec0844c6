﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesOrder
{
    [HandlerTopics("SO_Status_PDF_Failed")]
    public class UpdateSalesOrderStatusPDFFailed : IExternalTaskHandler
    {
        private readonly ILogger<UpdateSalesOrderStatusPDFFailed> _logger;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;
        private readonly IHelper<NotificationDTO> _notificationHelper;
        private readonly Configuration _configuration;

        public UpdateSalesOrderStatusPDFFailed(
        ILogger<UpdateSalesOrderStatusPDFFailed> logger,
        ISalesOrderService salesOrderService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        IHelper<NotificationDTO> notificationHelper,
        Configuration configuration)

        {
            _logger = logger;
            _salesOrderService = salesOrderService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _notificationHelper = notificationHelper;
            _configuration = configuration;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var orderJson = externalTask.Variables["SalesOrder"]?.ToString();
                SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(orderJson);
                //SalesOrderDTO? salesOrderDTO = JsonSerializer.Deserialize<SalesOrderDTO>(externalTask.Variables["SalesOrder"].Value.ToString());

                if (salesOrderDTO == null)
                {
                    outputVariables.Add("Result_SO_Status_PDF_Failed", new BooleanVariable(false));
                    outputVariables.Add("Error_SO_Status_PDF_Failed", new StringVariable("json conversion failed"));

                }
                else
                {
                    var response = _salesOrderService.UpdateStatus(salesOrderDTO.Id, SalesOrderStatus.PDFFailed, true);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        outputVariables.Add("Result_SO_Status_PDF_Failed", new BooleanVariable(true));
                        NotificationDTO notification = new NotificationDTO
                        {
                            Id = salesOrderDTO.Id,
                            Title = "Status Sales Order",
                            Message = JsonSerializer.Serialize(response.ObjectValue),
                            SendToGroup = new List<string> { salesOrderDTO.CompanyId.ToString() }
                        };

                        string url = $"{_configuration.NotificationAddress}/Api/Notification/SalesOrderStatusNotif";
                        ResponseAPI<NotificationDTO>? responseAPI = _notificationHelper.Post(url, notification);
                    }
                    else
                    {
                        outputVariables.Add("Result_SO_Status_PDF_Failed", new BooleanVariable(false));
                        outputVariables.Add("Error_SO_Status_PDF_Failed", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                    
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SO_Status_PDF_Failed", new BooleanVariable(false));
                outputVariables.Add("Error_SO_Status_PDF_Failed", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
