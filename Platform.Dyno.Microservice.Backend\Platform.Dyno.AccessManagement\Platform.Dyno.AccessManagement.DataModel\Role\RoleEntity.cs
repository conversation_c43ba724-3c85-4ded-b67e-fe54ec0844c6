﻿using Microsoft.AspNetCore.Identity;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.Shared.Enum;
using System.ComponentModel.DataAnnotations.Schema;

namespace Platform.Dyno.AccessManagement.DataModel.Role
{
    [Table("Role")]
    public class RoleEntity : IdentityRole<Guid>
    {
        public UserType UserType { get; set; }

        #region RefData
        public Status Status { get; set; }

        #region Creation
        public Guid? CreatorUserId { get; set; }
        public DateTime? CreationTime { get; set; }
        #endregion

        #region Modification
        public Guid? LastModifierUserId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        #endregion

        #region Deletion
        public Guid? DeleterUserId { get; set; }
        public DateTime? DeletionTime { get; set; }
        #endregion
        #endregion

        #region Structure
        
        public ICollection<RoleUserEntity>? RoleUsers { get; set; }
        public virtual IEnumerable<UserEntity>? Users => RoleUsers?.Select(u => u.User).ToList();
        public ICollection<PermissionEntity>? Permissions { get; set; }

        public Guid? CompanyId { get; set; }
        #endregion
    }
}
