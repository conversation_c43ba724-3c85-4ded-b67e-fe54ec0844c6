﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Reporting
{
    public class InvoiceDetailsDTO
    {
        public string Code { get; set; } = string.Empty;
        public ProductType ProductType { get; set; }
        public double DynoAmount { get; set; }
        public double NetAmount { get; set; }
        public double VATAmount { get; set; }
        public double TotalAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public Guid CompanyCreatorId { get; set; }
        public Guid CompanyReceiverId { get; set; }
    }
}
