﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class Employee_DeleteUserName_CompanyName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("01976278-669c-4fb3-a054-a2400b457e9e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("057454a0-9c0f-4c8a-a233-64e15308e9e8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("057803b1-805f-49e8-83cf-81c26071bdbf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("10f010d8-aebd-42d0-8960-3b70192b753d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1680bfa1-17a0-4d50-a5fd-d02e94dc6dc7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("17d48710-4642-4143-8bf5-db4b3149624c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("46c99795-0cd0-414d-be66-af6dcc6af7ed"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("49847712-c67b-4c2d-892c-c2ac5e9e9549"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4deb3535-967a-4ad1-aa70-70e6600d24cc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4e3eccd9-324e-42d0-9c10-f81eebc96530"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4ff1df44-b725-40d6-9eed-6ec0c7979ef9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("62ef46ef-2265-4ab2-9ff0-2b1f5475c398"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("707e694f-59f0-4ca2-9976-bbcdcbc701cd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7d2122f7-0300-4f92-b512-241e9244ee6b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("86a9c0b4-b4be-4a62-a4ce-941791cdb985"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8818f091-15b7-41e6-9794-eca7202007df"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8a869811-5c92-4d7e-bf05-6ae811172508"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("91529635-074d-4e93-9681-4df7aa834ea7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a8750d27-7fe7-4243-8aed-9033907aa949"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b0f95d68-4c89-4dbe-8abb-c5c331d17357"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b29c7f50-30b6-4de2-9b9b-e4598781afa9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d6e77324-60ca-4975-955b-4821acb5e205"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ed2d78c3-2dc5-4148-b00b-12711541df9f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f6d7877c-b570-47a1-9b25-cff6d8a03c33"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("04aaa642-2320-46e0-9622-1599610008d9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("896e4f08-4840-420e-bd54-46836d620107"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"));

            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "Employee");

            migrationBuilder.DropColumn(
                name: "UserName",
                table: "Employee");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0fc62146-edcf-44a6-92e6-94780216414d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9fcacc9a-4630-4896-85e6-038352fb4647"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("bb6514e8-9623-467c-8fee-5a2254515de9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ff32e5f3-9359-43bf-9cc6-544c52631ab4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "24797708-3e61-45a4-8b17-8a4dcef23432", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7762) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "d5f3dd33-883f-481f-a0d7-17cbf3215085", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7741) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fa24e072-3dbf-4f2e-81f6-afcaa740a804", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7750) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1b4dd9da-8284-47ce-96b3-fe76ce7723b1", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7765) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "8e1af9ac-94d0-41b4-99e7-e4d1228be6c3", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7758) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "11be0914-88a7-4819-9c31-e73e4fab0fc8", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(7755) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "e48bf651-a53c-45bf-8615-9d818af3c121", new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(8097), new DateTime(2024, 3, 27, 9, 55, 4, 34, DateTimeKind.Utc).AddTicks(8097), "AQAAAAEAACcQAAAAEEQod6WKHz58I9Os0MKopH9yZ+M0UsoGSvrXX0AcBcjBITVbVPdc/2ygmhgS75Jjew==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("ff32e5f3-9359-43bf-9cc6-544c52631ab4"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("9fcacc9a-4630-4896-85e6-038352fb4647"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("ff32e5f3-9359-43bf-9cc6-544c52631ab4"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("bb6514e8-9623-467c-8fee-5a2254515de9"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("bb6514e8-9623-467c-8fee-5a2254515de9"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0fc62146-edcf-44a6-92e6-94780216414d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("0020beea-fc2b-447d-86a7-7fdb0a33e30f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2d022c05-24d1-429c-a73e-7016d4a47223"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4e03a4a7-acec-460e-b401-4f8e9fc3fd12"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4e4686f7-3610-421d-894c-fd8aec98a4f3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5bce6547-bac1-43c6-b5ea-158bd2fbe329"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5cc2cc3b-37cf-4930-8b36-bc473c637563"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("632c4b3d-6531-4244-b54c-9b20fb4df65e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("*************-4e35-a4d2-1c3cd6f66861"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6fd71a47-2d0b-41ad-8c14-6f1c8feb93bc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("76488897-71f1-4a8e-8008-dd1802e000cb"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("815b363f-983c-4d65-89ad-f0e7270b3e50"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("82b18211-6768-4a2c-900e-f7f72b838f97"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("83f3b727-1d7a-4241-b0c5-8caadcb47705"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8ab394d5-141e-4d2e-84c8-1ed494c795dc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a05495c2-a1a1-4664-82ae-eca251d7adfd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b0655878-8d4a-4010-bd87-a753e148f4dc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c023fb40-409d-40e4-b2c7-64e9a53fc0bc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c3f916d5-0bfc-4b0f-83bc-5ef44d0d9dd9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ce4452a8-7863-435d-a29e-ec434dcd037d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d08c4f08-63b5-4713-be3c-2aad1b8c6e87"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d69736f6-0171-448f-81d4-5cf4541cddf5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d7c2c00e-1d79-4288-9c18-1c85a8e0807c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ddcb8225-371a-4b6b-ac43-d9405a0cc635"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e1a09a3a-c5e3-4217-a7ed-a803a8afa5fd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0020beea-fc2b-447d-86a7-7fdb0a33e30f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2d022c05-24d1-429c-a73e-7016d4a47223"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4e03a4a7-acec-460e-b401-4f8e9fc3fd12"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4e4686f7-3610-421d-894c-fd8aec98a4f3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5bce6547-bac1-43c6-b5ea-158bd2fbe329"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5cc2cc3b-37cf-4930-8b36-bc473c637563"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("632c4b3d-6531-4244-b54c-9b20fb4df65e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("*************-4e35-a4d2-1c3cd6f66861"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6fd71a47-2d0b-41ad-8c14-6f1c8feb93bc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("76488897-71f1-4a8e-8008-dd1802e000cb"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("815b363f-983c-4d65-89ad-f0e7270b3e50"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("82b18211-6768-4a2c-900e-f7f72b838f97"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("83f3b727-1d7a-4241-b0c5-8caadcb47705"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8ab394d5-141e-4d2e-84c8-1ed494c795dc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a05495c2-a1a1-4664-82ae-eca251d7adfd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b0655878-8d4a-4010-bd87-a753e148f4dc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c023fb40-409d-40e4-b2c7-64e9a53fc0bc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c3f916d5-0bfc-4b0f-83bc-5ef44d0d9dd9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ce4452a8-7863-435d-a29e-ec434dcd037d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d08c4f08-63b5-4713-be3c-2aad1b8c6e87"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d69736f6-0171-448f-81d4-5cf4541cddf5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d7c2c00e-1d79-4288-9c18-1c85a8e0807c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ddcb8225-371a-4b6b-ac43-d9405a0cc635"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e1a09a3a-c5e3-4217-a7ed-a803a8afa5fd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0db2be4d-1dfa-499f-93d7-1ede1745889a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2d3820c7-5a67-4ce8-b487-d2b0f6ec5a71"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9adf039c-d113-499d-9f42-da09a19d6c41"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a81a2cf1-9b57-47cf-ab61-052614abe3a6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cecfee64-01b5-4e41-a39a-386bec8e5d78"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cf72c8e2-5d01-41cd-ba99-eda55d047fdd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0fc62146-edcf-44a6-92e6-94780216414d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9fcacc9a-4630-4896-85e6-038352fb4647"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bb6514e8-9623-467c-8fee-5a2254515de9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ff32e5f3-9359-43bf-9cc6-544c52631ab4"));

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "Employee",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "UserName",
                table: "Employee",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "c11d7307-75f2-49ea-9f02-39711990e723", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5678) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1deb2074-1749-469a-bae3-c27c3a6b18f4", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5644) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "54d55432-72fd-45e8-b2b2-e2d2366eacff", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5652) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fc840988-7cb0-4639-9596-55688a85efa3", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5683) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "e18f9697-c753-49c6-8593-48e28bf94647", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5661) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f01d645f-21f7-4b14-b3e6-db12f15cee61", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5657) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "fc4db08b-aa02-47a9-af4a-d79bc5ff2c57", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6007), new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6006), "AQAAAAEAACcQAAAAEND7zUinVYKTFkL1KNVZ54BZLmGMB/o1AHyoYCOSY66siyngCJjiDM+J8y6C64D7Jg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("04aaa642-2320-46e0-9622-1599610008d9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("896e4f08-4840-420e-bd54-46836d620107"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("01976278-669c-4fb3-a054-a2400b457e9e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("057454a0-9c0f-4c8a-a233-64e15308e9e8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("057803b1-805f-49e8-83cf-81c26071bdbf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("10f010d8-aebd-42d0-8960-3b70192b753d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1680bfa1-17a0-4d50-a5fd-d02e94dc6dc7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("17d48710-4642-4143-8bf5-db4b3149624c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("46c99795-0cd0-414d-be66-af6dcc6af7ed"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("49847712-c67b-4c2d-892c-c2ac5e9e9549"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4deb3535-967a-4ad1-aa70-70e6600d24cc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4e3eccd9-324e-42d0-9c10-f81eebc96530"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4ff1df44-b725-40d6-9eed-6ec0c7979ef9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("62ef46ef-2265-4ab2-9ff0-2b1f5475c398"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("707e694f-59f0-4ca2-9976-bbcdcbc701cd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7d2122f7-0300-4f92-b512-241e9244ee6b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("86a9c0b4-b4be-4a62-a4ce-941791cdb985"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8818f091-15b7-41e6-9794-eca7202007df"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8a869811-5c92-4d7e-bf05-6ae811172508"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("91529635-074d-4e93-9681-4df7aa834ea7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a8750d27-7fe7-4243-8aed-9033907aa949"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b0f95d68-4c89-4dbe-8abb-c5c331d17357"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b29c7f50-30b6-4de2-9b9b-e4598781afa9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d6e77324-60ca-4975-955b-4821acb5e205"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ed2d78c3-2dc5-4148-b00b-12711541df9f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f6d7877c-b570-47a1-9b25-cff6d8a03c33"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }
    }
}
