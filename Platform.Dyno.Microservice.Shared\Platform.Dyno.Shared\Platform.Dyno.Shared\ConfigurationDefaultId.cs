﻿using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared
{
    public class ConfigurationDefaultId
    {
        public Guid CompanyId { get; }
        public Guid UserId { get; }
        public Guid UserClientId { get; }
        public Guid RoleId { get;}
        public Guid RoleSuperAdminId { get;}
        public Guid RoleCompanyAdminId { get; }
        public Guid RoleShopAdminId { get; }
        public Guid RoleClientId { get;}
        public Guid RoleCashierId { get; }

        #region Super Admin Permissions
        public Guid CompanyManagementPermissionId { get;} 
        public Guid CompanyPermissionId { get;} 
        public Guid ViewCompanyPermissionId { get;} 
        public Guid AddCompanyPermissionId { get;} 
        public Guid UpdateCompanyPermissionId { get;} 
        public Guid DeleteCompanyPermissionId { get;}

        public Guid AuthentificationPermissionId { get; }
        public Guid UserPermissionId { get; }
        public Guid ViewUserPermissionId { get; }
        public Guid AddUserPermissionId { get; }
        public Guid UpdateUserPermissionId { get; }
        public Guid DeleteUserPermissionId { get; }

        public Guid RolePermissionId { get; }
        public Guid ViewRolePermissionId { get; }
        public Guid AddRolePermissionId { get; }
        public Guid UpdateRolePermissionId { get; }
        public Guid DeleteRolePermissionId { get; }
        #endregion

        #region default role permissions
        public Guid DefaultCompanyManagementPermissionId { get; }
        public Guid DefaultCompanyPermissionId { get; }
        public Guid DefaultViewCompanyPermissionId { get; }
        public Guid DefaultAddCompanyPermissionId { get; }
        public Guid DefaultUpdateCompanyPermissionId { get; }
        public Guid DefaultDeleteCompanyPermissionId { get; }

        public Guid DefaultAuthentificationPermissionId { get; }
        public Guid DefaultUserPermissionId { get; }
        public Guid DefaultViewUserPermissionId { get; }
        public Guid DefaultAddUserPermissionId { get; }
        public Guid DefaultUpdateUserPermissionId { get; }
        public Guid DefaultDeleteUserPermissionId { get; }

        public Guid DefaultRolePermissionId { get; }
        public Guid DefaultViewRolePermissionId { get; }
        public Guid DefaultAddRolePermissionId { get; }
        public Guid DefaultUpdateRolePermissionId { get; }
        public Guid DefaultDeleteRolePermissionId { get; }
        #endregion

        #region company admin role permissions
        public Guid CompanyAdminCompanyManagementPermissionId { get; }
        public Guid CompanyAdminCompanyPermissionId { get; }
        public Guid CompanyAdminViewCompanyPermissionId { get; }
        public Guid CompanyAdminAddCompanyPermissionId { get; }
        public Guid CompanyAdminUpdateCompanyPermissionId { get; }
        public Guid CompanyAdminDeleteCompanyPermissionId { get; }

        public Guid CompanyAdminAuthentificationPermissionId { get; }
        public Guid CompanyAdminUserPermissionId { get; }
        public Guid CompanyAdminViewUserPermissionId { get; }
        public Guid CompanyAdminAddUserPermissionId { get; }
        public Guid CompanyAdminUpdateUserPermissionId { get; }
        public Guid CompanyAdminDeleteUserPermissionId { get; }

        public Guid CompanyAdminRolePermissionId { get; }
        public Guid CompanyAdminViewRolePermissionId { get; }
        public Guid CompanyAdminAddRolePermissionId { get; }
        public Guid CompanyAdminUpdateRolePermissionId { get; }
        public Guid CompanyAdminDeleteRolePermissionId { get; }
        #endregion

        #region Shop Admin role permissions
        public Guid ShopAdminCompanyManagementPermissionId { get; }
        public Guid ShopAdminCompanyPermissionId { get; }
        public Guid ShopAdminViewCompanyPermissionId { get; }
        public Guid ShopAdminAddCompanyPermissionId { get; }
        public Guid ShopAdminUpdateCompanyPermissionId { get; }
        public Guid ShopAdminDeleteCompanyPermissionId { get; }

        public Guid ShopAdminAuthentificationPermissionId { get; }
        public Guid ShopAdminUserPermissionId { get; }
        public Guid ShopAdminViewUserPermissionId { get; }
        public Guid ShopAdminAddUserPermissionId { get; }
        public Guid ShopAdminUpdateUserPermissionId { get; }
        public Guid ShopAdminDeleteUserPermissionId { get; }

        public Guid ShopAdminRolePermissionId { get; }
        public Guid ShopAdminViewRolePermissionId { get; }
        public Guid ShopAdminAddRolePermissionId { get; }
        public Guid ShopAdminUpdateRolePermissionId { get; }
        public Guid ShopAdminDeleteRolePermissionId { get; }
        #endregion

        public ConfigurationDefaultId()
        {
            string jsonFilePath = "Configuration/defaultIdConfig.json";

            if (File.Exists(jsonFilePath))
            {
                var json = File.ReadAllText(jsonFilePath);
                var jsonData = JsonConvert.DeserializeObject<Config>(json);

                CompanyId = jsonData.CompanyId;
                UserId = jsonData.UserId;
                UserClientId = jsonData.UserClientId;
                RoleId = jsonData.RoleId;
                RoleSuperAdminId = jsonData.RoleSuperAdminId;
                RoleCompanyAdminId = jsonData.RoleCompanyAdminId;
                RoleShopAdminId = jsonData.RoleShopAdminId;
                RoleClientId = jsonData.RoleClientId;
                RoleCashierId = jsonData.RoleCashierId;

                #region Permission

                #region Company
                CompanyManagementPermissionId = jsonData.CompanyManagementPermissionId;
                CompanyPermissionId= jsonData.CompanyPermissionId;
                ViewCompanyPermissionId = jsonData.ViewCompanyPermissionId;
                AddCompanyPermissionId= jsonData.AddCompanyPermissionId;
                UpdateCompanyPermissionId = jsonData.UpdateCompanyPermissionId;
                DeleteCompanyPermissionId = jsonData.DeleteCompanyPermissionId;
                #endregion

                #region Authentification

                AuthentificationPermissionId = jsonData.AuthentificationPermissionId;

                #region User
                UserPermissionId = jsonData.UserPermissionId;
                ViewUserPermissionId = jsonData.ViewUserPermissionId;
                AddUserPermissionId = jsonData.AddUserPermissionId;
                UpdateUserPermissionId = jsonData.UpdateUserPermissionId;
                DeleteUserPermissionId = jsonData.DeleteUserPermissionId;
                #endregion

                #region Role
                RolePermissionId = jsonData.RolePermissionId;
                ViewRolePermissionId = jsonData.ViewRolePermissionId;
                AddRolePermissionId = jsonData.AddRolePermissionId;
                UpdateRolePermissionId = jsonData.UpdateRolePermissionId;
                DeleteRolePermissionId = jsonData.DeleteRolePermissionId;
                #endregion

                #endregion

                #endregion


                #region Default Permission

                #region Company
                DefaultCompanyManagementPermissionId = jsonData.DefaultCompanyManagementPermissionId;
                DefaultCompanyPermissionId = jsonData.DefaultCompanyPermissionId;
                DefaultViewCompanyPermissionId = jsonData.DefaultViewCompanyPermissionId;
                DefaultAddCompanyPermissionId = jsonData.DefaultAddCompanyPermissionId;
                DefaultUpdateCompanyPermissionId = jsonData.DefaultUpdateCompanyPermissionId;
                DefaultDeleteCompanyPermissionId = jsonData.DefaultDeleteCompanyPermissionId;
                #endregion

                #region Authentification

                DefaultAuthentificationPermissionId = jsonData.DefaultAuthentificationPermissionId;

                #region User
                DefaultUserPermissionId = jsonData.DefaultUserPermissionId;
                DefaultViewUserPermissionId = jsonData.DefaultViewUserPermissionId;
                DefaultAddUserPermissionId = jsonData.DefaultAddUserPermissionId;
                DefaultUpdateUserPermissionId = jsonData.DefaultUpdateUserPermissionId;
                DefaultDeleteUserPermissionId = jsonData.DefaultDeleteUserPermissionId;
                #endregion

                #region Role
                DefaultRolePermissionId = jsonData.DefaultRolePermissionId;
                DefaultViewRolePermissionId = jsonData.DefaultViewRolePermissionId;
                DefaultAddRolePermissionId = jsonData.DefaultAddRolePermissionId;
                DefaultUpdateRolePermissionId = jsonData.DefaultUpdateRolePermissionId;
                DefaultDeleteRolePermissionId = jsonData.DefaultDeleteRolePermissionId;
                #endregion

                #endregion

                #endregion

                #region company admin permissions 
                #region Company
                CompanyAdminCompanyManagementPermissionId = jsonData.CompanyAdminCompanyManagementPermissionId;
                CompanyAdminCompanyPermissionId = jsonData.CompanyAdminCompanyPermissionId;
                CompanyAdminViewCompanyPermissionId = jsonData.CompanyAdminViewCompanyPermissionId;
                CompanyAdminAddCompanyPermissionId = jsonData.CompanyAdminAddCompanyPermissionId;
                CompanyAdminUpdateCompanyPermissionId = jsonData.CompanyAdminUpdateCompanyPermissionId;
                CompanyAdminDeleteCompanyPermissionId = jsonData.CompanyAdminDeleteCompanyPermissionId;
                #endregion

                #region Authentification

                CompanyAdminAuthentificationPermissionId = jsonData.CompanyAdminAuthentificationPermissionId;

                #region User
                CompanyAdminUserPermissionId = jsonData.CompanyAdminUserPermissionId;
                CompanyAdminViewUserPermissionId = jsonData.CompanyAdminViewUserPermissionId;
                CompanyAdminAddUserPermissionId = jsonData.CompanyAdminAddUserPermissionId;
                CompanyAdminUpdateUserPermissionId = jsonData.CompanyAdminUpdateUserPermissionId;
                CompanyAdminDeleteUserPermissionId = jsonData.CompanyAdminDeleteUserPermissionId;
                #endregion

                #region Role
                CompanyAdminRolePermissionId = jsonData.CompanyAdminRolePermissionId;
                CompanyAdminViewRolePermissionId = jsonData.CompanyAdminViewRolePermissionId;
                CompanyAdminAddRolePermissionId = jsonData.CompanyAdminAddRolePermissionId;
                CompanyAdminUpdateRolePermissionId = jsonData.CompanyAdminUpdateRolePermissionId;
                CompanyAdminDeleteRolePermissionId = jsonData.CompanyAdminDeleteRolePermissionId;
                #endregion

                #endregion
                #endregion

                #region Shop admin permissions 
                #region Company
                ShopAdminCompanyManagementPermissionId = jsonData.ShopAdminCompanyManagementPermissionId;
                ShopAdminCompanyPermissionId = jsonData.ShopAdminCompanyPermissionId;
                ShopAdminViewCompanyPermissionId = jsonData.ShopAdminViewCompanyPermissionId;
                ShopAdminAddCompanyPermissionId = jsonData.ShopAdminAddCompanyPermissionId;
                ShopAdminUpdateCompanyPermissionId = jsonData.ShopAdminUpdateCompanyPermissionId;
                ShopAdminDeleteCompanyPermissionId = jsonData.ShopAdminDeleteCompanyPermissionId;
                #endregion

                #region Authentification

                ShopAdminAuthentificationPermissionId = jsonData.ShopAdminAuthentificationPermissionId;

                #region User
                ShopAdminUserPermissionId = jsonData.ShopAdminUserPermissionId;
                ShopAdminViewUserPermissionId = jsonData.ShopAdminViewUserPermissionId;
                ShopAdminAddUserPermissionId = jsonData.ShopAdminAddUserPermissionId;
                ShopAdminUpdateUserPermissionId = jsonData.ShopAdminUpdateUserPermissionId;
                ShopAdminDeleteUserPermissionId = jsonData.ShopAdminDeleteUserPermissionId;
                #endregion

                #region Role
                ShopAdminRolePermissionId = jsonData.ShopAdminRolePermissionId;
                ShopAdminViewRolePermissionId = jsonData.ShopAdminViewRolePermissionId;
                ShopAdminAddRolePermissionId = jsonData.ShopAdminAddRolePermissionId;
                ShopAdminUpdateRolePermissionId = jsonData.ShopAdminUpdateRolePermissionId;
                ShopAdminDeleteRolePermissionId = jsonData.ShopAdminDeleteRolePermissionId;
                #endregion

                #endregion
                #endregion

            }
            else
            {
                // Handle the case when the JSON file doesn't exist.
                // You can set default values or throw an exception.
            }
        }

        private class Config
        {
            public Guid CompanyId { get; set; } = Guid.NewGuid();
            public Guid UserId { get; set; } = Guid.NewGuid();
            public Guid UserClientId { get; set; } = Guid.NewGuid();
            public Guid RoleId { get; set; } = Guid.NewGuid();

            public Guid RoleSuperAdminId { get; set; } = Guid.NewGuid();
            public Guid RoleCompanyAdminId { get; set; } = Guid.NewGuid();
            public Guid RoleShopAdminId { get; set; } = Guid.NewGuid();
            public Guid RoleClientId { get; set; } = Guid.NewGuid();
            public Guid RoleCashierId { get; set; } = Guid.NewGuid();

            #region Permission

            public Guid CompanyManagementPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid ViewCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid AddCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid UpdateCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid DeleteCompanyPermissionId { get; set; } = Guid.NewGuid();

            public Guid AuthentificationPermissionId { get; set; } = Guid.NewGuid();
            public Guid UserPermissionId { get; set; } = Guid.NewGuid();
            public Guid ViewUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid AddUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid UpdateUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid DeleteUserPermissionId { get; set; } = Guid.NewGuid();

            public Guid RolePermissionId { get; set; } = Guid.NewGuid();
            public Guid ViewRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid AddRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid UpdateRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid DeleteRolePermissionId { get; set; } = Guid.NewGuid();

            #endregion

            #region Default Permission
            public Guid DefaultCompanyManagementPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultViewCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultAddCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultUpdateCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultDeleteCompanyPermissionId { get; set; } = Guid.NewGuid();

            public Guid DefaultAuthentificationPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultViewUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultAddUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultUpdateUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultDeleteUserPermissionId { get; set; } = Guid.NewGuid();

            public Guid DefaultRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultViewRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultAddRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultUpdateRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid DefaultDeleteRolePermissionId { get; set; } = Guid.NewGuid();
            #endregion

            #region Admin Company Permissions
            public Guid CompanyAdminCompanyManagementPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminViewCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminAddCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminUpdateCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminDeleteCompanyPermissionId { get; set; } = Guid.NewGuid();

            public Guid CompanyAdminAuthentificationPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminViewUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminAddUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminUpdateUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminDeleteUserPermissionId { get; set; } = Guid.NewGuid();

            public Guid CompanyAdminRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminViewRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminAddRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminUpdateRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid CompanyAdminDeleteRolePermissionId { get; set; } = Guid.NewGuid();
            #endregion

            #region Shop Admin Permission
            public Guid ShopAdminCompanyManagementPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminViewCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminAddCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminUpdateCompanyPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminDeleteCompanyPermissionId { get; set; } = Guid.NewGuid();

            public Guid ShopAdminAuthentificationPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminViewUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminAddUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminUpdateUserPermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminDeleteUserPermissionId { get; set; } = Guid.NewGuid();

            public Guid ShopAdminRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminViewRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminAddRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminUpdateRolePermissionId { get; set; } = Guid.NewGuid();
            public Guid ShopAdminDeleteRolePermissionId { get; set; } = Guid.NewGuid();
            #endregion

        }
    }
}
