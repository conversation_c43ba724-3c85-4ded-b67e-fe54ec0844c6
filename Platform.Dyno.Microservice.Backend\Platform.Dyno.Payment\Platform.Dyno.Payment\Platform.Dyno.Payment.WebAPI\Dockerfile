#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["Platform.Dyno.Paiements.WebAPI/Platform.Dyno.Paiements.WebAPI.csproj", "Platform.Dyno.Paiements.WebAPI/"]
RUN dotnet restore "Platform.Dyno.Paiements.WebAPI/Platform.Dyno.Paiements.WebAPI.csproj"
COPY . .
WORKDIR "/src/Platform.Dyno.Paiements.WebAPI"
RUN dotnet build "Platform.Dyno.Paiements.WebAPI.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Platform.Dyno.Paiements.WebAPI.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Platform.Dyno.Paiements.WebAPI.dll"]