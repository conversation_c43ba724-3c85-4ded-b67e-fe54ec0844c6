﻿using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.SharedClass.HttpHelper
{
    public interface IHelper<DTO>
    {
        #region Get Without autorization
        public ResponseAPI<DTO>? Get(string endPoint, Guid id, string? authorization = null);
        public ResponseAPI<DTO>? Get(string endPoint, string? authorization = null);
        public ResponseAPI<List<DTO>>? GetAll(string endPoint, Dictionary<string, string>? queryParams = null, string? authorization = null);
        public ResponseAPI<decimal> GetTotalBalanceByUserType(string endPoint, Dictionary<string, string>? queryParams = null, string? authorization = null);
        public Task<ResponseAPI<List<DTO>>> GetAllAsync(string endPoint, Dictionary<string, string>? queryParams = null, CancellationToken cancellationToken = default);
        #endregion

        #region Post
        public ResponseAPI<DTO>? Post(string endPoint, DTO element, Dictionary<string, string>? queryParams = null, string ? authorization = null);
        public ResponseAPI<List<DTO>>? AddRange(string endPoint, List<DTO> element, Dictionary<string, string>? queryParams = null, string? authorization = null);
        public Task<ResponseAPI<DTO>?> PostAsync(string endPoint, DTO element, Dictionary<string, string>? queryParams = null, string? authorization = null);
        #endregion

        #region Patch
        public ResponseAPI<DTO>? Patch(string endPoint, DTO? element, Dictionary<string, string>? queryParams = null, string? authorization = null);
        #endregion

        #region Put
        public ResponseAPI<DTO>? Put(string endPoint, DTO element, Dictionary<string, string>? queryParams = null, string? authorization = null);
        #endregion

        #region Delete
        ResponseAPI<DTO>? Delete(string endPoint, Guid id, string? authorization = null);
        #endregion
    }
}
