﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Platform.Dyno.AccessManagement.Business.IService.Transaction;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.AccessManagement.DTO.Transaction;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Transaction
{
    [Route("api/[controller]")]
    [ApiController]
    public class RefundTransactionController : ControllerBase
    {
        private readonly IRefundTransactionService _refundTransactionService;
        private readonly ILogger<RefundTransactionController> _logger;
        private readonly Platform.Dyno.Shared.Configuration _configuration;
        public RefundTransactionController(IRefundTransactionService refundTransactionService,
            ILogger<RefundTransactionController> logger,
            Platform.Dyno.Shared.Configuration configuration)
        {
            _refundTransactionService = refundTransactionService;
            _logger = logger;
            _configuration = configuration;
        }


        #region Get
        [Route("GetRefundDemands")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetRefundDemands()
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<List<CashierRefundTransactionsDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<List<CashierRefundTransactionsDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? companyId = HttpContext.User.FindFirstValue("Company");
                if (companyId == null)
                {
                    return Unauthorized(new ResponseAPI<List<CashierRefundTransactionsDTO>>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<List<CashierRefundTransactionsDTO>>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }
                    ResponseAPI<List<CashierRefundTransactionsDTO>> response = _refundTransactionService.GetRefundDemands(new Guid(companyId));
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<List<CashierRefundTransactionsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetRefundDemands)}");
                ResponseAPI<List<CashierRefundTransactionsDTO>> response = new ResponseAPI<List<CashierRefundTransactionsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Create
        [Route("DemandeCancledTransaction/{transactionId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult DemandeCancledTransaction([FromHeader] string authorization, Guid transactionId)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if(enumUserType != UserType.Cashier)
                    {
                        return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.DemandeCancledTransaction(transactionId, new Guid(userId));
                    if (response.StatusCode != HttpStatusCode.Created)
                    {
                        return BadRequest(response);
                    }
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(DemandeCancledTransaction)}");
                ResponseAPI<RefundTransactionDTO> response = new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("ValidateCancledTransaction/{transactionId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult ValidateCancledTransaction([FromHeader] string authorization, Guid transactionId)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.ValidateCancledTransaction(transactionId, new Guid(userId));
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        return BadRequest(response);
                    }
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(ValidateCancledTransaction)}");
                ResponseAPI<RefundTransactionDTO> response = new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("RejectCancledTransaction/{transactionId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult RejectCancledTransaction([FromHeader] string authorization, Guid transactionId)
        {
            try
            {
                string? userId = HttpContext.User.FindFirstValue("Id");
                if (userId == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                string? userType = HttpContext.User.FindFirstValue("UserType");
                if (userType == null)
                {
                    return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                if (Enum.TryParse(userType, out UserType enumUserType))
                {
                    if (enumUserType != UserType.ShopOwner)
                    {
                        return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                        {
                            StatusCode = System.Net.HttpStatusCode.Unauthorized,
                            ExceptionMessage = "User unauthorized !"
                        });
                    }

                    ResponseAPI<RefundTransactionDTO> response = _refundTransactionService.RejectCancledTransaction(transactionId, new Guid(userId));
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        return BadRequest(response);
                    }
                    return Ok(response);
                }

                return Unauthorized(new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.Unauthorized,
                    ExceptionMessage = "User unauthorized !"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(RejectCancledTransaction)}");
                ResponseAPI<RefundTransactionDTO> response = new ResponseAPI<RefundTransactionDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

    }
}
