﻿using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Wallet
{
    public interface IWalletService
    {
        public WalletDTO GetWalletAssignedToName(WalletDTO walletDTOList);
        public List<WalletDTO> GetWalletsAssignedToName(List<WalletDTO> walletDTOList);
    }
}
