﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.Business.Service.Reporting;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using QuestPDF;
using QuestPDF.Infrastructure;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Reporting
{
    [Route("api/[controller]")]
    [ApiController]
    public class InvoicePDFController : ControllerBase
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<InvoicePDFController> _logger;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        private readonly Platform.Dyno.Shared.Configuration _configuration;
        private readonly ISortingUtility _sortingUtility;

        public InvoicePDFController(IInvoiceService invoiceService,
            ILogger<InvoicePDFController> logger,
            ConfigurationDefaultId configurationDefaultId,
            Platform.Dyno.Shared.Configuration configuration,
            ISortingUtility sortingUtility)
        {
            _invoiceService = invoiceService;
            _logger = logger;
            _configurationDefaultId = configurationDefaultId;
            _configuration = configuration;
            _sortingUtility = sortingUtility;
        }


        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                ResponseAPI<List<DocumentsDTO>> documents = _invoiceService.GetAll();
                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<DocumentsDTO>> documents = _invoiceService.GetAll(pagedParameters);

                if (documents.ObjectValue != null)
                {
                    documents.ObjectValue = _sortingUtility.SortData(pagedParameters, documents);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = documents.ObjectValue.TotalCount,
                        PageSize = documents.ObjectValue.PageSize,
                        CurrentPage = documents.ObjectValue.CurrentPage,
                        TotalPages = documents.ObjectValue.TotalPages,
                        HasNext = documents.ObjectValue.HasNext,
                        HasPrevious = documents.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }

                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<DocumentsDTO> document = _invoiceService.Get(id);
                return Ok(document);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

        #region Create 
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Add(SalesOrderDTO salesOrder)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<DocumentsDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                string? companyReceiverId = HttpContext.User.FindFirstValue("Company");
                if (companyReceiverId == null)
                {
                    return Unauthorized(new ResponseAPI<MacAddressDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.Unauthorized,
                        ExceptionMessage = "User unauthorized !"
                    });
                }

                ResponseAPI<DocumentsDTO> response = await _invoiceService.CreatePDF(salesOrder.Code,
                    salesOrder.ProductType,
                    salesOrder.DynoAmount,
                    salesOrder.NetAmount,
                    salesOrder.VATAmount,
                    salesOrder.TotalAmount,
                    salesOrder.PaymentMethod,
                    new Guid(companyReceiverId),
                    _configurationDefaultId.CompanyId,
                    DocumentType.Invoice
                    );



                if (response.StatusCode == System.Net.HttpStatusCode.Created && response.ObjectValue != null)
                {
                    var pdfLink= $"{_configuration.AccessManagementAddress}/PDF/Invoice/{response.ObjectValue.Name}.pdf";
                    return Ok(pdfLink);
                }
                return BadRequest(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<List<DocumentsDTO>> response = new ResponseAPI<List<DocumentsDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        #endregion

    }
}
