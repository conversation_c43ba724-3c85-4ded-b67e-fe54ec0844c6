﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class addrnevatInCompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("00ec879c-de9c-4410-8ab4-62e6f7169835"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0ef2a3a1-95cf-47d2-9f01-83d22ff02359"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("154e6e38-4fbe-4d40-ae1b-02a907c1408e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("155d0062-f896-48e3-af1e-e36429084a51"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1dc03eb8-467b-43b2-b104-9124af5a62dd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("21211758-30b2-4222-be22-9c4065c155ff"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2e384021-cbd8-405b-8b9b-365384a008c7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3d8043f6-297b-4de5-8b30-f7bd8a61bd38"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("404e1c94-d42b-4727-9285-646da8ce5623"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("540f6b1d-f177-4702-862b-edfe0f7033cf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("54542c70-1670-41df-9a05-00cf29b0e221"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5bd1f892-63ae-4bcf-9c60-339202fc9d82"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("72ca6f73-7afe-48ba-9306-09624fd8221b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("78b8f760-f466-4aa0-aa9f-7fd2205cbf73"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7e004f36-b49e-4409-8351-e8ff7950d401"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("869dcfdc-9350-4538-99a8-87bcaeea34a2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bf2ab398-4487-40f9-95be-e862083e4bec"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c3c0c6fe-249a-4ab0-b470-e0e8f3667bef"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c5a29f14-3299-4ac4-998e-19fcd1b63c75"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d6dbac4f-e8f4-458c-821f-f781bfd9ce2b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e79081c0-e52c-47fa-ac2a-404d2a474d27"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f364c2e8-49b4-437a-a8c9-696007b20854"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f66b8182-afd4-4f6a-a453-b4ee05c862cf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f9082292-f575-4cfd-9e31-a634426215f3"));

            migrationBuilder.DeleteData(
                table: "RoleUser",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"), new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb70") });

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("157da159-f7da-4b21-ad38-2975391bf43b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1c31de79-2406-4365-ae90-2e77409f4972"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("58e97429-64c6-4a99-a10a-a9770f357272"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1237313c-c28f-4f0e-b7d5-4191c0845aa8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cc46d60e-835d-4cb7-bf16-87d5620e47d7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f1860454-6320-448e-97cd-a289a85e871e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"));

            migrationBuilder.RenameColumn(
                name: "TaxRegistration",
                table: "Company",
                newName: "VAT");

            migrationBuilder.AddColumn<string>(
                name: "RNECode",
                table: "Company",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Company",
                keyColumn: "Id",
                keyValue: new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                columns: new[] { "RNECode", "VAT" },
                values: new object[] { "1803838P", "000 M A 1803838P" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c0662728-170f-4608-8790-a75002485c5e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "82405186-493a-4588-b063-dcb990d38bf5", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8023) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0a833934-4eb2-4ee6-ac8b-60c5b9cc80d2", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8001) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "e35130ca-60b5-409e-87fb-b3b4d4093406", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8011) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "efacfc0c-864a-496e-8dbe-47d803eaa2f9", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8027) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "c55dbbd5-2ada-4a41-8179-425e5d2e749c", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8020) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a136db30-3f8e-4a22-85eb-fe7175a41cd2", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8016) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "8b451254-5d0e-4d90-b5fe-d207f8cf8694", new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8292), new DateTime(2024, 3, 18, 14, 8, 17, 620, DateTimeKind.Utc).AddTicks(8292), "AQAAAAEAACcQAAAAEIha7wm1O0eg6VE2ODmXNWKHkpkYoTqQjUcbZrHR3nLyOVLMKvYHFTaJkrFZn+1f/g==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("c0662728-170f-4608-8790-a75002485c5e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("49694334-e359-499d-ba36-a659953650c9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("c0662728-170f-4608-8790-a75002485c5e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("109bafac-2fe4-40e7-87fe-590c987dc848"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1319a316-0442-4a9c-9106-6a18faa58dc5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1a8360a6-2433-4c1e-80f0-8a7613ef6c93"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1a97ef7d-13e5-40db-a7a2-64254875b4e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("20498995-70c3-467a-9978-f937f9091269"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("31877d89-32e6-4a31-8d2d-322ad5dcb8dc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3f2aa28d-d2f7-40ef-bdc6-c3e61a4241c1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("50ce0cd0-1cb0-42ce-a6cb-b5f69e4c63a1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5646ae84-55c7-489e-a16d-b175b006e495"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5c2fde05-aa88-41f7-ba7d-9d511b3e8557"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("68a6aa59-fe68-4267-b431-1706c194edac"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("69ea35e9-bc06-487c-9f68-6444a75430c0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("71af6b29-ca7d-4856-9c9f-5227bc8d2342"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("76f874d4-44fd-4e31-ab6e-64f1792d7759"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8379e6bf-ed60-4d5e-8bab-68020ee3764f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9319e86f-3a58-4a9e-98af-073bd780a4f1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("a75c0dfe-03de-48ae-b855-e5b2459c0a6f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b801c653-b091-4d37-b08f-43ee5395b813"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b9b46627-9b78-473f-8a69-eb1899255408"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d1fa49ed-76ce-44b2-a483-d6fa374e4766"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e6a33497-1a82-4c6e-b926-f03348b16bc5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e7a96040-1ed4-479a-befc-ade376979634"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e866cb5f-b3fd-494a-87c3-2bc6e0ac52a6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("49694334-e359-499d-ba36-a659953650c9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e99490ce-86f9-4dde-81b9-061203d1fe89"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("109bafac-2fe4-40e7-87fe-590c987dc848"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1319a316-0442-4a9c-9106-6a18faa58dc5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1a8360a6-2433-4c1e-80f0-8a7613ef6c93"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1a97ef7d-13e5-40db-a7a2-64254875b4e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("20498995-70c3-467a-9978-f937f9091269"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("31877d89-32e6-4a31-8d2d-322ad5dcb8dc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3f2aa28d-d2f7-40ef-bdc6-c3e61a4241c1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50ce0cd0-1cb0-42ce-a6cb-b5f69e4c63a1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5646ae84-55c7-489e-a16d-b175b006e495"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5c2fde05-aa88-41f7-ba7d-9d511b3e8557"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("68a6aa59-fe68-4267-b431-1706c194edac"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("69ea35e9-bc06-487c-9f68-6444a75430c0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("71af6b29-ca7d-4856-9c9f-5227bc8d2342"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("76f874d4-44fd-4e31-ab6e-64f1792d7759"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8379e6bf-ed60-4d5e-8bab-68020ee3764f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9319e86f-3a58-4a9e-98af-073bd780a4f1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a75c0dfe-03de-48ae-b855-e5b2459c0a6f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b801c653-b091-4d37-b08f-43ee5395b813"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b9b46627-9b78-473f-8a69-eb1899255408"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d1fa49ed-76ce-44b2-a483-d6fa374e4766"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e6a33497-1a82-4c6e-b926-f03348b16bc5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e7a96040-1ed4-479a-befc-ade376979634"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e866cb5f-b3fd-494a-87c3-2bc6e0ac52a6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e99490ce-86f9-4dde-81b9-061203d1fe89"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("477476ae-24c3-4e42-abb2-49d53c9a2688"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("49694334-e359-499d-ba36-a659953650c9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ec8af02-15ec-43d7-9471-6867b3def1a3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7792cee1-7d20-49d5-aacb-34d73188aee7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b7cd4ff9-eda0-4a1d-a36a-76ad579ebd98"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("da25d168-6dec-4734-9a9e-8fb6351571de"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("21454c0d-e214-4cda-83fe-2c7096b0fc8d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("95263907-cf90-41aa-b64e-6ac50376fb56"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("aa098f90-412c-4cec-a4fb-8099e576a443"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c0662728-170f-4608-8790-a75002485c5e"));

            migrationBuilder.DropColumn(
                name: "RNECode",
                table: "Company");

            migrationBuilder.RenameColumn(
                name: "VAT",
                table: "Company",
                newName: "TaxRegistration");

            migrationBuilder.UpdateData(
                table: "Company",
                keyColumn: "Id",
                keyValue: new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"),
                column: "TaxRegistration",
                value: "1803838P");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("1237313c-c28f-4f0e-b7d5-4191c0845aa8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("cc46d60e-835d-4cb7-bf16-87d5620e47d7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f1860454-6320-448e-97cd-a289a85e871e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "00db659a-43e8-4e48-b14f-402fde21c2bc", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9556) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "c5e8e0f5-0620-4b70-b718-3c87d4b0b561", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9531) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "adf1c387-cd96-4b08-87c3-a48f540e527f", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9540) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f99ae2b1-9c80-482a-b293-644806477a94", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9573) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "baa475f8-522b-4143-8fad-f2e47e1bb084", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9550) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "622856f9-e8e4-46c3-adbe-b537c6a5259a", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9545) });

            migrationBuilder.InsertData(
                table: "RoleUser",
                columns: new[] { "RoleId", "UserId" },
                values: new object[] { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"), new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb70") });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "c4bf2f2a-a846-4ae4-b264-ce3171efd9bb", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9945), new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9944), "AQAAAAEAACcQAAAAECssTfMVvC/F+jL3HBdzvwHh66CG4AOOPuWK4T2fvcpNQNN6ZAjGYtKFFJHgkt3bEg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("cc46d60e-835d-4cb7-bf16-87d5620e47d7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("f1860454-6320-448e-97cd-a289a85e871e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("1237313c-c28f-4f0e-b7d5-4191c0845aa8"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("f1860454-6320-448e-97cd-a289a85e871e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("00ec879c-de9c-4410-8ab4-62e6f7169835"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("0ef2a3a1-95cf-47d2-9f01-83d22ff02359"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("154e6e38-4fbe-4d40-ae1b-02a907c1408e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("155d0062-f896-48e3-af1e-e36429084a51"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1dc03eb8-467b-43b2-b104-9124af5a62dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("21211758-30b2-4222-be22-9c4065c155ff"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2e384021-cbd8-405b-8b9b-365384a008c7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3d8043f6-297b-4de5-8b30-f7bd8a61bd38"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("404e1c94-d42b-4727-9285-646da8ce5623"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("540f6b1d-f177-4702-862b-edfe0f7033cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("54542c70-1670-41df-9a05-00cf29b0e221"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5bd1f892-63ae-4bcf-9c60-339202fc9d82"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("72ca6f73-7afe-48ba-9306-09624fd8221b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("78b8f760-f466-4aa0-aa9f-7fd2205cbf73"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7e004f36-b49e-4409-8351-e8ff7950d401"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("869dcfdc-9350-4538-99a8-87bcaeea34a2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("bf2ab398-4487-40f9-95be-e862083e4bec"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c3c0c6fe-249a-4ab0-b470-e0e8f3667bef"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c5a29f14-3299-4ac4-998e-19fcd1b63c75"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d6dbac4f-e8f4-458c-821f-f781bfd9ce2b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e79081c0-e52c-47fa-ac2a-404d2a474d27"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f364c2e8-49b4-437a-a8c9-696007b20854"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f66b8182-afd4-4f6a-a453-b4ee05c862cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f9082292-f575-4cfd-9e31-a634426215f3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }
    }
}
