﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Notification.Business.Service;
using Platform.Dyno.Notification.DTO.Emailing;
using Platform.Dyno.Notification.DTO.Notification;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.Notification.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly ILogger<NotificationController> _logger;
        private readonly NotificationService<NotificationDTO> _notificationService;
        private readonly NotificationService<WatchDogNotifDTO> _watchdogNotificationService;
        private readonly NotificationService<double> _balanceNotificationService;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public NotificationController(ILogger<NotificationController> logger,
            NotificationService<NotificationDTO> notificationService,
            NotificationService<double> balanceNotificationService,
            NotificationService<WatchDogNotifDTO> watchdogNotificationService,
            ConfigurationDefaultId configurationDefaultId)
        {
            _logger = logger;
            _notificationService = notificationService;
            _balanceNotificationService = balanceNotificationService;
            _watchdogNotificationService = watchdogNotificationService;
            _configurationDefaultId = configurationDefaultId;
        }

        [Route("Send")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Add([FromBody] NotificationDTO notification)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<NotificationDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                foreach(string group in notification.SendToGroup)
                {
                    string groupName = $"/Notify/{group}";
                    await _notificationService.SendNotification(notification, groupName);
                }
                
                return Ok(new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<NotificationDTO> response = new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("SendBalance")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SendBalance(BalanceNotificationDTO balanceNotification)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<NotificationDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                string groupName = $"/Balance/{balanceNotification.GroupName}";
                await _balanceNotificationService.SendNotification(balanceNotification.Balance, groupName);

                return Ok(new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<NotificationDTO> response = new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("SendWatchDog")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SendWatchDog([FromBody] WatchDogNotifDTO notification)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<NotificationDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                string groupName = $"/WatchNotify/{_configurationDefaultId.CompanyId}";
                await _watchdogNotificationService.SendNotification(notification, groupName);

                return Ok(new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Add)}");
                ResponseAPI<NotificationDTO> response = new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("SalesOrderStatusNotif")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SalesOrderStatusNotif([FromBody] NotificationDTO notification)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<NotificationDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }
                foreach (string group in notification.SendToGroup)
                {
                    string groupName = $"/SalesOrderStatusNotif/{group}";
                    await _notificationService.SendNotification(notification, groupName);
                }

                return Ok(new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.OK
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(SalesOrderStatusNotif)}");
                ResponseAPI<NotificationDTO> response = new ResponseAPI<NotificationDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
    }
}
