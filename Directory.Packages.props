<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
    <NoWarn>$(NoWarn);NU1507</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="14.0.0" />
    <PackageVersion Include="AWSSDK.S3" Version="4.0.1.2" />
    <PackageVersion Include="AWSSDK.SimpleNotificationService" Version="4.0.0.4" />
    <PackageVersion Include="Azure.Identity" Version="1.14.0" />
    <PackageVersion Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageVersion Include="Camunda.Api.Client" Version="2.6.0" />
    <PackageVersion Include="Camunda.Worker" Version="0.16.0" />
    <PackageVersion Include="Confluent.Kafka" Version="2.10.0" />
    <PackageVersion Include="EnyimMemcachedCore" Version="3.4.0" />
    <PackageVersion Include="EPPlus" Version="8.0.5" />
    <PackageVersion Include="HarfBuzzSharp.NativeAssets.Linux" Version="8.3.1.1" />
    <PackageVersion Include="jQuery" Version="3.7.1" />
    <PackageVersion Include="libphonenumber-csharp" Version="9.0.6" />
    <PackageVersion Include="MailKit" Version="4.12.1" />
    <PackageVersion Include="MessagePack" Version="3.1.3" />
    <PackageVersion Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageVersion Include="Microsoft.AspNet.SignalR" Version="2.4.3" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageVersion Include="Microsoft.AspNetCore.Authorization" Version="9.0.5" />
    <PackageVersion Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.ObjectPool" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.WebEncoders" Version="9.0.5" />
    <PackageVersion Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.12.0" />
    <PackageVersion Include="Microsoft.Owin" Version="4.2.2" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageVersion Include="MimeKit" Version="4.12.0" />
    <PackageVersion Include="Neo4j.Driver" Version="5.28.1" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Npgsql" Version="9.0.3" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Polly" Version="8.5.2" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="protobuf-net" Version="3.2.52" />
    <PackageVersion Include="protobuf-net.Core" Version="3.2.52" />
    <PackageVersion Include="QRCoder" Version="1.6.0" />
    <PackageVersion Include="QuestPDF" Version="2025.5.0" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="RestSharp" Version="112.1.0" />
    <PackageVersion Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="3.119.0" />
    <PackageVersion Include="StackExchange.Redis" Version="2.8.37" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.4" />
    <PackageVersion Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.4" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Security.Cryptography.Xml" Version="9.0.5" />
    <PackageVersion Include="System.Text.Json" Version="9.0.5" />
    <PackageVersion Include="Z.EntityFramework.Plus.EFCore" Version="9.103.8.1" />
  </ItemGroup>
</Project>