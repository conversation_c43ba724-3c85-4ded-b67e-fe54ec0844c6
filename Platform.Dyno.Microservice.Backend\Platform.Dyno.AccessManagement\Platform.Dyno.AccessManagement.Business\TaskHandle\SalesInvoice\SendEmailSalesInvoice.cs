﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using static Camunda.Api.Client.Filter.FilterInfo;
using Platform.Dyno.Shared.SharedClass.EmailTemplate;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("Send_Email_SI")]
    public class SendEmailSalesInvoice : IExternalTaskHandler
    {
        private readonly ILogger<SendEmailSalesInvoice> _logger;
        private readonly IEmailingService _emailingService;
        private readonly ICompanyService _companyService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public SendEmailSalesInvoice(
        ILogger<SendEmailSalesInvoice> logger,
        IEmailingService emailingService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId,
        ICompanyService companyService)

        {
            _logger = logger;
            _emailingService = emailingService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
            _companyService = companyService;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
                SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
                //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());
                //string? PDFLink = externalTask.Variables["PDFLink"].Value?.ToString();
                string? PDFLink = null;

                if (externalTask.Variables.TryGetValue("PDFLink", out var varItem))
                {
                    if (varItem is StringVariable strVar && !string.IsNullOrWhiteSpace(strVar.Value))
                    {
                        PDFLink = strVar.Value;
                        _logger.LogInformation("Lien PDF récupéré : {PDFLink}", PDFLink);
                    }
                    else
                    {
                        _logger.LogWarning("PDFLink présent mais format invalide");
                    }
                }
                else
                {
                    _logger.LogError("Variable PDFLink absente");
                }



                if (salesInvoiceDTO == null )
                {
                    outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(false));
                    outputVariables.Add("Error_Send_Email_SI", new StringVariable("json conversion failed"));

                }
                else if(PDFLink == null)
                {
                    outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(false));
                    outputVariables.Add("Error_Send_Email_SI", new StringVariable("PDF link conversion failed"));
                }
                else
                {
                    var company = _companyService.Get(salesInvoiceDTO.CompanyId);
                    if (company.ObjectValue != null)
                    {
                        var emailBody = EmailTemplateEN.SalesInvoiceEmailbody;
                        var content = emailBody.Replace("#URL#", PDFLink).Replace("[type_of_action]", "Dynos");
                        var response = _emailingService.sendSalesInvoiceEmail(company.ObjectValue.Email, content);
                        if (response != null && response.StatusCode == System.Net.HttpStatusCode.Accepted)
                        {
                            outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(true));
                        }
                        else
                        {
                            outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(false));
                            outputVariables.Add("Error_Send_Email_SI", new JsonVariable(JsonSerializer.Serialize(response)));
                        }
                    }
                    else
                    {
                        outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(false));
                        outputVariables.Add("Error_Send_Email_SI", new JsonVariable(JsonSerializer.Serialize(company)));
                    }
                    
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_Send_Email_SI", new BooleanVariable(false));
                outputVariables.Add("Error_Send_Email_SI", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
