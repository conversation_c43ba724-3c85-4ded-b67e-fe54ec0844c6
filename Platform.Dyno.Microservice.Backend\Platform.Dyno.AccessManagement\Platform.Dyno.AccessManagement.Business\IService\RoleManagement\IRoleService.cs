﻿using Platform.Dyno.AccessManagement.DataModel.Role;
using Platform.Dyno.AccessManagement.DTO.Role;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.RoleManagement
{
    public interface IRoleService : IGenericService<RoleDTO>
    {       
    }
}
