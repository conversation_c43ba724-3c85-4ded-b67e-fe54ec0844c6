﻿using Domain.Enums.Query;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.IServices
{
    public interface ITransactionBlockchainService
    {
        public ResponseAPI<List<TransactionDTO>> GetAll();
        public ResponseAPI<TransactionDTO> Get(Guid id);

        public ResponseAPI<List<TransactionDTO>> GetUserTransactions(Guid userId);
        public ResponseAPI<List<TransactionDTO>> GetUserReceivedTransactions(Guid userId);
        public ResponseAPI<List<TransactionDTO>> GetUserSentTransactions(Guid userId);
        
        public ResponseAPI<List<TransactionDTO>> GetWalletReceivedTransactions(Guid walletId);
        public ResponseAPI<List<TransactionDTO>> GetWalletSentTransactions(Guid walletId);
        public ResponseAPI<List<TransactionDTO>> GetWalletTransactions(Guid walletId);


        public ResponseAPI<TransactionDTO> Create(TransactionDTO transactionDTO, Guid userId, string pinCode);
        public ResponseAPI<TransactionDTO> CreateTransactionToCompany(TransactionToCompanyDTO transactionDTO, Guid userId, string pinCode);
        public ResponseAPI<TransactionDTO> TransactionClientShop(TransactionDTO transactionDTO, Guid userId, string pinCode);
        public ResponseAPI<TransactionDTO> CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO, string pinCode);
        public ResponseAPI<List<TransactionUserDTO>> CreateTransactionsForGroup(TransactionGroupDTO transactionGroup, string pinCode);


        ResponseAPI<TransactionDTO> UpdateRefundStatus(Guid transactionId, RefundStatus refundStatus);
        public ResponseAPI<TransactionDTO> CancelTransaction(Guid transactionId, string pinCode);
    }
}
