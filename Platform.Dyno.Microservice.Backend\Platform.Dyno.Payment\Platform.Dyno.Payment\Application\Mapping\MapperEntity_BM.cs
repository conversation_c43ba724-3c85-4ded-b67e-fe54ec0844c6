﻿using Application.Models.BMs;
using AutoMapper;
using Domain.Entities;
using Neo4j.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Mapping
{
    public class MapperEntity_BM : Profile
    {
        public MapperEntity_BM() {
            CreateMap<WalletEntity, WalletBM>().ReverseMap();
            CreateMap<TransactionEntity, TransactionBM>().ReverseMap();
            CreateMap<RefundTransactionEntity, RefundTransactionBM>().ReverseMap();           
        }
        
    }
}
