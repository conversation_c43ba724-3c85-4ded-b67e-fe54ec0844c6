﻿using Microsoft.AspNetCore.SignalR;
using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Notification.DTO.Notification;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Notification.Business.Service
{
    public class NotificationService<T>
    {
        private readonly IHubContext<NotificationHub> _hubContext;
        public NotificationService(IHubContext<NotificationHub> hubContext) 
        {
            _hubContext= hubContext;
        }

        public async Task SendNotification(T notification, string groupName)
        {
            await _hubContext.Clients.Group(groupName).SendAsync("ReceiveNotification", notification);
        }
    }
}
