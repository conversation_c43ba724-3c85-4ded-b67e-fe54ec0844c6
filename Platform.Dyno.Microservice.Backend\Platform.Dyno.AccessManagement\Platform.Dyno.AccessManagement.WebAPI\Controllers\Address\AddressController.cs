﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.Address;
using Platform.Dyno.AccessManagement.Business.Service.Address;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.Address
{
    [Route("Api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<AddressController> _logger;
        public AddressController(IAddressService addressService, ILogger<AddressController> logger)
        {
            _addressService = addressService;
            _logger = logger;
        }

        #region Get
        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<AddressDTO>> addressesDTO = _addressService.GetAll(pagedParameters);
                if(addressesDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = addressesDTO.ObjectValue.TotalCount,
                        PageSize = addressesDTO.ObjectValue.PageSize,
                        CurrentPage = addressesDTO.ObjectValue.CurrentPage,
                        TotalPages = addressesDTO.ObjectValue.TotalPages,
                        HasNext = addressesDTO.ObjectValue.HasNext,
                        HasPrevious = addressesDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(addressesDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllActive")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllActive([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                ResponseAPI<PagedList<AddressDTO>> addressesDTO = _addressService.GetAllActive(pagedParameters);
                if(addressesDTO.ObjectValue != null)
                {
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = addressesDTO.ObjectValue.TotalCount,
                        PageSize = addressesDTO.ObjectValue.PageSize,
                        CurrentPage = addressesDTO.ObjectValue.CurrentPage,
                        TotalPages = addressesDTO.ObjectValue.TotalPages,
                        HasNext = addressesDTO.ObjectValue.HasNext,
                        HasPrevious = addressesDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(addressesDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllActive)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<AddressDTO> addressDTO = _addressService.Get(id);
                return Ok(addressDTO);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<AddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] AddressDTO addressDTO)
        {
            try
            {
                ResponseAPI<AddressDTO> response = _addressService.Create(addressDTO);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<AddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region Update
        [Route("Update")]
        [HttpPatch]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] AddressDTO addressDTO)
        {
            try
            {
                ResponseAPI<AddressDTO> response = _addressService.Update(addressDTO);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<AddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                ResponseAPI<AddressDTO> response = _addressService.Delete(id);
                return Ok(response);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<AddressDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        #endregion
    }

 }
