<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>1815c319-3b7f-456a-b5e4-ad57e0bcca0b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="AWSSDK.S3" />
    <PackageReference Include="jQuery" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.Owin" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="MimeKit" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions.Http" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Z.EntityFramework.Plus.EFCore" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj" />
    <ProjectReference Include="..\..\Server.Kafka\Server.Kafka\Server.Kafka.csproj" />
    <ProjectReference Include="..\Platform.Dyno.Notification.Business\Platform.Dyno.Notification.Business.csproj" />
  </ItemGroup>
</Project>