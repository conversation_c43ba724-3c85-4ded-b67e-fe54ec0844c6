using System.Text.RegularExpressions;
using System.ComponentModel.DataAnnotations;

namespace Platform.Dyno.Shared.Helpers
{
    public static class MobileValidationHelper
    {
        // Regex patterns optimisés pour mobile
        private static readonly Regex PhoneRegex = new(@"^(\+|00)?\d{1,4}\d{4,15}$", RegexOptions.Compiled);
        private static readonly Regex EmailRegex = new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.Compiled);
        private static readonly Regex MacAddressRegex = new(@"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", RegexOptions.Compiled);

        /// <summary>
        /// Valide un numéro de téléphone international
        /// </summary>
        public static bool IsValidPhoneNumber(string countryCode, string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(countryCode) || string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var fullNumber = countryCode + phoneNumber;
            return PhoneRegex.IsMatch(fullNumber);
        }

        /// <summary>
        /// Valide une adresse email
        /// </summary>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            return EmailRegex.IsMatch(email);
        }

        /// <summary>
        /// Valide une adresse MAC
        /// </summary>
        public static bool IsValidMacAddress(string macAddress)
        {
            if (string.IsNullOrWhiteSpace(macAddress))
                return false;

            return MacAddressRegex.IsMatch(macAddress);
        }

        /// <summary>
        /// Valide un PIN code (4-6 chiffres)
        /// </summary>
        public static bool IsValidPinCode(string pinCode)
        {
            if (string.IsNullOrWhiteSpace(pinCode))
                return false;

            return pinCode.Length >= 4 && pinCode.Length <= 6 && pinCode.All(char.IsDigit);
        }

        /// <summary>
        /// Valide un montant de transaction
        /// </summary>
        public static bool IsValidTransactionAmount(double amount, double maxAmount = 999999)
        {
            return amount > 0 && amount <= maxAmount;
        }

        /// <summary>
        /// Valide une plage de dates
        /// </summary>
        public static bool IsValidDateRange(DateTime? startDate, DateTime? endDate)
        {
            if (startDate == null)
                return true;

            if (endDate == null)
                return true;

            return endDate > startDate;
        }

        /// <summary>
        /// Valide un GUID
        /// </summary>
        public static bool IsValidGuid(string guidString)
        {
            return Guid.TryParse(guidString, out _);
        }

        /// <summary>
        /// Valide les paramètres de pagination pour mobile
        /// </summary>
        public static (bool isValid, int pageSize, int pageNumber) ValidatePaginationParameters(int? pageSize, int? pageNumber)
        {
            const int maxPageSize = 50; // Limite pour mobile
            const int defaultPageSize = 20;
            const int defaultPageNumber = 1;

            var validPageSize = pageSize ?? defaultPageSize;
            var validPageNumber = pageNumber ?? defaultPageNumber;

            // Validation des limites
            if (validPageSize <= 0 || validPageSize > maxPageSize)
                validPageSize = defaultPageSize;

            if (validPageNumber <= 0)
                validPageNumber = defaultPageNumber;

            return (true, validPageSize, validPageNumber);
        }

        /// <summary>
        /// Sanitize input pour éviter les injections
        /// </summary>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Supprime les caractères dangereux
            return input.Trim()
                       .Replace("<", "&lt;")
                       .Replace(">", "&gt;")
                       .Replace("\"", "&quot;")
                       .Replace("'", "&#x27;")
                       .Replace("/", "&#x2F;");
        }
    }
}
