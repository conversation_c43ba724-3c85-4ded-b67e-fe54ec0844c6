﻿using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.RefData;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Company
{
    public class PaymentDetailsDTO:ReferentialData
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public PaymentMethod PaymentMethod { get; set; }
        public string RIB { get; set; } = string.Empty;
        public int? PaymentDelay { get; set; } 
        public double? ShopFeePercentage { get; set; }
        public Guid CompanyId { get; set; }
    }
}
