﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Process
{
    public class StartProcessInstanceRequest
    {
        [JsonPropertyName("businessKey")]
        public string BusinessKey { get; set; }

        [JsonPropertyName("variables")]
        public Dictionary<string, VariableValue> Variables { get; set; }
    }

    public class VariableValue
    {
        [JsonPropertyName("value")]
        public object Value { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        public static VariableValue FromObject(object value)
        {
            return new VariableValue
            {
                Value = value,
                Type = "object" // "json" si besoin de sérialisation explicite
            };
        }
    }

    public class ProcessInstanceResult
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    public class CamundaError
    {
        [JsonPropertyName("message")]
        public string Message { get; set; }
    }
}
