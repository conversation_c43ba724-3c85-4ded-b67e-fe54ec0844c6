﻿using Application.Interfaces;
using Infrastructure.DatabaseConnection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Neo4j.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Infrastructure.Repository
{
    public class GenericRepository : IGenericRepository, IAsyncDisposable
    {
        private IAsyncSession _session;

        private ILogger<GenericRepository> _logger;

        public GenericRepository(IDriver driver, ILogger<GenericRepository> logger)
        {
            _logger = logger;
            _session = driver.AsyncSession(o => o.WithDatabase("neo4j"));
        }

        #region read
        public async Task<List<string>> ExecuteReadListAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null)
        {
            return await ExecuteReadTransactionAsync<string>(query, returnObjectKey, parameters);
        }

        public async Task<List<Dictionary<string, object>>> ExecuteReadDictionaryAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null)
        {
            return await ExecuteReadTransactionAsync<Dictionary<string, object>>(query, returnObjectKey, parameters);
        }

        public async Task<T> ExecuteReadScalarAsync<T>(string query, IDictionary<string, object>? parameters = null)
        {
            try
            {
                parameters = parameters == null ? new Dictionary<string, object>() : parameters;

                var result = await _session.ExecuteReadAsync(async tx =>
                {
                    T scalar = default(T);
                    var res = await tx.RunAsync(query, parameters);
                    scalar = (await res.SingleAsync())[0].As<T>();
                    return scalar;
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "There was a problem while executing database query : ExecuteReadScalarAsync");
                throw;
            }
        }
        private async Task<List<T>> ExecuteReadTransactionAsync<T>(string query, string returnObjectKey, IDictionary<string, object>? parameters)
        {
            try
            {
                parameters = parameters == null ? new Dictionary<string, object>() : parameters;

                var result = await _session.ExecuteReadAsync(async tx =>
                {
                    var data = new List<T>();
                    var res = await tx.RunAsync(query, parameters);
                    var records = await res.ToListAsync();
                    data = records.Select(x => (T)x[returnObjectKey]).ToList();
                    return data;
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "There was a problem while executing database query : ExecuteReadTransactionAsync");
                throw;
            }
        }
        #endregion

        #region write
        public async Task<List<Dictionary<string, object>>> ExecuteWriteDictionaryAsync(string query, string returnObjectKey, IDictionary<string, object>? parameters = null)
        {
            return await ExecuteWriteRangeTransactionAsync<Dictionary<string, object>>(query, returnObjectKey, parameters);
        }
        public async Task<T> ExecuteWriteTransactionAsync<T>(string query, IDictionary<string, object>? parameters = null)
        {
            try
            {
                parameters = parameters == null ? new Dictionary<string, object>() : parameters;

                var result = await _session.ExecuteWriteAsync(async tx =>
                {
                    T scalar = default(T);
                    var res = await tx.RunAsync(query, parameters);
                    scalar = (await res.SingleAsync())[0].As<T>();
                    return scalar;
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "There was a problem while executing database query : ExecuteWriteTransactionAsync");
                throw;
            }
        }
        public async Task<List<T>> ExecuteWriteRangeTransactionAsync<T>(string query,string returnObjectKey, IDictionary<string, object>? parameters = null)
        {
            try
            {
                parameters = parameters == null ? new Dictionary<string, object>() : parameters;

                var result = await _session.ExecuteWriteAsync(async tx =>
                {
                    var data = new List<T>();
                    var res = await tx.RunAsync(query, parameters);
                    var records = await res.ToListAsync();
                    data = records.Select(x => (T)x[returnObjectKey]).ToList();
                    return data;
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "There was a problem while executing database query : ExecuteWriteRangeTransactionAsync");
                throw;
            }
        }
        #endregion


        async ValueTask IAsyncDisposable.DisposeAsync()
        {
            await _session.CloseAsync();
        }
    }
}
