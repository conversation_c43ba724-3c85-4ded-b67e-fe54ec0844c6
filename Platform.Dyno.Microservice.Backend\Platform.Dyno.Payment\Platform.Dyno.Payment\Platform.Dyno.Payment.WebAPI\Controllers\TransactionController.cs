﻿using Application.IServices;
using Application.Models.BMs;
using Domain.Enums.Query;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;

namespace Platform.Dyno.Paiement.WebAPI.Controllers
{
    [Route("Api/[controller]")]
    [ApiController]
    public class TransactionController : ControllerBase
    {
        private readonly ITransactionService _transactionService;
        private readonly ILogger<TransactionController> _logger;

        public TransactionController(ITransactionService transactionService,
            ILogger<TransactionController> logger)
        {
            _transactionService = transactionService;
            _logger = logger;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {

                var transactions = _transactionService.GetAll();
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("GetAllByUserType")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllByUserType(UserType userType)
        {
            try
            {

                var transactions = _transactionService.GetAllByUserType(userType);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllByUserType)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        
        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged()
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);
                var response = _transactionService.GetAll(sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                ResponseAPI<TransactionDTO> response = _transactionService.Get(id);
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("CheckQRcodeExist/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CheckQRcodeExist(Guid id)
        {
            try
            {
                ResponseAPI<bool> response = _transactionService.CheckQRcodeExist(id);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CheckQRcodeExist)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        #region Get by user
        [Route("GetUserReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserReceivedTransactions([FromQuery] PagedParameters pagedParameters, Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);
                limit = pagedParameters.PageSize;
                skip = pagedParameters.PageSize * (pagedParameters.PageNumber - 1);

                var response = _transactionService.GetUserReceivedTransactions(userId,sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        
        [Route("GetUserSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserSentTransactions([FromQuery] PagedParameters pagedParameters, Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);

                limit = pagedParameters.PageSize;
                skip = pagedParameters.PageSize * (pagedParameters.PageNumber - 1);

                var response = _transactionService.GetUserSentTransactions(userId,sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        
        [Route("GetUserTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetUserTransactions([FromQuery] PagedParameters pagedParameters, Guid userId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);
                limit = pagedParameters.PageSize;
                skip = pagedParameters.PageSize * (pagedParameters.PageNumber - 1);
                var response = _transactionService.GetUserTransactions(userId, sort, order, limit, skip);
                
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion

        #region transaction by company
        [Route("GetCompanyReceivedTransactionsTotalAmountPerMonth/{companyId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCompanyReceivedTransactionsTotalAmountPerMonth(Guid companyId)
        {
            try
            {


                var response = _transactionService.GetCompanyReceivedTransactionsTotalAmountPerMonth(companyId);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }     
        
        [Route("GetCompanySendedTransactionsTotalAmountPerMonth/{companyId}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetCompanySendedTransactionsTotalAmountPerMonth(Guid companyId)
        {
            try
            {


                var response = _transactionService.GetCompanySendedTransactionsTotalAmountPerMonth(companyId);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion

        #region Get by wallet
        [Route("GetWalletReceivedTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetReceivedTransactions([FromQuery] PagedParameters pagedParameters, Guid walletId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);

                var response = _transactionService.GetWalletReceivedTransactions(walletId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        [Route("GetWalletSentTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletSentTransactions([FromQuery] PagedParameters pagedParameters, Guid walletId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);

                var response = _transactionService.GetWalletSentTransactions(walletId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        [Route("GetWalletTransactions")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetWalletTransactions([FromQuery] PagedParameters pagedParameters, Guid walletId)
        {
            try
            {
                var (_, sort, order, limit, skip) = HttpRequestUtils
                .GetPagination(Request.Query, SortType.Transaction);

                limit = pagedParameters.PageSize;
                skip = pagedParameters.PageSize * (pagedParameters.PageNumber - 1);

                var response = _transactionService.GetWalletTransactions(walletId, sort, order, limit, skip);

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };

                return StatusCode(500, response);
            }

        }
        #endregion
        
        #endregion

        #region Create 

        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] TransactionDTO transactionDTO,string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionService.Create(transactionDTO, new Guid(),pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("CreatePreTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreatePreTransaction([FromBody] TransactionDTO TransactionDTO)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionService.CreatePreTransaction(TransactionDTO);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CreatePreTransaction)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("CreateUniqueQrCodeTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateUniqueQrCodeTransaction(TransactionDTO transactionDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<UniqueQRCodeTransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionService.CreateUniqueQrCodeTransaction(transactionDTO,pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CreateUniqueQrCodeTransaction)}");
                ResponseAPI<List<UniqueQRCodeTransactionDTO>> response = new ResponseAPI<List<UniqueQRCodeTransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }


        [Route("CancelTransaction")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CancelTransaction(Guid transactionId,string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ResponseAPI<TransactionDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<TransactionDTO> response = _transactionService.CancelTransaction(transactionId,pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(CancelTransaction)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }
        [Route("CreateTransactionsForGroup")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateTransactionsForGroup([FromBody] List<TransactionUserDTO> transactionUserDTO, string pinCode)
        {
            try
            {
                if (!ModelState.IsValid)

                {
                    return BadRequest(new ResponseAPI<TransactionUserDTO>
                    {
                        StatusCode = System.Net.HttpStatusCode.BadRequest,
                        ExceptionMessage = "Model is invalid"
                    });
                }

                ResponseAPI<List<TransactionUserDTO>> response = _transactionService.CreateTransactionsForGroup(transactionUserDTO, pinCode);
                if (response.StatusCode == System.Net.HttpStatusCode.Created)
                {
                    return Ok(response);
                }
                else
                {
                    return BadRequest(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                ResponseAPI<List<TransactionDTO>> response = new ResponseAPI<List<TransactionDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }
        }
        #endregion

    }
}
