﻿using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Group;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.RoleManagement;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.Address;
using Platform.Dyno.AccessManagement.BusinessModel.Company;
using Platform.Dyno.AccessManagement.BusinessModel.Group;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DataModel.Address;
using Platform.Dyno.AccessManagement.DataModel.Company;
using Platform.Dyno.AccessManagement.DataModel.Employee;
using Platform.Dyno.AccessManagement.DataModel.Group;
using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.Address;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Employee;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Payment.DTO;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Cache.RedisCache;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Image;
using Platform.Dyno.Shared.Location;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.RefData;
using Platform.Dyno.Shared.Response;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.EmailTemplate;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Platform.Dyno.Shared.SharedClass.UnitWork;
using System.Net;


namespace Platform.Dyno.AccessManagement.Business.Service.Company;

public class CompanyService : ICompanyService
{
    private readonly IUnitOfWork<CompanyEntity> _companyRepository;
    private readonly IUnitOfWork<EmployeeEntity> _employeeRepository;
    private readonly IUnitOfWork<UserEntity> _userRepository;
    private readonly IUnitOfWork<PaymentDetailsEntity> _paymentDetailsRepository;
    private readonly IUnitOfWork<AddressEntity> _addressRepository;
    private readonly IHelper<WalletDTO> _walletHelper;
    private readonly IEmailingService _emailService;
    private readonly Configuration _configuration;
    private readonly ConfigurationDefaultId _configurationDefaultId;
    private readonly IUserService _userService;
    private readonly IGroupService _groupService;
    private readonly IRoleService _roleService;
    private readonly IMapper _mapper;
    private readonly IRedisCacheService _cache;
    private readonly string _companyCacheKey = RedisCacheKey.CompanyCacheKey;
    private readonly string _employeeCacheKey = RedisCacheKey.EmployeeCacheKey;
    private readonly string _userCacheKey = RedisCacheKey.UserCacheKey;
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly ExceptionMessages exceptionMessages = new ExceptionMessages("company");

    public CompanyService(IUnitOfWork<CompanyEntity> companyRepository,
        IRoleService roleService,
        IGroupService groupService,
        IUnitOfWork<EmployeeEntity> employeeRepository,
        IUnitOfWork<PaymentDetailsEntity> paymentDetailsRepository,
        IUnitOfWork<AddressEntity> addressRepository,
        IUnitOfWork<UserEntity> userRepository,
        IHelper<WalletDTO> walletHelper,
        Configuration configuration,
        ConfigurationDefaultId configurationDefaultId,
        IMapper mapper,
        IRedisCacheService cache,
        IUserService userService,
        IEmailingService emailingService,
        IWebHostEnvironment webHostEnvironment)
    {
        _companyRepository = companyRepository;
        _userRepository = userRepository;
        _roleService = roleService;
        _employeeRepository = employeeRepository;
        _paymentDetailsRepository = paymentDetailsRepository;
        _addressRepository = addressRepository;
        _walletHelper = walletHelper;
        _configuration = configuration;
        _userService = userService;
        _groupService = groupService;
        _mapper = mapper;
        _cache = cache;
        _emailService = emailingService;
        _webHostEnvironment = webHostEnvironment;
        _configurationDefaultId = configurationDefaultId;
    }

    #region Company Info

    #region Get
    public ResponseAPI<List<CompanyDTO>> GetAll(Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<CompanyDTO>> response = new ResponseAPI<List<CompanyDTO>>();
        try
        {

            if (userType == UserType.SuperAdmin || userType == null)
            {
                var companiesDTO = _cache.GetData<List<CompanyDTO>>(_companyCacheKey);
                IList<UserEntity> companyUsers = new List<UserEntity>();

                if (companiesDTO == null || companiesDTO.Count() == 0)
                {
                    var companiesEntity = _companyRepository.Repository.GetAll(includes: new List<string> { "Employees", "PaymentDetails", "Employees.User", "Addresses" }, orderBy: company => company.OrderByDescending(company => company.LastModificationTime));
                    var companiesBM = _mapper.Map<List<CompanyBM>>(companiesEntity);
                    companiesDTO = _mapper.Map<List<CompanyDTO>>(companiesBM);
                    foreach (var company in companiesDTO)
                    {
                        company.Picture = $"{_configuration.AWSS3URL}/Images/{company.Picture}";
                    }

                    _cache.SetData(_companyCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
                }

                response.StatusCode = companiesDTO == null || companiesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = companiesDTO?.OrderByDescending(c => c.LastModificationTime).ToList();
            }
            else
            {
                response.StatusCode = HttpStatusCode.Unauthorized;
                response.ObjectValue = null;
                response.ExceptionMessage = exceptionMessages.UnAuthorized;
            }
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.BadRequest;
            response.ExceptionMessage = exceptionMessages.ReadError + $"Exception Message : {ex.Message}";
        }
        return response;
    }
    public ResponseAPI<PagedList<CompanyDTO>> GetAll(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<CompanyDTO>> companyDTO = GetAll(companyId, userType);
        PagedList<CompanyDTO>? pagedList = null;
        if (companyDTO.ObjectValue != null)
        {
            pagedList = PagedList<CompanyDTO>.ToGenericPagedList(companyDTO.ObjectValue, pagedParameters);
        }
        return new ResponseAPI<PagedList<CompanyDTO>>
        {
            StatusCode = System.Net.HttpStatusCode.OK,
            ObjectValue = pagedList
        };

    }
    public ResponseAPI<PagedList<CompanyDTO>> GetAllActive(PagedParameters pagedParameters, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<CompanyDTO>> companyDTO = GetAll(companyId, userType);
        PagedList<CompanyDTO>? pagedList = null;
        if (companyDTO.ObjectValue != null)
        {
            List<CompanyDTO> activeCompany = companyDTO.ObjectValue.Where(company => company.Status == Status.Active).ToList();
            pagedList = PagedList<CompanyDTO>.ToGenericPagedList(activeCompany, pagedParameters);
        }
        return new ResponseAPI<PagedList<CompanyDTO>>
        {
            StatusCode = System.Net.HttpStatusCode.OK,
            ObjectValue = pagedList
        };

    }


    public ResponseAPI<PagedList<CompanyDTO>> GetAllActiveByUserType(PagedParameters pagedParameters, UserType filterType, Guid? companyId = null, UserType? userType = null)
    {
        ResponseAPI<List<CompanyDTO>> companyDTO = GetAll(companyId, userType);
        PagedList<CompanyDTO>? pagedList = null;
        if (companyDTO.ObjectValue != null)
        {
            List<CompanyDTO> activeCompany = companyDTO.ObjectValue.Where(company => company.Status == Status.Active &&
            (filterType == UserType.SuperAdmin ? company.Id == _configurationDefaultId.CompanyId :
            filterType == UserType.Company ? company.EntrepriseType == EnterpiseType.Enterprise : 
            filterType == UserType.ShopOwner ? company.EntrepriseType == EnterpiseType.Shop :          
            false)).ToList();
            pagedList = PagedList<CompanyDTO>.ToGenericPagedList(activeCompany, pagedParameters);
        }
        return new ResponseAPI<PagedList<CompanyDTO>>
        {
            StatusCode = System.Net.HttpStatusCode.OK,
            ObjectValue = pagedList
        };

    }
    public ResponseAPI<PagedList<CompanyDTO>> GetRecents(PagedParameters pagedParameters)
    {
        ResponseAPI<PagedList<CompanyDTO>> response = new ResponseAPI<PagedList<CompanyDTO>>();
        var companiesDTO = _cache.GetData<List<CompanyDTO>>(_companyCacheKey);
        try
        {
            if (companiesDTO == null || companiesDTO.Count() == 0)
            {
                companiesDTO = GetAll().ObjectValue.Where(company => company.Status == Status.Active)
                    .OrderByDescending(company => company.CreationTime).ToList();
                foreach (var company in companiesDTO)
                {
                    company.Picture = $"{_configuration.AWSS3URL}/Images/{company.Picture}";
                }
                response.StatusCode = companiesDTO == null || companiesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = PagedList<CompanyDTO>.ToGenericPagedList(companiesDTO, pagedParameters);
                _cache.SetData(_companyCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
            }
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<CompanyDTO> Get(Guid id)
    {
        ResponseAPI<CompanyDTO> response = new ResponseAPI<CompanyDTO>();
        try
        {
            var companyDTO = GetAll().ObjectValue?.Where(company => company.Id == id).FirstOrDefault();
            if (companyDTO != null && companyDTO.Employees != null && companyDTO.Employees.Count > 0)
            {
                foreach (var employee in companyDTO.Employees)
                {

                    var user = GetUserByEmployeeInCompany(employee.EmployeeId).ObjectValue;
                    if (user != null)
                        companyDTO?.Users?.Add(user);
                }
            }
            response.StatusCode = companyDTO == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = companyDTO;
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<List<CompanyDTO>> Get(Func<CompanyDTO, bool> expression)
    {
        var companiesDTO = GetAll().ObjectValue?.Where(expression).ToList();
        return new ResponseAPI<List<CompanyDTO>>
        {
            StatusCode = companiesDTO == null || companiesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK,
            ExceptionMessage = companiesDTO == null || companiesDTO.Count() == 0 ? $"companies Not Found !" : null,
            ObjectValue = companiesDTO
        };
    }

    public ResponseAPI<List<CompanyDTO>> GetAllShopByCategory(CategoryType category)
    {
        ResponseAPI<List<CompanyDTO>> shops = Get(c => c.EntrepriseType == EnterpiseType.Shop && c.CategoryType == category && c.Status == Status.Active);
        if(shops.ObjectValue != null && shops.ObjectValue.Count > 0)
        {
            return shops;
        }

        return new ResponseAPI<List<CompanyDTO>>
        {
            StatusCode = HttpStatusCode.NotFound,
            ExceptionMessage = "No shop in this category"
        };

    }

    public ResponseAPI<List<CompanyDTO>> GetNearestShopByCategory(CategoryType category, double longitude, double latitude, double distance)
    {
        ResponseAPI<List<CompanyDTO>> shops = Get(c => c.EntrepriseType == EnterpiseType.Shop 
        && c.CategoryType == category 
        && c.Status == Status.Active
        && c.Addresses != null && c.Addresses.Count > 0
        && GeoLocationService.CalculateDistance(latitude, longitude, (double)(c.Addresses?.FirstOrDefault().Latitude), (double)c.Addresses?.FirstOrDefault().Longitude) <= distance);
        if (shops.ObjectValue != null && shops.ObjectValue.Count > 0)
        {
            return shops;
        }

        return new ResponseAPI<List<CompanyDTO>>
        {
            StatusCode = HttpStatusCode.NotFound,
            ExceptionMessage = "there is no shop in this category near to you ."
        };
    }
    #endregion

    #region Create
    public ResponseAPI<CompanyDTO> Create(CompanyDTO companyDTO, Guid? creatorUserId = null, bool updateCache = true)
    {
        companyDTO.Id = Guid.NewGuid();

        #region Refdata
        RefDataService<CompanyDTO>.CreateRefData(companyDTO, creatorUserId);
        if (companyDTO.Picture != null)
        {
            string imagePath = ImageHelper.SaveImageAsync(companyDTO.Picture ?? string.Empty).Result;
            companyDTO.Picture = imagePath;
        }
        #endregion

        var paymentDetailsList = companyDTO.PaymentDetails;
        if (paymentDetailsList != null && paymentDetailsList.Count > 0)
        {
            foreach (var paymentDetails in paymentDetailsList)
            {
                paymentDetails.Code = paymentDetails.PaymentMethod.ToString() + "-**" + paymentDetails.RIB.Substring(paymentDetails.RIB.Length - 5);
                RefDataService<PaymentDetailsDTO>.CreateRefData(paymentDetails, creatorUserId);
            }

            companyDTO.PaymentDetails = paymentDetailsList;
        }

        CompanyBM companyBM = _mapper.Map<CompanyBM>(companyDTO);
        CompanyEntity companyEntity = _mapper.Map<CompanyEntity>(companyBM);
        _companyRepository.Repository.Insert(companyEntity);
        _companyRepository.Save();
        companyDTO.Id = companyEntity.Id;
        if (updateCache)
            _cache.RemoveData(_companyCacheKey);
        return new ResponseAPI<CompanyDTO>()
        {
            StatusCode = HttpStatusCode.Created,
            ObjectValue = companyDTO
        };
    }
    public ResponseAPI<CompanyDTO> Create(CompanyDTO companyDTO, Guid? creatorUserId = null, bool updateCache = true, Guid? companyId = null, UserType? userType = null)
    {
        if (userType == UserType.SuperAdmin)
        {
            List<CompanyDTO>? companys = Get(c =>
                (c.Email.ToUpper() == companyDTO.Email.ToUpper() ||
                c.RNECode == companyDTO.RNECode ||
                c.TaxCode == companyDTO.TaxCode))?.ObjectValue;

            if (companys != null || companys?.Count > 0)
            {
                if (companys.FirstOrDefault()?.Email.ToUpper() == companyDTO.Email.ToUpper())
                    return new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = "This Email is used by another company !"
                    };
                if (companys.FirstOrDefault()?.RNECode == companyDTO.RNECode)
                    return new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = "This RNE Code is used by another company !"
                    };
                if (companys.FirstOrDefault()?.TaxCode == companyDTO.TaxCode)
                    return new ResponseAPI<CompanyDTO>
                    {
                        StatusCode = HttpStatusCode.Unauthorized,
                        ExceptionMessage = "This Tax Code is used by another company !"
                    };
            }

            #region Refdata
            companyDTO.Id = Guid.NewGuid();
            RefDataService<CompanyDTO>.CreateRefData(companyDTO, creatorUserId);
            if (companyDTO.Picture != null)
            {
                string imagePath = ImageHelper.SaveImageAsync(companyDTO.Picture ?? string.Empty).Result;
                companyDTO.Picture = imagePath;
            }
            #endregion

            string Url = $"{_configuration.PaymentAddress}/Api/WalletBlockchain/Create";
            var assignedToType = companyDTO.EntrepriseType == 0 ? UserType.Company : UserType.ShopOwner;
            var walletDTO = new WalletDTO()
            {
                AssignedToId = companyDTO.Id,
                AssignedToType = assignedToType,
            };
            Dictionary<string, string> queryParams = new Dictionary<string, string>
            {
                 { "pinCode", companyDTO.Id.ToString() }
            };
            ResponseAPI<WalletDTO>? response = _walletHelper.Post(Url, walletDTO, queryParams);
            if (response != null && response.StatusCode == HttpStatusCode.Created)
            {
                if (response.ObjectValue?.Id != null)
                {
                    #region add payment details
                    var paymentDetailsList = companyDTO.PaymentDetails;
                    companyDTO.PaymentDetails = null;
                    #endregion
                    companyDTO.WalletId = response.ObjectValue.Id;
                    CompanyBM companyBM = _mapper.Map<CompanyBM>(companyDTO);
                    CompanyEntity companyEntity = _mapper.Map<CompanyEntity>(companyBM);
                    _companyRepository.Repository.Insert(companyEntity);
                    _companyRepository.Save();
                    #region add payment details
                    if (paymentDetailsList != null && paymentDetailsList.Count > 0)
                    {
                        foreach (var paymentDetails in paymentDetailsList)
                        {
                            paymentDetails.Code = paymentDetails.PaymentMethod.ToString() + "-**" + paymentDetails.RIB.Substring(paymentDetails.RIB.Length - 5);
                            paymentDetails.Id = Guid.NewGuid();
                            paymentDetails.CompanyId = companyDTO.Id;
                            RefDataService<PaymentDetailsDTO>.CreateRefData(paymentDetails, creatorUserId);
                            PaymentDetailsBM paymentDetailsBM = _mapper.Map<PaymentDetailsBM>(paymentDetails);
                            PaymentDetailsEntity paymentDetailsEntity = _mapper.Map<PaymentDetailsEntity>(paymentDetailsBM);
                            _paymentDetailsRepository.Repository.Insert(paymentDetailsEntity);
                            _paymentDetailsRepository.Save();
                        }
                    }
                    #endregion
                    companyDTO.Id = companyEntity.Id;
                    if (updateCache)
                        _cache.RemoveData(_companyCacheKey);
                    var emailBody = EmailTemplateEN.CongartCompanyEmailbody;
                    ResponseAPI<bool> responseAPI = _emailService.sendAddCompanyEmail(companyDTO.Email, emailBody);

                    if (!responseAPI.ObjectValue)
                    {
                        return new ResponseAPI<CompanyDTO>
                        {
                            StatusCode = responseAPI.StatusCode,
                            ExceptionMessage = "Company is created but email failed to be sent. Exception : " + responseAPI.ExceptionMessage
                        };
                    }
                    return new ResponseAPI<CompanyDTO>()
                    {
                        StatusCode = HttpStatusCode.Created,
                        ObjectValue = companyDTO
                    };
                }
                return new ResponseAPI<CompanyDTO>()
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    ExceptionMessage = exceptionMessages.CreateError
                };
            }

            return new ResponseAPI<CompanyDTO>()
            {
                StatusCode = HttpStatusCode.InternalServerError,
                ExceptionMessage = exceptionMessages.CreateError
            };

        }

        return new ResponseAPI<CompanyDTO>()
        {
            StatusCode = HttpStatusCode.Unauthorized,
            ExceptionMessage = exceptionMessages.UnAuthorized
        };


    }
    private string GenerateRandom4DigitString()
    {
        int randomNumber = new Random().Next(10000);
        string random4DigitString = randomNumber.ToString().PadLeft(4, '0');
        return random4DigitString;
    }
    #endregion

    #region Update
    public ResponseAPI<CompanyDTO> Update(CompanyDTO companyDTO, Guid? updateUserId = null, bool updateCache = true)
    {
        List<CompanyDTO>? companys = Get(c =>
        (c.Email.ToUpper() == companyDTO.Email.ToUpper() || c.RNECode == companyDTO.RNECode || c.TaxCode == companyDTO.TaxCode) &&
        c.Id != companyDTO.Id &&
        c.Status == Status.Active)?.ObjectValue;

        if (companys != null || companys?.Count > 0)
        {
            if (companys.FirstOrDefault()?.Email.ToUpper() == companyDTO.Email.ToUpper())
                return new ResponseAPI<CompanyDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "This Email is used by another company !"
                };
            if (companys.FirstOrDefault()?.TaxCode == companyDTO.TaxCode)
                return new ResponseAPI<CompanyDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "This Tax Code is used by another company !"
                };
            if (companys.FirstOrDefault()?.RNECode == companyDTO.RNECode)
                return new ResponseAPI<CompanyDTO>
                {
                    StatusCode = HttpStatusCode.Unauthorized,
                    ExceptionMessage = "This RNE Code is used by another company !"
                };
            
        }
        CompanyDTO? companyExist = Get(companyDTO.Id).ObjectValue;
        if (companyExist != null)
        {
            #region Refdata
            RefDataService<CompanyDTO>.UpdateRefData(companyDTO, updateUserId);

            if (companyDTO.Picture != null && companyDTO.Picture != companyExist.Picture)
            {
                string imagePath = ImageHelper.SaveImageAsync(companyDTO.Picture ?? string.Empty).Result;
                companyDTO.Picture = imagePath;

            }
            else
            {
                string url = $"{_configuration.AWSS3URL}/Images/";

                // Find the index where string2 ends in string1
                int? index = companyExist.Picture?.IndexOf(url);

                if (index != null && index != -1)
                {
                    // Get the substring starting from the index where string2 ends to the end of string1
                    companyDTO.Picture = companyExist.Picture?.Substring((int)(index + url.Length));
                };
            }
            #endregion

            #region delete old payment details
            var ExistingpaymentDetails = _paymentDetailsRepository.Repository.GetAll(x => x.CompanyId == companyDTO.Id);
            foreach (var paymentDetails in ExistingpaymentDetails)
            {
                _paymentDetailsRepository.Repository.Delete(paymentDetails.Id);
                _paymentDetailsRepository.Save();
            }
            #endregion

            #region add payment details
            var paymentDetailsList = companyDTO.PaymentDetails;
            if (paymentDetailsList != null && paymentDetailsList.Count > 0)
            {
                foreach (var paymentDetails in paymentDetailsList)
                {
                    paymentDetails.Code = paymentDetails.PaymentMethod.ToString() + "-**" + paymentDetails.RIB.Substring(paymentDetails.RIB.Length - 5);
                    paymentDetails.Id = Guid.NewGuid();
                    paymentDetails.CompanyId = companyDTO.Id;
                    RefDataService<PaymentDetailsDTO>.CreateRefData(paymentDetails, updateUserId);
                    PaymentDetailsBM paymentDetailsBM = _mapper.Map<PaymentDetailsBM>(paymentDetails);
                    PaymentDetailsEntity paymentDetailsEntity = _mapper.Map<PaymentDetailsEntity>(paymentDetailsBM);
                    _paymentDetailsRepository.Repository.Insert(paymentDetailsEntity);
                    _paymentDetailsRepository.Save();
                }
                companyDTO.PaymentDetails = null;
            }
            #endregion

            #region delete old addresses 
            var ExistingAddress = _addressRepository.Repository.GetAll(x => x.CompanyId == companyDTO.Id);
            foreach (var address in ExistingAddress)
            {
                _addressRepository.Repository.Delete(address.Id);
                _addressRepository.Save();
            }
            #region add addresses
            var addressList = companyDTO.Addresses;
            if (addressList != null && addressList.Count > 0)
            {
                foreach (var address in addressList)
                {
                    address.Id = Guid.NewGuid();
                    address.CompanyId = companyDTO.Id;
                    RefDataService<AddressDTO>.CreateRefData(address, updateUserId);
                    AddressBM addressBM = _mapper.Map<AddressBM>(address);
                    AddressEntity addressEntity = _mapper.Map<AddressEntity>(addressBM);
                    _addressRepository.Repository.Insert(addressEntity);
                    _addressRepository.Save();
                }
                companyDTO.Addresses = null;
            }
            #endregion

            #endregion

            CompanyBM companyBM = _mapper.Map<CompanyBM>(companyDTO);
            CompanyEntity companyEntity = _mapper.Map<CompanyEntity>(companyBM);
            _companyRepository.Repository.Update(companyEntity);
            _companyRepository.Save();

            if (updateCache)
                _cache.RemoveData(_companyCacheKey);
            return new ResponseAPI<CompanyDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = companyDTO
            };
        }
        return new ResponseAPI<CompanyDTO>
        {
            StatusCode = HttpStatusCode.BadRequest,
            ExceptionMessage = exceptionMessages.UpdateError
        };
    }
    #endregion

    #region Delete
    public ResponseAPI<CompanyDTO> Delete(Guid id, Guid? deletorUserId = null, bool updateCache = true)
    {
        CompanyEntity? companyEntity = _companyRepository.Repository.Get(user => user.Id == id);    
        
        if (companyEntity != null)
        {
            

            companyEntity.Status = Status.Deleted;
            companyEntity.DeleterUserId = deletorUserId;
            companyEntity.DeletionTime = DateTime.UtcNow;
            companyEntity.LastModificationTime = DateTime.UtcNow;
            companyEntity.LastModifierUserId = deletorUserId;
            _companyRepository.Repository.Update(companyEntity);
            _companyRepository.Save();


            List<UserEntity>? users = _userRepository.Repository.GetAll(user => user.CompanyId == companyEntity.Id 
            && user.UserType != UserType.Client 
            && user.Status == Status.Active).ToList();
            
            if (users != null && users.Count > 0)
            {
                foreach (var user in users)
                {
                    user.Status = Status.Deleted;
                    user.LastModifierUserId = deletorUserId;
                    user.LastModificationTime = DateTime.UtcNow;
                }
                _userRepository.Repository.UpdateRange(users);
                
                _userRepository.Save();
            }
            
            CompanyBM company = _mapper.Map<CompanyBM>(companyEntity);
            CompanyDTO companyDTO = _mapper.Map<CompanyDTO>(company);

            if (updateCache)
            {
                _cache.RemoveData(_companyCacheKey);
                _cache.RemoveData(_userCacheKey);
            }
            return new ResponseAPI<CompanyDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = companyDTO
            };
        }

        return new ResponseAPI<CompanyDTO>
        {
            StatusCode = HttpStatusCode.BadRequest,
            ExceptionMessage = exceptionMessages.DeleteError
        };
    }

    public ResponseAPI<CompanyDTO> Reactivate(Guid id, Guid? reactivateUserId = null, bool updateCache = true)
    {
        CompanyEntity? companyEntity = _companyRepository.Repository.Get(user => user.Id == id);

        if (companyEntity != null)
        {


            companyEntity.Status = Status.Active;
            companyEntity.LastModificationTime = DateTime.UtcNow;
            companyEntity.LastModifierUserId = reactivateUserId;
            _companyRepository.Repository.Update(companyEntity);
            _companyRepository.Save();


            List<UserEntity>? users = _userRepository.Repository.GetAll(user => user.CompanyId == companyEntity.Id
            && user.UserType != UserType.Client
            && user.Status == Status.Deleted).ToList();

            if (users != null && users.Count > 0)
            {
                foreach (var user in users)
                {
                    user.Status = Status.Active;
                    user.LastModifierUserId = reactivateUserId;
                    user.LastModificationTime = DateTime.UtcNow;
                }
                _userRepository.Repository.UpdateRange(users);

                _userRepository.Save();
            }

            CompanyBM company = _mapper.Map<CompanyBM>(companyEntity);
            CompanyDTO companyDTO = _mapper.Map<CompanyDTO>(company);

            if (updateCache)
            {
                _cache.RemoveData(_companyCacheKey);
                _cache.RemoveData(_userCacheKey);
            }
            return new ResponseAPI<CompanyDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = companyDTO
            };
        }

        return new ResponseAPI<CompanyDTO>
        {
            StatusCode = HttpStatusCode.BadRequest,
            ExceptionMessage = exceptionMessages.DeleteError
        };
    }

    #endregion

    #endregion

    #region Employee Info

    #region Get
    public ResponseAPI<List<UserDTO>> GetAllEmployees(Guid companyId)
    {
        ResponseAPI<List<UserDTO>> response = new ResponseAPI<List<UserDTO>>();
        var employeesDTO = _cache.GetData<List<UserDTO>>(_employeeCacheKey);
        try
        {
            if (employeesDTO == null || employeesDTO.Count() == 0)
            {
                var employeesEntity = Get(companyId).ObjectValue?.Users;
                var employeesBM = _mapper.Map<List<UserBM>>(employeesEntity);
                employeesDTO = _mapper.Map<List<UserDTO>>(employeesBM);
                response.StatusCode = employeesDTO == null || employeesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = employeesDTO;
                _cache.SetData(_employeeCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
            }
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<PagedList<UserDTO>> GetAllEmployees(PagedParameters pagedParameters, Guid companyId)
    {
        ResponseAPI<PagedList<UserDTO>> response = new ResponseAPI<PagedList<UserDTO>>();
        var employeesDTO = _cache.GetData<List<UserDTO>>(_employeeCacheKey);
        try
        {
            if (employeesDTO == null || employeesDTO.Count() == 0)
            {
                employeesDTO = GetAllEmployees(companyId).ObjectValue;
                response.StatusCode = employeesDTO == null || employeesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = PagedList<UserDTO>.ToGenericPagedList(employeesDTO, pagedParameters);
                _cache.SetData(_employeeCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
            }
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<PagedList<UserDTO>> GetAllActiveEmployees(PagedParameters pagedParameters, Guid companyId)
    {
        ResponseAPI<PagedList<UserDTO>> response = new ResponseAPI<PagedList<UserDTO>>();

        try
        {
            var employeesDTO = _cache.GetData<List<UserDTO>>(_employeeCacheKey);
            if (employeesDTO == null || employeesDTO.Count() == 0)
            {
                employeesDTO = GetAllEmployees(companyId).ObjectValue?.Where(user => user.Status == Status.Active).ToList();
            }

            response.StatusCode = employeesDTO == null || employeesDTO.Count() == 0 ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = PagedList<UserDTO>.ToGenericPagedList(employeesDTO, pagedParameters);
            _cache.SetData(_employeeCacheKey, response.ObjectValue, DateTimeOffset.UtcNow.AddDays(1));
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<CompanyDTO> GetCompanyByEmployee(Guid employeeId)
    {
        ResponseAPI<CompanyDTO> response = new ResponseAPI<CompanyDTO>();
        try
        {
            var getCompanyId = GetAll().ObjectValue?.Where(c => c.Employees.Select(e => e.UserId).ToList().Contains(employeeId)).FirstOrDefault();
            response.StatusCode = getCompanyId == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
            response.ObjectValue = getCompanyId;
        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    public ResponseAPI<UserDTO> GetUserByEmployeeInCompany(Guid employeeId)
    {
        ResponseAPI<UserDTO> response = new ResponseAPI<UserDTO>();
        try
        {
            EmployeeEntity? employee = _employeeRepository.Repository.Get(e => e.EmployeeId == employeeId);
            if (employee != null)
            {
                var user = _userService.Get(employee.UserId).ObjectValue;
                response.StatusCode = user == null ? HttpStatusCode.NotFound : HttpStatusCode.OK;
                response.ObjectValue = user;
            }
            else
            {
                response.StatusCode = HttpStatusCode.BadRequest;
                response.ExceptionMessage = exceptionMessages.ReadError;
            }

        }
        catch (Exception ex)
        {
            response.StatusCode = HttpStatusCode.InternalServerError;
            response.ExceptionMessage = ex.Message;
        }
        return response;
    }
    #endregion

    #region Create
    public ResponseAPI<EmployeeDTO> AssociateEmployeeToCompany(Guid companyId, Guid userId, Guid groupId, Status status, bool updateCache = true)
    {
        // Check if User and Company exist
        var users = _userService.Get(user => user.Id == userId
        && user.UserType == UserType.Client &&
        user.Status == Status.Active);
        if (users.ObjectValue == null || users.ObjectValue.Count <= 0)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "This client unfound"
            };
        }

        var user = users.ObjectValue[0];

        var company = Get(companyId);
        var group = _groupService.Get(groupId);
        //Mapping
        var companyBM = _mapper.Map<CompanyBM>(company.ObjectValue);
        var companyEntity = _mapper.Map<CompanyEntity>(companyBM);
        var userBM = _mapper.Map<UserBM>(user);
        var userEntity = _mapper.Map<UserEntity>(userBM);
        var groupBM = _mapper.Map<GroupBM>(group.ObjectValue);
        var groupEntity = _mapper.Map<GroupEntity>(groupBM);

        if (user == null || company == null || group == null)
        {
            // Handle the case where User or Company or Role does not exist
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "Employee or Company or Role or group not found."
            };
        }
        //Verify if the user exist as an employee with the same role in the same company
        var existingEmployee = _employeeRepository.Repository.GetAll(e => e.UserId == userId && e.CompanyId == companyId && e.GroupId == groupId);

        if (existingEmployee.Count > 0)
        {
            // Handle the case where the user is already associated with the specified role in the same company
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.Conflict,
                ExceptionMessage = "Employee is already associated with the specified role in the same company."
            };
        }
        // Create and associate EmployeeEntity
        EmployeeEntity employeeEntity = new EmployeeEntity
        {
            CompanyId = companyId,
            UserId = userId,
            GroupId = groupId,
            Status = status
        };

        _employeeRepository.Repository.Insert(employeeEntity);
        _employeeRepository.Save();
        if (updateCache)
        {
            _cache.RemoveData(_employeeCacheKey);
        }
        var employeeDTO = _mapper.Map<EmployeeDTO>(employeeEntity);
        return new ResponseAPI<EmployeeDTO>()
        {
            StatusCode = HttpStatusCode.Created,
            ObjectValue = employeeDTO
        };
    }
    #endregion

    #region Update
    public ResponseAPI<EmployeeDTO> UpdateEmployeeAssociation1(Guid companyId, Guid employeeId, Guid groupId, Status status, bool updateCache = true)
    {
        // Check if User and Company exist
        var company = Get(companyId);
        var group = _groupService.Get(groupId);
        //Mapping
        var companyBM = _mapper.Map<CompanyBM>(company.ObjectValue);
        var companyEntity = _mapper.Map<CompanyEntity>(companyBM);
        var groupBM = _mapper.Map<GroupBM>(group.ObjectValue);
        var groupEntity = _mapper.Map<GroupEntity>(groupBM);

        if (company == null || group == null)
        {
            // Handle the case where Company or Role does not exist
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "Company or Group not found."
            };
        }

        //Verify if the user exist as an employee with the same role in the same group in the same company
        var existingEmployee = _employeeRepository.Repository.GetAll(e => e.EmployeeId == employeeId && e.CompanyId == companyId && e.GroupId == groupId).FirstOrDefault();
        var existingEmployeeId = _employeeRepository.Repository.GetAll(e => e.EmployeeId == employeeId).FirstOrDefault();

        if (existingEmployee != null)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.Conflict,
                ExceptionMessage = "employee is already associated with the specified role in the same group and the same company."
            };
        }
        if (existingEmployeeId == null)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.Conflict,
                ExceptionMessage = "employee not found."
            };
        }

        // Update EmployeeEntity

        existingEmployeeId.CompanyId = companyId;
        existingEmployeeId.GroupId = groupId;
        existingEmployeeId.Status = status;

        _employeeRepository.Repository.Update(existingEmployeeId);
        _employeeRepository.Save();
        if (updateCache)
        {
            _cache.RemoveData(_employeeCacheKey);
        }
        //Mapping
        var employeeDTO = _mapper.Map<EmployeeDTO>(existingEmployeeId);

        return new ResponseAPI<EmployeeDTO>()
        {
            StatusCode = HttpStatusCode.OK,
            ObjectValue = employeeDTO
        };
    }

    public ResponseAPI<EmployeeDTO> UpdateEmployeeAssociation(Guid companyId, Guid employeeId, Guid userId, Guid groupId, Status status, bool updateCache = true)
    {
        var company = Get(companyId);
        var group = _groupService.Get(groupId);
        //Mapping
        var companyBM = _mapper.Map<CompanyBM>(company.ObjectValue);
        var companyEntity = _mapper.Map<CompanyEntity>(companyBM);
        var groupBM = _mapper.Map<GroupBM>(group.ObjectValue);
        var groupEntity = _mapper.Map<GroupEntity>(groupBM);


        if (company == null || group == null)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "Company or Group not found."
            };
        }
        var existingEmployee = _employeeRepository.Repository.GetAll(e => e.EmployeeId == employeeId).AsQueryable()
    .Include(e => e.Company)
    .Include(e => e.Group)
    .FirstOrDefault();

        if (existingEmployee == null)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.NotFound,
                ExceptionMessage = "Employee not found."
            };
        }
        //Verify if the user exist as an employee with the same group in the same company
        var existingEmployeeDuplicate = _employeeRepository.Repository.GetAll(e => e.UserId == userId && e.CompanyId == companyId && e.GroupId == groupId).FirstOrDefault();
        if (existingEmployeeDuplicate != null)
        {
            return new ResponseAPI<EmployeeDTO>()
            {
                StatusCode = HttpStatusCode.Conflict,
                ExceptionMessage = "employee is already associated with the same group and the same company."
            };
        }

        // Update EmployeeEntity
        existingEmployee.CompanyId = companyId;
        existingEmployee.GroupId = groupId;
        existingEmployee.Status = status;

        // Detach the entity before making changes
        _employeeRepository.Repository.Detach(existingEmployee);

        // Update the detached entity
        _employeeRepository.Repository.Update(existingEmployee);
        _employeeRepository.Save();
        if (updateCache)
        {
            _cache.RemoveData(_employeeCacheKey);
        }
        // Mapping
        var employeeDTO = _mapper.Map<EmployeeDTO>(existingEmployee);

        return new ResponseAPI<EmployeeDTO>()
        {
            StatusCode = HttpStatusCode.OK,
            ObjectValue = employeeDTO
        };
    }
    #endregion

    #region Delete
    public ResponseAPI<EmployeeDTO> DeleteEmployee(Guid employeeId, Guid companyId, Guid? deletorUserId = null, bool updateCache = true)
    {
        try
        {
            // Retrieve the employee to be deleted
            var employeeEntity = _employeeRepository.Repository.Get(e => e.EmployeeId == employeeId);

            if (employeeEntity == null || employeeEntity.CompanyId != companyId)
            {
                return new ResponseAPI<EmployeeDTO>
                {
                    StatusCode = HttpStatusCode.NotFound,
                    ExceptionMessage = "Employee not found in the specified company."
                };
            }
            _employeeRepository.Repository.Delete(employeeEntity.EmployeeId);
            _employeeRepository.Save();
            if (updateCache)
            {
                _cache.RemoveData(_employeeCacheKey);
            }

            // Map and return the deleted employee
            var deletedEmployeeDTO = _mapper.Map<EmployeeDTO>(employeeEntity);
            return new ResponseAPI<EmployeeDTO>
            {
                StatusCode = HttpStatusCode.OK,
                ObjectValue = deletedEmployeeDTO
            };
        }
        catch (Exception ex)
        {
            // Log the exception if needed
            return new ResponseAPI<EmployeeDTO>
            {
                StatusCode = HttpStatusCode.InternalServerError,
                ExceptionMessage = ex.InnerException?.Message
            };
        }
    }
    #endregion

    #endregion
}
