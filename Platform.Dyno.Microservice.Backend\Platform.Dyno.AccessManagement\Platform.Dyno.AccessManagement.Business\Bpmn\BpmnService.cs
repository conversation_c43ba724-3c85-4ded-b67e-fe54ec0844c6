﻿using Camunda.Api.Client;
using Camunda.Api.Client.ProcessInstance;
using Camunda.Api.Client.Deployment;
using Camunda.Api.Client.ProcessDefinition;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Refit;
using System.Reflection;
using Platform.Dyno.AccessManagement.DTO.Process;
using VariableValue = Platform.Dyno.AccessManagement.DTO.Process.VariableValue;

namespace Platform.Dyno.AccessManagement.Business.Bpmn
{
    public interface IDeploymentService
    {
        [Multipart]
        [Post("/deployment/create")]
        Task CreateDeployment(
            string deploymentName,
            bool enableDuplicateFiltering,
            bool deployChangedOnly,
            [AliasAs("deployment-source")] string deploymentSource,
            [AliasAs("deployment-activation-time")] DateTime? deploymentActivationTime,
            [AttachmentName("data")] Stream file,
            string filename);
    }

    public interface IProcessDefinitionService
    {
        [Post("/process-definition/{processDefinitionKey}/start")]
        Task<ProcessInstanceResult> StartProcessInstance(
            string processDefinitionKey,
            [Body] StartProcessInstanceRequest request);
    }
    public class BpmnService
    {
        private readonly CamundaClient camunda;

        private readonly HttpClient _httpClient;
        private readonly IProcessDefinitionService _processDefinitionService;
        private readonly IDeploymentService _deploymentService;


        public BpmnService(string camundaRestApiUri)
        {
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri(camundaRestApiUri)
            };

            // Configuration Refit explicite
            _deploymentService = RestService.For<IDeploymentService>(_httpClient);
            _processDefinitionService = RestService.For<IProcessDefinitionService>(_httpClient);
        }

        public async Task DeployProcessDefinition()
        {
            var assembly = GetType().Assembly;

            await DeployBpmnFile(assembly, "SalesOrderWorkflow.bpmn");
            await DeployBpmnFile(assembly, "SalesInvoiceWorkflow.bpmn");
        }

        private async Task DeployBpmnFile(Assembly assembly, string filename)
        {
            var resourceName = $"Platform.Dyno.AccessManagement.Business.Bpmn.{filename}";

            await using var stream = assembly.GetManifestResourceStream(resourceName);

            await _deploymentService.CreateDeployment(
                deploymentName: $"{filename} Deployment",
                enableDuplicateFiltering: true,
                deployChangedOnly: true,
                deploymentSource: null,
                deploymentActivationTime: null,
                file: stream,
                filename: filename);
        }

        //////
        /*
        public async Task DeployProcessDefinition()
         {
             var name = this.GetType().Assembly.GetManifestResourceNames();
             var bpmnResourceStream = this.GetType()
                 .Assembly
                 .GetManifestResourceStream("Platform.Dyno.AccessManagement.Business.Bpmn.SalesOrderWorkflow.bpmn");
             var bpmnResourceStreamSI = this.GetType()
                 .Assembly
                 .GetManifestResourceStream("Platform.Dyno.AccessManagement.Business.Bpmn.SalesInvoiceWorkflow.bpmn");

             try
             {
                 await camunda.Deployments.Create(
                     "SalesOrderWorkflow Deployment",
                     true,
                     true,
                     null,
                     null,
                     new ResourceDataContent(bpmnResourceStream, "SalesOrderWorkflow.bpmn"));
                 await camunda.Deployments.Create(
                    "SalesInvoiceWorkflow Deployment",
                    true,
                    true,
                    null,
                    null,
                    new ResourceDataContent(bpmnResourceStreamSI, "SalesInvoiceWorkflow.bpmn"));
             }
             catch (Exception e)
             {

                 throw new ApplicationException("Failed to deploy process definition", e);
             }
         }*/

        public async Task<string> StartProcessForSalesOrder(SalesOrderDTO salesOrder)
        {
            var request = new StartProcessInstanceRequest
            {
                BusinessKey = Guid.NewGuid().ToString(),
                Variables = new Dictionary<string, VariableValue>
                {
                    ["SalesOrder"] = VariableValue.FromObject(salesOrder)
                }
            };

            var result = await _processDefinitionService.StartProcessInstance(
                "Process_SalesOrder_Management",
                request);

            return result.Id;
        }

        /*
        public async Task<string> StartProcessForSalesOrder(SalesOrderDTO salesOrder)
        {
            var businessKey = Guid.NewGuid().ToString();
            var processParams = new StartProcessInstance()
                .SetVariable("SalesOrder", VariableValue.FromObject(salesOrder));

            processParams.BusinessKey = businessKey;

            var processStartResult = await
                camunda.ProcessDefinitions.ByKey("Process_SalesOrder_Management").StartProcessInstance(processParams);

            return processStartResult.Id;
        }*/
        public async Task<string> StartProcessForSalesInvoice(SalesInvoiceDTO salesInvoice)
        {
            var businessKey = Guid.NewGuid().ToString();
            var processParams = new StartProcessInstance()
                .SetVariable("SalesInvoice", VariableValue.FromObject(salesInvoice));

            processParams.BusinessKey = businessKey;

            var processStartResult = await
                camunda.ProcessDefinitions.ByKey("Process_SalesInvoice_Management").StartProcessInstance(processParams);

            return processStartResult.Id;
        }

        public async Task CleanupProcessInstances(string processName)
        {
            var instances = await camunda.ProcessInstances
                .Query(new ProcessInstanceQuery
                {
                    ProcessDefinitionKey = processName
                })
                .List();

            if (instances.Count > 0)
            {
                await camunda.ProcessInstances.Delete(new DeleteProcessInstances
                {
                    ProcessInstanceIds = instances.Select(i => i.Id).ToList()
                });
            }
        }
    }
}
