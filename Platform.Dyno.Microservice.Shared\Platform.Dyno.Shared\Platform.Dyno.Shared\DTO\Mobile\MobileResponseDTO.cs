using System.Net;

namespace Platform.Dyno.Shared.DTO.Mobile
{
    /// <summary>
    /// Réponse optimisée pour mobile avec pagination
    /// </summary>
    public class MobilePagedResponseDTO<T>
    {
        public HttpStatusCode StatusCode { get; set; }
        public string? Message { get; set; }
        public List<T>? Data { get; set; }
        public MobilePaginationDTO? Pagination { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Réponse simple optimisée pour mobile
    /// </summary>
    public class MobileResponseDTO<T>
    {
        public HttpStatusCode StatusCode { get; set; }
        public string? Message { get; set; }
        public T? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Informations de pagination optimisées pour mobile
    /// </summary>
    public class MobilePaginationDTO
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasNext { get; set; }
        public bool HasPrevious { get; set; }
    }

    /// <summary>
    /// Profil utilisateur optimisé pour mobile
    /// </summary>
    public class MobileUserProfileDTO
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string? Picture { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        public bool IsVerified { get; set; }
        public DateTime LastLoginDate { get; set; }
    }

    /// <summary>
    /// Réponse d'authentification optimisée pour mobile
    /// </summary>
    public class MobileAuthResponseDTO
    {
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public DateTime ExpiredDate { get; set; }
        public MobileUserProfileDTO? UserProfile { get; set; }
        public List<string>? Permissions { get; set; }
    }

    /// <summary>
    /// Transaction optimisée pour mobile
    /// </summary>
    public class MobileTransactionDTO
    {
        public Guid Id { get; set; }
        public double Amount { get; set; }
        public string Currency { get; set; } = "TND";
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; }
        public string? MerchantName { get; set; }
        public string? Reference { get; set; }
    }

    /// <summary>
    /// Wallet optimisé pour mobile
    /// </summary>
    public class MobileWalletDTO
    {
        public Guid Id { get; set; }
        public double Balance { get; set; }
        public string Currency { get; set; } = "TND";
        public string Type { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Notification optimisée pour mobile
    /// </summary>
    public class MobileNotificationDTO
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? ActionUrl { get; set; }
    }

    /// <summary>
    /// Compagnie optimisée pour mobile
    /// </summary>
    public class MobileCompanyDTO
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Logo { get; set; }
        public string? Description { get; set; }
        public string Category { get; set; } = string.Empty;
        public double? CashbackRate { get; set; }
        public bool IsActive { get; set; }
        public string? Address { get; set; }
        public string? PhoneNumber { get; set; }
    }

    /// <summary>
    /// Erreur optimisée pour mobile
    /// </summary>
    public class MobileErrorDTO
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Details { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
