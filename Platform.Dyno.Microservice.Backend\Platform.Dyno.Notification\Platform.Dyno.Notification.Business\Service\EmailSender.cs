﻿using Platform.Dyno.Notification.Business.IService;
using Platform.Dyno.Notification.DTO.Emailing;
using MailKit.Net.Smtp;
using MimeKit;
using Platform.Dyno.Shared;

using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Utilities;

namespace Platform.Dyno.Notification.Business.Service
{
    public class EmailSender : IEmailSender
    {
        private readonly EmailConfiguration _emailConfig;
        private readonly ILogger<EmailSender> _logger;
        private readonly Platform.Dyno.Shared.Configuration _configuration;
        public EmailSender(EmailConfiguration emailConfig,
            ILogger<EmailSender> logger,
            Configuration configuration)
        {
            _emailConfig = emailConfig;
            _logger = logger;
            _configuration = configuration;
        }
        public void SendEmail(MessageEmailDTO messageEmail)
        {
            Message message = new(messageEmail);
            message.IsBodyHtml = messageEmail.IsBodyHtml;
            var emailMessage = CreateEmailMessage(message);
            Send(emailMessage);
        }

        private MimeMessage CreateEmailMessage(Message message)
        {
            var emailMessage = new MimeMessage();
            emailMessage.From.Add(new MailboxAddress("email", "<EMAIL>"));
            emailMessage.To.AddRange(message.To);
            emailMessage.Subject = message.Subject;
            emailMessage.Body = message.IsBodyHtml
             ? new TextPart(MimeKit.Text.TextFormat.Html) { Text = message.Content }
             : new TextPart(MimeKit.Text.TextFormat.Text) { Text = message.Content };

            return emailMessage;
        }
        private void Send(MimeMessage mailMessage)
        {
            using (var client = new SmtpClient())
            {
                try
                {
                    client.Connect(_configuration.SmtpServer, int.Parse(_configuration.EmailPort), true);
                    //client.Connect("ssl0.ovh.net", 465, true);
                    client.AuthenticationMechanisms.Remove("XOAUTH2");
                    client.Authenticate(_configuration.Email, _configuration.EmailPassword);
                   // client.Authenticate("<EMAIL>", "Dyno123;");
                    client.Send(mailMessage);
                }
                catch(Exception ex)
                {
                    _logger.LogError("Error in sending email : " + ex);
                    throw;
                }
                finally
                {
                    client.Disconnect(true);
                    client.Dispose();
                }
            }
        }
    }
}
