﻿using Camunda.Worker;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SI_Notification_PDF_Failed")]
    public class SendNotifSIPDFFailed : IExternalTaskHandler
    {
        private readonly ISignalRNotificationService _signalRNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ConfigurationDefaultId _configuration;
        public SendNotifSIPDFFailed(ISignalRNotificationService signalRNotificationService,
            ICompanyService companyService,
            ConfigurationDefaultId configuration)
        {
            _signalRNotificationService = signalRNotificationService;
            _companyService = companyService;
            _configuration = configuration;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());
            var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
            SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);

            if (salesInvoiceDTO != null)
            {
                CompanyDTO? company = _companyService.Get(salesInvoiceDTO.CompanyId).ObjectValue;
                if (company != null)
                {
                    SignalRNotificationDTO notificationDTO = new SignalRNotificationDTO
                    {
                        Title = "PDF Failed",
                        Message = "PDF Failed To generate in Sales invoice !",
                        SendToId = new List<CompanyDTO> { company }

                    };
                    _signalRNotificationService.Create(notificationDTO, companyId: salesInvoiceDTO.CompanyId);
                }

            }

            var completeResult = new CompleteResult();
            return completeResult;
        }
    }
}
