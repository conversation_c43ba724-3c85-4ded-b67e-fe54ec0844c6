﻿using Platform.Dyno.AccessManagement.DTO.Reporting;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Reporting
{
    public interface IInvoiceService : IGenericService<DocumentsDTO>
    {
        Task<ResponseAPI<DocumentsDTO>> CreatePDF(string code,
            ProductType productType,
            double dynoAmount,
            double netAmount,
            double VATAmount,
            double totalAmount,
            PaymentMethod paymentMethod,
            Guid companyCreatorId,
            Guid companyReceiverId,
            DocumentType documentType);
    }
}
