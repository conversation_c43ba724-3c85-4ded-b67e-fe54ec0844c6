﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.AppConfig;

[Route("Api/[controller]")]
public class AppConfigController : ControllerBase
{
        private readonly ILogger<AppConfigController> _logger;
        private readonly IWebHostEnvironment _env;

        public AppConfigController(ILogger<AppConfigController> logger, IWebHostEnvironment env)
        {
            _logger = logger;
            _env = env;
        }
        [AllowAnonymous]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetConfig()
        {
            try
            {
                string filePath = "./config.json";
                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogError($"Config file not found at path: {filePath}");
                    return NotFound(); // Return a 404 response if the file does not exist
                }

                string jsonData = System.IO.File.ReadAllText(filePath);
                return Content(jsonData, "application/json"); // Return the JSON content directly

            }
        catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetConfig)}");
                return StatusCode(500, "Internal server Error. Please try later");
            }

        }


}
