﻿using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class RefundTransactionEntity
    {
        public Guid Id { get; set; }
        public Guid SenderWalletId { get; set; }
        public Guid ReceiverWalletId { get; set; }
        public Guid? QrCodeId { get; set; }
        public DateTime TransactionDate { get; set; }
        public double Amount { get; set; }
        public TransactionStatus Status { get; set; }
        public RefundStatus RefundStatus { get; set; }
    }
}
