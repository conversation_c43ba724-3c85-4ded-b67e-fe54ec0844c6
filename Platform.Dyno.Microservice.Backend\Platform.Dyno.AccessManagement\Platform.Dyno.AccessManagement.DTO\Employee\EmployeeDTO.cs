﻿using Platform.Dyno.AccessManagement.DataModel.User;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.DTO.Employee;

public class EmployeeDTO
{
    public Guid EmployeeId { get; set; }
    public Guid CompanyId { get; set; }
    public Guid UserId { get; set; }
    public Guid GroupId { get; set; }
    public Status Status { get; set; }
    public UserDTO? User { get; set; }
}
