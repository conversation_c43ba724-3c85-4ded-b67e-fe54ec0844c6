﻿using Platform.Dyno.AccessManagement.DTO.Authentification.AuthRequest;
using Platform.Dyno.AccessManagement.DTO.Authentification.AuthResponse;
using Platform.Dyno.AccessManagement.DTO.User;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.UserManagement
{
    public interface IAuthService<LoginDto, RegisterDto>
    {
        public ResponseAPI<AuthResponseDTO> Login(LoginDto loginDto, LanguageType language);
        Task <ResponseAPI<AuthResponseDTO>> Register(RegisterDto registerDto, Guid? createdUserId = null, Guid? companyId=null);
        public ResponseAPI<AuthResponseDTO> LogOut(Guid userId, string accessToken);
        public ResponseAPI<AuthResponseDTO> GetRefreshToken(UserTokenDTO token);
        public Task<ResponseAPI<AuthResponseDTO>> ForgetPassword(string element, string? countryCode = null);

        public ResponseAPI<AuthResponseDTO> UpdatePassword(UpdatePasswordDTO passwordDTO);
    }
}
