﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Platform.Dyno.AccessManagement.Business.IService.CashBack;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.Business.IService.UserManagement;
using Platform.Dyno.AccessManagement.BusinessModel.User;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using Platform.Dyno.AccessManagement.DTO.Company;
using Platform.Dyno.AccessManagement.DTO.Group;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.AccessManagement.WebAPI.Controllers.CashBack;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.Pagination;
using Platform.Dyno.Shared.ResponseAPI;
using System.Net;
using System.Security.Claims;

namespace Platform.Dyno.AccessManagement.WebAPI.Controllers.CashBack
{
    [Route("Api/[controller]")]
    [ApiController]
    public class CashBackController : Controller
    {
        private readonly ICashBackService _cashBackService;
        private readonly IUserService _userService;
        private readonly IEmailingService _emailingService;
        private readonly ICompanyService  _companyService;
        private readonly ILogger<CashBackController> _logger;
        private readonly ISignalRNotificationService _signalRNotification;
        private readonly ConfigurationDefaultId _configuration;
        private readonly ISortingUtility _sortingUtility;
        public CashBackController(ICashBackService cashBackService,
            IUserService userService,
            IEmailingService emailingService,
            ICompanyService companyService,
            ILogger<CashBackController> logger,
            ISignalRNotificationService signalRNotification,
            ConfigurationDefaultId configuration,
            ISortingUtility sortingUtility)
        {
            _cashBackService = cashBackService;
            _userService = userService;
            _companyService = companyService;
            _emailingService = emailingService;
            _logger = logger;
            _signalRNotification = signalRNotification;
            _configuration = configuration;
            _sortingUtility = sortingUtility;
        }

        #region Get

        [Route("GetAll")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAll()
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<CashBackDTO>> cashBacks = _cashBackService.GetAll(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(cashBacks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAll)}");
                ResponseAPI<List<CashBackDTO>> response = new ResponseAPI<List<CashBackDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPerPeriod")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPerPeriod(DateTime startDate, DateTime endDate)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }

                ResponseAPI<List<int>> cashBacks = _cashBackService.GetByPeriod(companyId, isSuperAdminResponse.ObjectValue);
                return Ok(cashBacks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPerPeriod)}");
                ResponseAPI<List<CashBackDTO>> response = new ResponseAPI<List<CashBackDTO>>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = "An error occurred : " + ex.InnerException?.Message
                };
                return StatusCode(500, response);
            }

        }

        [Route("GetAllPaged")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllPaged([FromQuery] PagedParameters pagedParameters)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<PagedList<CashBackDTO>> cashBacksDTO = _cashBackService.GetAll(companyId, isSuperAdminResponse.ObjectValue, pagedParameters);
                if(cashBacksDTO.ObjectValue != null)
                {
                   

                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = cashBacksDTO.ObjectValue.TotalCount,
                        PageSize = cashBacksDTO.ObjectValue.PageSize,
                        CurrentPage = cashBacksDTO.ObjectValue.CurrentPage,
                        TotalPages = cashBacksDTO.ObjectValue.TotalPages,
                        HasNext = cashBacksDTO.ObjectValue.HasNext,
                        HasPrevious = cashBacksDTO.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(cashBacksDTO);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllPaged)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("GetAllByStatus")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult GetAllByStatus([FromQuery] PagedParameters pagedParameters, CashBackStatus cashBackStatus)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<PagedList<CashBackDTO>> cashBacksDTOResponse = _cashBackService.GetAllByStatus(companyId, isSuperAdminResponse.ObjectValue, cashBackStatus, pagedParameters);
                if(cashBacksDTOResponse.ObjectValue != null)
                {
                    cashBacksDTOResponse.ObjectValue= _sortingUtility.SortData(pagedParameters, cashBacksDTOResponse);
                    PaginationData metadata = new PaginationData
                    {
                        TotalCount = cashBacksDTOResponse.ObjectValue.TotalCount,
                        PageSize = cashBacksDTOResponse.ObjectValue.PageSize,
                        CurrentPage = cashBacksDTOResponse.ObjectValue.CurrentPage,
                        TotalPages = cashBacksDTOResponse.ObjectValue.TotalPages,
                        HasNext = cashBacksDTOResponse.ObjectValue.HasNext,
                        HasPrevious = cashBacksDTOResponse.ObjectValue.HasPrevious
                    };

                    Response.Headers.Append("X-Pagination", JsonConvert.SerializeObject(metadata));
                }
                
                return Ok(cashBacksDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(GetAllByStatus)}");
                return StatusCode(500, "An error occurred. " + ex.InnerException?.Message);

            }

        }

        [Route("Get/{id}")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Get(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<CashBackDTO> cashBackDTOResponse = _cashBackService.Get(companyId, isSuperAdminResponse.ObjectValue, id);
                if (cashBackDTOResponse.ObjectValue == null)
                {
                    return BadRequest(cashBackDTOResponse);
                }
                return Ok(cashBackDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Get)}");
                return BadRequest(new ResponseAPI<CashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }

        }
        #endregion

        #region Create
        [Route("Create")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Create([FromBody] CashBackDTO cashBackDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                ResponseAPI<CashBackDTO> response = _cashBackService.Create(companyId, cashBackDTO, userId);
                if (response.ObjectValue == null)
                {
                    return BadRequest(Response);
                }

                //Send Notification To Super Admin
                SignalRNotificationDTO notif = new SignalRNotificationDTO
                {
                    Message = NotifMessage.CashBackMessage,
                    Title = "CashBack",
                    SendToId = new List<CompanyDTO>
                    {
                        new CompanyDTO
                        {
                            Id = _configuration.CompanyId
                        }
                    }
                };
                ResponseAPI<SignalRNotificationDTO> sendNotification = _signalRNotification.Create(notif, userId, true, companyId, UserType.ShopOwner);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Create)}");
                return BadRequest(new ResponseAPI<CashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion


        #region Update
        [Route("Update")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Update([FromBody] CashBackDTO cashBackDTO)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<CashBackDTO> cashBackDTOResponse = _cashBackService.Update(companyId, isSuperAdminResponse.ObjectValue, cashBackDTO, userId);
                if (cashBackDTOResponse.ObjectValue == null)
                {
                    return BadRequest(cashBackDTOResponse);
                }
                return Ok(cashBackDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<CashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }
        [Route("ValidateCashBackRequest")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult ValidateCashBackRequest( Guid cashBackId)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<CashBackDTO> cashBackDTOResponse = _cashBackService.ValidateCashBackRequest(companyId, isSuperAdminResponse.ObjectValue, cashBackId, userId);
                if (cashBackDTOResponse.ObjectValue == null)
                {
                    return BadRequest(cashBackDTOResponse);
                }
                var companyExist = _companyService.Get(cashBackDTOResponse.ObjectValue.CompanyId);
                if (companyExist.ObjectValue == null)
                {
                    return BadRequest(companyExist);
                }

                ResponseAPI<bool> responseAPI = _emailingService.sendCashBackEmail(companyExist.ObjectValue, cashBackDTOResponse.ObjectValue);
                //Send Notification To shop owner
                SignalRNotificationDTO notif = new SignalRNotificationDTO
                {
                    Message = NotifMessage.CashBackValidatedMessage,
                    Title = "CashBack validated",
                    SendToId = new List<CompanyDTO>
                    {
                        new CompanyDTO
                        {
                            Id = cashBackDTOResponse.ObjectValue.CompanyId
                        }
                    }
                };
                ResponseAPI<SignalRNotificationDTO> sendNotification = _signalRNotification.CreateValidCashbackNotif(notif, userId, true);
                return Ok(cashBackDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Update)}");
                return BadRequest(new ResponseAPI<CashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion

        #region Delete
        [Route("Delete/{id}")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public IActionResult Delete(Guid id)
        {
            try
            {
                Guid companyId = Guid.Parse(HttpContext.User.FindFirstValue("Company"));
                Guid userId = Guid.Parse(HttpContext.User.FindFirstValue("Id"));
                if (companyId == Guid.Empty || userId == Guid.Empty)
                {
                    var Response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(Response);
                }
                var isSuperAdminResponse = _userService.CheckUserIsSuperAdmin(userId);
                if (isSuperAdminResponse.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var response = new ResponseAPI<CashBackDTO>()
                    {
                        StatusCode = System.Net.HttpStatusCode.NotFound,
                        ExceptionMessage = "user not authorized"
                    };
                    return BadRequest(response);
                }
                ResponseAPI<CashBackDTO> cashBackDTOResponse = _cashBackService.Delete(companyId, isSuperAdminResponse.ObjectValue, id, userId);
                return Ok(cashBackDTOResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Something went wrong in the {nameof(Delete)}");
                return BadRequest(new ResponseAPI<CashBackDTO>
                {
                    StatusCode = System.Net.HttpStatusCode.InternalServerError,
                    ExceptionMessage = ex.InnerException?.Message
                });
            }
        }

        #endregion
    }
}
