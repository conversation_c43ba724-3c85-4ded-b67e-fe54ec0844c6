﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;
using Org.BouncyCastle.Bcpg.OpenPgp;
using Platform.Dyno.AccessManagement.Business.IService.Notification;
using Platform.Dyno.AccessManagement.DTO.User.Password;
using Platform.Dyno.Notification.DTO.Emailing;
using Platform.Dyno.Shared;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.MultiLanguageConfig;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass.HttpHelper;
using Microsoft.Net.Http.Headers;
using System.Net.Mail;
using System.Xml.Linq;
using System.Net;
using System.Web;
using Platform.Dyno.AccessManagement.Business.IService.Company;
using Platform.Dyno.Shared.SharedClass.EmailTemplate;
using Platform.Dyno.AccessManagement.DTO.CashBack;
using System.ComponentModel.DataAnnotations;
using Platform.Dyno.AccessManagement.DTO.Company;
using Newtonsoft.Json.Linq;

namespace Platform.Dyno.AccessManagement.Business.Service.Notification
{
    public class EmailingService : IEmailingService
    {
        private readonly IHelper<MessageEmailDTO> _helper;
        private readonly Configuration _configuration;
        private readonly EmailContentEN _emailContentEN;
        private readonly EmailContentFR _emailContentFR;

        public EmailingService(IHelper<MessageEmailDTO> helper,
            Configuration configuration,
            EmailContentEN emailContentEN,
            EmailContentFR emailContentFR)
        {
            _helper = helper;
            _configuration = configuration;
            _emailContentEN = emailContentEN;
            _emailContentFR = emailContentFR;
        }

        public ResponseAPI<bool> SendMacAddressCodeEmail(string email, int code, LanguageType language)
        {
            // 🔧 TEMPORAIRE POUR TESTS : Désactiver l'envoi d'email
            // TODO: Réactiver quand le service de notification sera opérationnel
            try
            {
                MessageEmailDTO message = new MessageEmailDTO
                {
                    To = new List<string> { email },
                    Subject = "Dyno : Confirm Device",
                    Content = language == LanguageType.Fr ? _emailContentFR.NewDeviceEmail + $"\n Le code est : {code}" : _emailContentEN.NewDeviceEmail + $"\n The code is : {code}" ,

                };
                string url = $"{_configuration.NotificationAddress}/API/Emailing/Send";
                ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, message);

                if (sendEmail != null)
                {
                    return new ResponseAPI<bool>
                    {
                        StatusCode = sendEmail.StatusCode,
                        ExceptionMessage = sendEmail.ExceptionMessage
                    };
                }
            }
            catch (Exception ex)
            {
                // 🔧 TEMPORAIRE : Log l'erreur mais continue le processus
                Console.WriteLine($"⚠️ Email service error (ignored for testing): {ex.Message}");

                // Retourner un succès pour permettre les tests
                return new ResponseAPI<bool>
                {
                    StatusCode = System.Net.HttpStatusCode.OK,
                    ExceptionMessage = "Email service temporarily disabled for testing",
                    ObjectValue = true
                };
            }

            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with an other email !"
            };
        }

        public ResponseAPI<bool> SendResetPasswordEmail(ForgetPasswordDTO forgetPassword, string code)
        {

            string template = @"
<!DOCTYPE html>
<html>
<head>

  <meta charset=""utf-8"">
  <meta http-equiv=""x-ua-compatible"" content=""ie=edge"">
  <title>Password Reset</title>
  <meta name=""viewport"" content=""width=device-width, initial-scale=1"">
  <style type=""text/css"">
 
  @media screen {
    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 400;
      src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
    }
    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 700;
      src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff');
    }
  }

  body,
  table,
  td,
  a {
    -ms-text-size-adjust: 100%; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }
 
  table,
  td {
    mso-table-rspace: 0pt;
    mso-table-lspace: 0pt;
  }

  img {
    -ms-interpolation-mode: bicubic;
  }
 
  a[x-apple-data-detectors] {
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-decoration: none !important;
  }

  div[style*=""margin: 16px 0;""] {
    margin: 0 !important;
  }
  body {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  table {
    border-collapse: collapse !important;
  }
  a {
    color: #1a82e2;
  }
  img {
    height: auto;
    line-height: 100%;
    text-decoration: none;
    border: 0;
    outline: none;
  }
  </style>

</head>
<body style=""background-color: #e9ecef;"">

  <div class=""preheader"" style=""display: none; max-width: 0; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: #fff; opacity: 0;"">
    A preheader is the short summary text that follows the subject line when an email is viewed in the inbox.
  </div>

  <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">

        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">
          <tr>
            <td align=""center"" valign=""top"" style=""padding: 36px 24px;"">
              <a href=""http://localhost:4200/#/"" target=""_blank"" style=""display: inline-block;"">
                <img src=""https://dynofiles.s3.amazonaws.com/Images/Logo.png"" alt=""Logo"" border=""0"" width=""200"" style=""display: block; width: 200px; max-width: 200px; min-width: 100px;"">
              </a>
            </td>
          </tr>
        </table>
  
      </td>
    </tr>

    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">
          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 36px 24px 0; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; border-top: 3px solid #d4dadf;"">
              <h1 style=""margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -1px; line-height: 48px;"">Reset Your Password</h1>
            </td>
          </tr>
        </table>
 
      </td>
    </tr>

    <tr>
      <td align=""center"" bgcolor=""#e9ecef"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"">
              <p style=""margin: 0;"">Tap the button below to reset your password. If you didn't request a password reset, you can safely ignore this email.</p>
            </td>
          </tr>
          <tr>
            <td align=""left"" bgcolor=""#ffffff"">
              <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
                <tr>
                  <td align=""center"" bgcolor=""#ffffff"" style=""padding: 12px;"">
                    <table border=""0"" cellpadding=""0"" cellspacing=""0"">
                      <tr>
                        <td align=""center"" bgcolor=""#1a82e2"" style=""border-radius: 6px;"">
                          <a href=""#RESET_URL#"" target=""_blank"" style=""display: inline-block; padding: 16px 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px;"">Reset Password</a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"">
              <p style=""margin: 0;"">If clicking the button above doesn't work, you can copy and paste the following link into your web browser:</p>
              <p style=""margin: 0;""><a href=""#RESET_URL#"" target=""_blank"">#RESET_URL#</a></p>
            </td>
          </tr>

          <tr>
            <td align=""left"" bgcolor=""#ffffff"" style=""padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px; border-bottom: 3px solid #d4dadf"">
              <p style=""margin: 0;"">Best Regards,<br> Your Name</p>
            </td>
          </tr>
     

        </table>

      </td>
    </tr>

    <tr>
      <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 24px;"">
        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"" style=""max-width: 600px;"">

          <tr>
            <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;"">
              <p style=""margin: 0;"">You are receiving this email because a password reset request was received for your account. If you did not request a password reset, please ignore this email.</p>
            </td>
          </tr>
          <tr>
            <td align=""center"" bgcolor=""#e9ecef"" style=""padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;"">
              <p style=""margin: 0;"">Dyno & Motiva Systems, THE DOT 1053 Lac Malären, Tunis</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>

";

            template = template.Replace("#RESET_URL#", $"{forgetPassword.ClientURI}?code={code}&email={forgetPassword.Email}")
                .Replace("[type_of_action]", "Reset password");
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO message = new MessageEmailDTO
            {
                To = new List<string> { forgetPassword.Email },
                Subject = "Reset password token",
                Content = template,
                IsBodyHtml = true

            };
            string url = $"{_configuration.NotificationAddress}/API/Emailing/Send";
            ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, message);

            if(sendEmail != null )
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = sendEmail.StatusCode,
                    ExceptionMessage= sendEmail.ExceptionMessage
                };
            }

            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
       
        public ResponseAPI<bool> sendConfirmationEmail(string email,string code, string token)
        {
            //need to be chnage read url from json file setting
            var template = code.Replace("#URL#", $"{_configuration.PlatformAdminAddress}/#/auth/MailConfirmed?token=" + HttpUtility.UrlEncode(token)+"&email="+ HttpUtility.UrlEncode(email))
                .Replace("[type_of_action]", "Confirm mail");
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO messageDto = new MessageEmailDTO
            {
                To = new List<string> { email },
                Subject = "Confirm mail",
                Content = template,
                IsBodyHtml = true

            };
            MailMessage mailMessage = new MailMessage();
            mailMessage.From = new MailAddress("<EMAIL>");
            foreach (var recipient in messageDto.To)
            {
                mailMessage.To.Add(recipient);
            }
            mailMessage.Subject = messageDto.Subject;
            mailMessage.Body = messageDto.Content;
            mailMessage.IsBodyHtml = messageDto.IsBodyHtml; // Use the property to set the email format
             bool sendEmail ;
            using (SmtpClient smtpClient = new SmtpClient("ssl0.ovh.net", 587))
            {
              
                smtpClient.EnableSsl = true; // Enable SSL
                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "DynoInfo123");

                try
                {
                    smtpClient.Send(mailMessage);
                    Console.WriteLine("Email sent successfully.");
                    sendEmail = true;
                }
                catch (Exception ex)
                {
                    sendEmail = false;
                    Console.WriteLine("Exception caught in sending email: {0}", ex.ToString());
                }
            }
                /* string url = $"{_configuration.Notification}/API/Emailing/Send";
                 ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, message);*/
                if (sendEmail)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Accepted,
                    ExceptionMessage ="",
                    ObjectValue = true
                };
            }
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
        public ResponseAPI<bool> sendAddCompanyEmail(string email, string code)
        {
            //need to be chnage read url from json file setting
            var template = code;
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO messageDto = new MessageEmailDTO
            {
                To = new List<string> { email },
                Subject = "Welcome to Dyno Platform!",
                Content = template,
                IsBodyHtml = true

            };
            MailMessage mailMessage = new MailMessage();
            mailMessage.From = new MailAddress("<EMAIL>");
            foreach (var recipient in messageDto.To)
            {
                mailMessage.To.Add(recipient);
            }
            mailMessage.Subject = messageDto.Subject;
            mailMessage.Body = messageDto.Content;
            mailMessage.IsBodyHtml = messageDto.IsBodyHtml; // Use the property to set the email format
            bool sendEmail;
            using (SmtpClient smtpClient = new SmtpClient("ssl0.ovh.net", 587))
            {

                smtpClient.EnableSsl = true; // Enable SSL
                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "DynoInfo123");

                try
                {
                    smtpClient.Send(mailMessage);
                    Console.WriteLine("Email sent successfully.");
                    sendEmail = true;
                }
                catch (Exception ex)
                {
                    sendEmail = false;
                    Console.WriteLine("Exception caught in sending email: {0}", ex.ToString());
                }
            }
            /* string url = $"{_configuration.Notification}/API/Emailing/Send";
             ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, message);*/
            if (sendEmail)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Accepted,
                    ExceptionMessage = "",
                    ObjectValue = true
                };
            }
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
        public ResponseAPI<bool> sendSalesOrderEmail(string email, string template)
        {
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO messageDto = new MessageEmailDTO
            {
                To = new List<string> { email },
                Subject = "Dyno Sales Order",
                Content = template,
                IsBodyHtml = true

            };
            MailMessage mailMessage = new MailMessage();
            mailMessage.From = new MailAddress("<EMAIL>");
            foreach (var recipient in messageDto.To)
            {
                mailMessage.To.Add(recipient);
            }
            mailMessage.Subject = messageDto.Subject;
            mailMessage.Body = messageDto.Content;
            mailMessage.IsBodyHtml = messageDto.IsBodyHtml; // Use the property to set the email format
            bool sendEmail;
            using (SmtpClient smtpClient = new SmtpClient("ssl0.ovh.net", 587))
            {

                smtpClient.EnableSsl = true; // Enable SSL
                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "DynoInfo123");

                try
                {
                    smtpClient.Send(mailMessage);
                    Console.WriteLine("Email sent successfully.");
                    sendEmail = true;
                }
                catch (Exception ex)
                {
                    sendEmail = false;
                    Console.WriteLine("Exception caught in sending email: {0}", ex.ToString());
                }
            }
             //string url = $"{_configuration.Notification}/API/Emailing/Send";
             //ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, mailMessage);
            if (sendEmail)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Accepted,
                    ExceptionMessage = ""
                };
            }
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
        public ResponseAPI<bool> sendSalesInvoiceEmail(string email, string template)
        {
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO messageDto = new MessageEmailDTO
            {
                To = new List<string> { email },
                Subject = "Dyno Sales Invoice",
                Content = template,
                IsBodyHtml = true

            };
            MailMessage mailMessage = new MailMessage();
            mailMessage.From = new MailAddress("<EMAIL>");
            foreach (var recipient in messageDto.To)
            {
                mailMessage.To.Add(recipient);
            }
            mailMessage.Subject = messageDto.Subject;
            mailMessage.Body = messageDto.Content;
            mailMessage.IsBodyHtml = messageDto.IsBodyHtml; // Use the property to set the email format
            bool sendEmail;
            using (SmtpClient smtpClient = new SmtpClient("ssl0.ovh.net", 587))
            {

                smtpClient.EnableSsl = true; // Enable SSL
                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "DynoInfo123");

                try
                {
                    smtpClient.Send(mailMessage);
                    Console.WriteLine("Email sent successfully.");
                    sendEmail = true;
                }
                catch (Exception ex)
                {
                    sendEmail = false;
                    Console.WriteLine("Exception caught in sending email: {0}", ex.ToString());
                }
            }
            //string url = $"{_configuration.Notification}/API/Emailing/Send";
            //ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, mailMessage);
            if (sendEmail)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Accepted,
                    ExceptionMessage = ""
                };
            }
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
        public ResponseAPI<bool> sendCashBackEmail(CompanyDTO company,CashBackDTO cashBackDTO)
        {
            var requestDate = cashBackDTO.CreationTime.ToString();
            var validationDate = cashBackDTO.ValidationDate.ToString();
            var rib = cashBackDTO.PaymentDetails?.RIB;
            var dynoAmount = cashBackDTO.DynoAmount.ToString();
            var totalAmount = cashBackDTO.TotalAmount.ToString();
            var emailBody = EmailTemplateEN.CashBackEmailbody;
            var content = emailBody.Replace("[dynoAmount]",dynoAmount).Replace("[requestDate]", requestDate).Replace("[validationDate]", validationDate).Replace("[totalAmount]",totalAmount).Replace("[rib]",rib);
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;
            MessageEmailDTO messageDto = new MessageEmailDTO
            {
                To = new List<string> { company.Email },
                Subject = "Dyno CashBack Request",
                Content = content,
                IsBodyHtml = true

            };
            MailMessage mailMessage = new MailMessage();
            mailMessage.From = new MailAddress("<EMAIL>");
            foreach (var recipient in messageDto.To)
            {
                mailMessage.To.Add(recipient);
            }
            mailMessage.Subject = messageDto.Subject;
            mailMessage.Body = messageDto.Content;
            mailMessage.IsBodyHtml = messageDto.IsBodyHtml; // Use the property to set the email format
            bool sendEmail;
            using (SmtpClient smtpClient = new SmtpClient("ssl0.ovh.net", 587))
            {

                smtpClient.EnableSsl = true; // Enable SSL
                smtpClient.Credentials = new NetworkCredential("<EMAIL>", "DynoInfo123");

                try
                {
                    smtpClient.Send(mailMessage);
                    Console.WriteLine("Email sent successfully.");
                    sendEmail = true;
                }
                catch (Exception ex)
                {
                    sendEmail = false;
                    Console.WriteLine("Exception caught in sending email: {0}", ex.ToString());
                }
            }
            //string url = $"{_configuration.Notification}/API/Emailing/Send";
            //ResponseAPI<MessageEmailDTO>? sendEmail = _helper.Post(url, mailMessage);
            if (sendEmail)
            {
                return new ResponseAPI<bool>
                {
                    StatusCode = HttpStatusCode.Accepted,
                    ExceptionMessage = ""
                };
            }
            return new ResponseAPI<bool>
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                ExceptionMessage = "Error in sending Message for this email, please try with other email !"
            };
        }
    }
}
