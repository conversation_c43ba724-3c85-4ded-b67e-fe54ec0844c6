﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SI_Status_Transaction_Succeeded")]
    public class UpdateSalesInvoiceStatusTransactionSucceeded : IExternalTaskHandler
    {
        private readonly ILogger<UpdateSalesInvoiceStatusTransactionSucceeded> _logger;
        private readonly ISalesInvoiceService _salesInvoiceService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public UpdateSalesInvoiceStatusTransactionSucceeded(
        ILogger<UpdateSalesInvoiceStatusTransactionSucceeded> logger,
        ISalesInvoiceService salesInvoiceService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId)

        {
            _logger = logger;
            _salesInvoiceService = salesInvoiceService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
                SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
                //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());

                if (salesInvoiceDTO == null)
                {
                    outputVariables.Add("Result_SI_Status_Transaction_Succeded", new BooleanVariable(false));
                    outputVariables.Add("Error_SI_Status_Transaction_Succeded", new StringVariable("json conversion failed"));

                }
                else
                {
                    var response = _salesInvoiceService.UpdateStatus(salesInvoiceDTO.Id, InvoiceStatus.TransactionSucceeded, true, null, null, true);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        outputVariables.Add("Result_SI_Status_Transaction_Succeded", new BooleanVariable(true));
                    }
                    else
                    {
                        outputVariables.Add("Result_SI_Status_Transaction_Succeded", new BooleanVariable(false));
                        outputVariables.Add("Error_SI_Status_Transaction_Succeded", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SI_Status_Transaction_Succeded", new BooleanVariable(false));
                outputVariables.Add("Error_SI_Status_Transaction_Succeded", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
