﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class company_TaxCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7512033a-3ffb-4156-9c22-014b0b035348"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("80cb2a33-a443-492b-b005-eb51b674e504"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9c28fb26-9536-467e-8667-69716031350a"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"));

            migrationBuilder.RenameColumn(
                name: "VAT",
                table: "Company",
                newName: "TaxCode");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "c11d7307-75f2-49ea-9f02-39711990e723", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5678) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1deb2074-1749-469a-bae3-c27c3a6b18f4", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5644) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "54d55432-72fd-45e8-b2b2-e2d2366eacff", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5652) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fc840988-7cb0-4639-9596-55688a85efa3", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5683) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "e18f9697-c753-49c6-8593-48e28bf94647", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5661) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f01d645f-21f7-4b14-b3e6-db12f15cee61", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(5657) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "fc4db08b-aa02-47a9-af4a-d79bc5ff2c57", new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6007), new DateTime(2024, 3, 26, 10, 1, 11, 15, DateTimeKind.Utc).AddTicks(6006), "AQAAAAEAACcQAAAAEND7zUinVYKTFkL1KNVZ54BZLmGMB/o1AHyoYCOSY66siyngCJjiDM+J8y6C64D7Jg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("04aaa642-2320-46e0-9622-1599610008d9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("896e4f08-4840-420e-bd54-46836d620107"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("01976278-669c-4fb3-a054-a2400b457e9e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("057454a0-9c0f-4c8a-a233-64e15308e9e8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("057803b1-805f-49e8-83cf-81c26071bdbf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("10f010d8-aebd-42d0-8960-3b70192b753d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1680bfa1-17a0-4d50-a5fd-d02e94dc6dc7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("17d48710-4642-4143-8bf5-db4b3149624c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("46c99795-0cd0-414d-be66-af6dcc6af7ed"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("49847712-c67b-4c2d-892c-c2ac5e9e9549"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4deb3535-967a-4ad1-aa70-70e6600d24cc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4e3eccd9-324e-42d0-9c10-f81eebc96530"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4ff1df44-b725-40d6-9eed-6ec0c7979ef9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("62ef46ef-2265-4ab2-9ff0-2b1f5475c398"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("707e694f-59f0-4ca2-9976-bbcdcbc701cd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7d2122f7-0300-4f92-b512-241e9244ee6b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("86a9c0b4-b4be-4a62-a4ce-941791cdb985"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8818f091-15b7-41e6-9794-eca7202007df"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("8a869811-5c92-4d7e-bf05-6ae811172508"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("896e4f08-4840-420e-bd54-46836d620107"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("91529635-074d-4e93-9681-4df7aa834ea7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a8750d27-7fe7-4243-8aed-9033907aa949"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b0f95d68-4c89-4dbe-8abb-c5c331d17357"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b29c7f50-30b6-4de2-9b9b-e4598781afa9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d6e77324-60ca-4975-955b-4821acb5e205"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ed2d78c3-2dc5-4148-b00b-12711541df9f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("04aaa642-2320-46e0-9622-1599610008d9"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f6d7877c-b570-47a1-9b25-cff6d8a03c33"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("01976278-669c-4fb3-a054-a2400b457e9e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("057454a0-9c0f-4c8a-a233-64e15308e9e8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("057803b1-805f-49e8-83cf-81c26071bdbf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("10f010d8-aebd-42d0-8960-3b70192b753d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1680bfa1-17a0-4d50-a5fd-d02e94dc6dc7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("17d48710-4642-4143-8bf5-db4b3149624c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("46c99795-0cd0-414d-be66-af6dcc6af7ed"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("49847712-c67b-4c2d-892c-c2ac5e9e9549"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4deb3535-967a-4ad1-aa70-70e6600d24cc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4e3eccd9-324e-42d0-9c10-f81eebc96530"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4ff1df44-b725-40d6-9eed-6ec0c7979ef9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("62ef46ef-2265-4ab2-9ff0-2b1f5475c398"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("707e694f-59f0-4ca2-9976-bbcdcbc701cd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7d2122f7-0300-4f92-b512-241e9244ee6b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("86a9c0b4-b4be-4a62-a4ce-941791cdb985"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8818f091-15b7-41e6-9794-eca7202007df"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8a869811-5c92-4d7e-bf05-6ae811172508"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("91529635-074d-4e93-9681-4df7aa834ea7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a8750d27-7fe7-4243-8aed-9033907aa949"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b0f95d68-4c89-4dbe-8abb-c5c331d17357"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b29c7f50-30b6-4de2-9b9b-e4598781afa9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d6e77324-60ca-4975-955b-4821acb5e205"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ed2d78c3-2dc5-4148-b00b-12711541df9f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f6d7877c-b570-47a1-9b25-cff6d8a03c33"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("04aaa642-2320-46e0-9622-1599610008d9"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("22fe318d-1255-4508-97e7-40a32e7037ea"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("40003703-645a-40f1-b1cc-1b22db42f37c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("63095082-7f72-42a5-b8e5-c146a6eca2d8"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("896e4f08-4840-420e-bd54-46836d620107"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ecf678a7-1776-4453-87ca-eaa9feff337d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2e8768fd-9b36-49c6-8e6f-e665fe4193a1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("479d5e3b-4722-4662-9c33-ed8dcd8044fc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c3e7e20-dcb0-4976-b87e-71a07b7b703b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5d164d03-d79c-411c-8f27-c321d31bb51c"));

            migrationBuilder.RenameColumn(
                name: "TaxCode",
                table: "Company",
                newName: "VAT");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0acbfbe8-3349-482b-9bfc-4824250a0816", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5106) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "0e7a606d-8454-413e-9868-e5bc3eb4f4f3", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5083) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "64543438-a9da-444a-b7db-26ea225cc2a4", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5091) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "8f0319ae-5e56-4005-b90d-3e002266a1e7", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5120) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "312096dc-9485-48db-82a9-8f18c56f720c", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5101) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "f0af4f7c-e9a5-4364-ad92-b71a37293160", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5096) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "a90c297a-f5eb-4a40-95ce-f7e870d066df", new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), new DateTime(2024, 3, 23, 16, 35, 20, 522, DateTimeKind.Utc).AddTicks(5409), "AQAAAAEAACcQAAAAEJ0+oX6N25DaHXAF43NrLXkDoCQOrjJww9ZiMa6vObnLktq/9yCa12SfPC+8f+IVAg==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("0df1fd45-3ee5-4f9b-a994-f4f29b47ce76"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("03e2a7db-c547-477a-af1d-1b4d96409cf6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("235a0a76-b2e4-4e6d-b982-9d0839d73b9d"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("29b4568b-c736-4049-8ed0-50a062e9d94e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("12db6515-c16c-485c-877f-bd0a3e3ecc28"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("14eed7fc-7429-44e5-b581-e25d9a7f5635"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("18e3e020-4150-4dc0-ba80-50d5bb7030a4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("279b360e-acb0-4615-8fb6-ecb894b242e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2f30246e-7dd0-4f25-8df8-7e14d21917d1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3108416e-79fb-4ec2-90c4-aaf78a99d7bf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("38ffb2de-6e8c-404a-8ed5-65a8ec59632d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4f052e62-198e-4449-acb9-bc3fcfe6386c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57419206-bbc1-4069-925d-b5c73a6817ec"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("6339673e-04d0-4ed4-85ec-feba99304a5c"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("5e5d849f-2321-4603-95bf-b97bad8fc7f2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6236644f-feb4-43b0-a37f-d40ebcfd53ad"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6aa92bc0-9f04-4388-ba50-2ed4c25e86b6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7512033a-3ffb-4156-9c22-014b0b035348"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("80cb2a33-a443-492b-b005-eb51b674e504"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("81acd893-77c1-46c0-8321-6a149e2bc054"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("9c28fb26-9536-467e-8667-69716031350a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a904a188-3399-42f1-88b0-0ca6e275c2f9"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c463caa3-ab65-4672-9c10-a23fd1864fe1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("71fcb027-ce86-46ac-9e72-34b2e2e909e1"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d8a287a7-b393-4b47-8db3-f3ff5996a713"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e2b78207-5ed4-46e5-92d5-93716124c9e3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("e629a8f5-0ee2-4abf-b1d4-1bbea2b65c8d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("4c807f9c-cf82-4c22-8ca0-cab3373310b8"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ef9eebc3-1a47-453a-8875-e120293a9d55"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("c8a746d9-2fc8-4902-9bc9-514492614f7e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f7ff2295-2fbf-47b2-9c2e-b3af51a380e4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("c97cae72-fd03-4b73-8a73-e4b475487eb7"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("fc316c2e-0a35-46f5-a12d-ebbf2c08de5e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("ea62bde1-46e5-4c5f-b57a-ea2494e3d164"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }
    }
}
