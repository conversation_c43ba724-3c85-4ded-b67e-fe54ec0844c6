﻿using Platform.Dyno.AccessManagement.BusinessModel.Notification;
using Platform.Dyno.AccessManagement.DTO.Notification;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared.ResponseAPI;
using Platform.Dyno.Shared.SharedClass;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.AccessManagement.Business.IService.Notification
{
    public interface ISubscriberDeviceService : IGenericService<SubscriberDeviceDTO>
    {
        ResponseAPI<SubscriberDeviceDTO> RegistrationDevice(SubscriberDeviceDTO dtoObject);
    }
}
