﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33403.182
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Shared", "..\..\..\Platform.Dyno.Microservice.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared\Platform.Dyno.Shared.csproj", "{19D4740A-A36D-4880-A984-15C97AF14A5F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure", "Infrastructure\Infrastructure.csproj", "{6B40E8B8-9011-42F4-8BFB-AD5B12673C47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "Domain\Domain.csproj", "{F3E15807-C04A-45C0-B75B-C963AA249D52}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "Application\Application.csproj", "{46E01B90-AC15-4E66-8631-6DE127243EC1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Payment.WebAPI", "Platform.Dyno.Payment.WebAPI\Platform.Dyno.Payment.WebAPI.csproj", "{4CDE4203-02C3-4EA9-8C84-DFE707818D47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Platform.Dyno.Payment.DTO", "Platform.Dyno.Payment.DTO\Platform.Dyno.Payment.DTO.csproj", "{0E3CAE91-E8B5-4182-9817-509DBC4D0607}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Server.Kafka", "..\..\Server.Kafka\Server.Kafka\Server.Kafka.csproj", "{7E81F76F-6570-4700-AFFF-5F10D936F154}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{19D4740A-A36D-4880-A984-15C97AF14A5F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19D4740A-A36D-4880-A984-15C97AF14A5F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19D4740A-A36D-4880-A984-15C97AF14A5F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19D4740A-A36D-4880-A984-15C97AF14A5F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B40E8B8-9011-42F4-8BFB-AD5B12673C47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B40E8B8-9011-42F4-8BFB-AD5B12673C47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B40E8B8-9011-42F4-8BFB-AD5B12673C47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B40E8B8-9011-42F4-8BFB-AD5B12673C47}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3E15807-C04A-45C0-B75B-C963AA249D52}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3E15807-C04A-45C0-B75B-C963AA249D52}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3E15807-C04A-45C0-B75B-C963AA249D52}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3E15807-C04A-45C0-B75B-C963AA249D52}.Release|Any CPU.Build.0 = Release|Any CPU
		{46E01B90-AC15-4E66-8631-6DE127243EC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46E01B90-AC15-4E66-8631-6DE127243EC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46E01B90-AC15-4E66-8631-6DE127243EC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46E01B90-AC15-4E66-8631-6DE127243EC1}.Release|Any CPU.Build.0 = Release|Any CPU
		{4CDE4203-02C3-4EA9-8C84-DFE707818D47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CDE4203-02C3-4EA9-8C84-DFE707818D47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CDE4203-02C3-4EA9-8C84-DFE707818D47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CDE4203-02C3-4EA9-8C84-DFE707818D47}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E3CAE91-E8B5-4182-9817-509DBC4D0607}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E3CAE91-E8B5-4182-9817-509DBC4D0607}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E3CAE91-E8B5-4182-9817-509DBC4D0607}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E3CAE91-E8B5-4182-9817-509DBC4D0607}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E81F76F-6570-4700-AFFF-5F10D936F154}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E81F76F-6570-4700-AFFF-5F10D936F154}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E81F76F-6570-4700-AFFF-5F10D936F154}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E81F76F-6570-4700-AFFF-5F10D936F154}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7A2BFC32-E8FE-4427-8633-0021E99B1369}
	EndGlobalSection
EndGlobal
