﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class update_Table_blacklist : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0e21cfb3-60e4-409a-b438-a8b60d236473"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("135e4f25-022c-47a3-9b0f-79e95d75074e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2811492f-da88-4933-af5a-902094630cc1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2d283c82-87ad-4d20-b039-fc50734a07b7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("36fbce42-4c38-4a74-ac67-bb882983c80b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("42b51296-900b-429c-a89d-0cd0b5b7c9c3"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4b0eb34d-075a-4628-bd0b-dc52f7eec0a2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4bdfebd8-8b39-418b-9a57-b2e617d0dc92"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("50a35b63-cff0-46d6-9ea7-4c4119bf2147"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("592eef25-0a3a-4d97-bcea-98cf0ec25340"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5f43caba-f688-44ed-8ce1-a207e262a298"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("608648d7-8d75-486a-8b19-fa329b4ca5cf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("68aa16e1-3dbc-4b69-828d-966e519faa06"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6e9bd497-728b-4101-bf4e-5c8c29cce61b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6fdbc3ad-b568-490d-a349-97a5796b5ffb"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("74af2506-958e-41c6-9e13-764ae5990d34"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("887be67b-1c47-4273-ac91-a4dcdecbd8ac"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b6186c1b-241e-48da-aff1-7880140bfbbf"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b96f629c-5f5a-4945-902b-1ff7b3df6443"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c00d081d-6d82-4a47-a343-11ecf03cb199"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c2eb4422-ceb6-4209-b9d3-2d35ccdd0b9d"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c5a8f77d-2716-463f-83f7-13424066f239"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ed688d1c-88bf-4035-836f-ac0ed8b93411"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7c360f8-10ba-45bd-b1e8-0172e8c4b060"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("1447408f-467f-4c15-88d8-df21affa0d07"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"));

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "BlackListedUserEntity",
                newName: "Description");

            migrationBuilder.AddColumn<int>(
                name: "NumberOfTimesBlocked",
                table: "BlackListedUserEntity",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "BlackListedUserEntity",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("67e6fcdd-773a-4f41-a3f3-6a4392b05f83"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("94bcff08-2c50-4433-a443-2df0966e875e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ae612f50-d2c7-4e50-923b-1cf2e720fbe0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d70e0ea5-55e6-4a7f-9c02-2b7d71f19e93"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "4de4f63b-42b2-4989-b2cf-146c7cd05bc5", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2203) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "9fd92945-e9ec-4061-8e68-b313746bd84a", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2126) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "4a1d0aae-bc08-4415-9398-92374eede7e1", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2158) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "44e985d4-3917-4d6f-82c6-d81ce4e0ca49", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2216) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "1fabe081-e8c0-4eeb-bc25-32ecd7a5a773", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2190) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "4d643a16-f513-4d4f-8655-2538f2b8c1b2", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(2174) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "0ecbe0d6-b296-4631-9032-44ca7acbc8b8", new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(3012), new DateTime(2024, 4, 22, 12, 48, 55, 4, DateTimeKind.Utc).AddTicks(3009), "AQAAAAEAACcQAAAAEExMNvIojxvAo24MX1fDhR0bT6684yfzJCOTv5TUhoIXSkqCqBEE9+R6gyR+YRC9GQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("67e6fcdd-773a-4f41-a3f3-6a4392b05f83"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("94bcff08-2c50-4433-a443-2df0966e875e"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("ae612f50-d2c7-4e50-923b-1cf2e720fbe0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("67e6fcdd-773a-4f41-a3f3-6a4392b05f83"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("ae612f50-d2c7-4e50-923b-1cf2e720fbe0"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f77e3346-a525-47f2-909f-162865edb558"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("d70e0ea5-55e6-4a7f-9c02-2b7d71f19e93"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("01cbe3c5-a927-477c-9daa-bef8a43b7d0b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("02498a99-1350-41a0-b10c-8c84f4d4b518"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("f77e3346-a525-47f2-909f-162865edb558"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("250c9633-7ec8-48cc-872d-5c365da9c368"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("3c095416-d495-405b-9c47-3e195afbe413"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4004950a-7bce-4cec-9ead-fde44315d9b0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("410ef5c5-4d12-4bae-9cd4-3a6247b2228f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("42802e1e-c53c-4865-9dde-6390b821dd8c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4431242f-d2ae-4470-8249-81a66902b1b4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("47978669-8f36-4b56-918e-efb490a7b827"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("4c1f2320-dc3f-4cdd-9166-c5f0f2907116"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("591d4d91-94f2-4152-976d-2d0536c120be"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("8b395819-e990-4c74-83ce-44581b501494"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("f77e3346-a525-47f2-909f-162865edb558"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("97ae9ac5-837e-43f4-b2f7-e150cbf422b0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("*************-4361-ba3e-c8483f31ff12"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("a6e7e822-052d-45a5-9240-c95bb664f013"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("f77e3346-a525-47f2-909f-162865edb558"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("a73acc56-9870-47a6-a4e2-5b65e96e61d7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("f77e3346-a525-47f2-909f-162865edb558"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b63e91d4-e491-4378-bff6-ee98806802c2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b82f48a2-34ef-4ed4-a054-268c9a78f71b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("b99312b2-db90-4120-badb-3f36756717e6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("cdc9bc85-9050-4daf-b919-8b266c44761c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("d0433610-c097-47ab-a76b-26ccb57f821c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("eb95ba63-e373-43d3-9f6d-954321b213f2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ed720781-e02f-4de2-9f79-958d395b3ae1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ffe4f965-a6ac-41ea-aa94-e616bc80d40f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("01cbe3c5-a927-477c-9daa-bef8a43b7d0b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("02498a99-1350-41a0-b10c-8c84f4d4b518"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("250c9633-7ec8-48cc-872d-5c365da9c368"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3c095416-d495-405b-9c47-3e195afbe413"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4004950a-7bce-4cec-9ead-fde44315d9b0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("410ef5c5-4d12-4bae-9cd4-3a6247b2228f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("42802e1e-c53c-4865-9dde-6390b821dd8c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4431242f-d2ae-4470-8249-81a66902b1b4"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("47978669-8f36-4b56-918e-efb490a7b827"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4c1f2320-dc3f-4cdd-9166-c5f0f2907116"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("591d4d91-94f2-4152-976d-2d0536c120be"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8b395819-e990-4c74-83ce-44581b501494"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("97ae9ac5-837e-43f4-b2f7-e150cbf422b0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("*************-4361-ba3e-c8483f31ff12"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a6e7e822-052d-45a5-9240-c95bb664f013"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a73acc56-9870-47a6-a4e2-5b65e96e61d7"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b63e91d4-e491-4378-bff6-ee98806802c2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b82f48a2-34ef-4ed4-a054-268c9a78f71b"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b99312b2-db90-4120-badb-3f36756717e6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cdc9bc85-9050-4daf-b919-8b266c44761c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d0433610-c097-47ab-a76b-26ccb57f821c"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("eb95ba63-e373-43d3-9f6d-954321b213f2"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ed720781-e02f-4de2-9f79-958d395b3ae1"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ffe4f965-a6ac-41ea-aa94-e616bc80d40f"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("36d4e1f0-9324-4d43-bdfe-c2fd57a53330"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d02e385f-672d-4544-8716-8b8f2d4135a0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d2e3a12f-8996-48b7-8a69-493992f18d20"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d3467233-3ffc-4792-82f1-9117f73aa5d6"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("eb5ea91b-4b45-4036-9297-e491a6807bbc"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f77e3346-a525-47f2-909f-162865edb558"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("67e6fcdd-773a-4f41-a3f3-6a4392b05f83"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("94bcff08-2c50-4433-a443-2df0966e875e"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ae612f50-d2c7-4e50-923b-1cf2e720fbe0"));

            migrationBuilder.DeleteData(
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("d70e0ea5-55e6-4a7f-9c02-2b7d71f19e93"));

            migrationBuilder.DropColumn(
                name: "NumberOfTimesBlocked",
                table: "BlackListedUserEntity");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "BlackListedUserEntity");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "BlackListedUserEntity",
                newName: "Name");

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "b73daf13-c629-4a54-b7a8-f3e1f0326fa8", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2628) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "a56ced52-2705-47c0-89d8-a280d9b87d87", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2600) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "362e1e97-1d36-4305-a611-97f65d685d57", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2610) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "acd068b0-8e5a-4a3b-a7ac-4da021b16a77", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2634) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "26f3be49-53e9-4c5a-a2ad-843d937877e9", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2623) });

            migrationBuilder.UpdateData(
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"),
                columns: new[] { "ConcurrencyStamp", "CreationTime" },
                values: new object[] { "fffa58c2-afee-4089-b7aa-37f3b16e46af", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(2617) });

            migrationBuilder.UpdateData(
                table: "User",
                keyColumn: "Id",
                keyValue: new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"),
                columns: new[] { "ConcurrencyStamp", "CreationTime", "DateOfBirth", "PasswordHash" },
                values: new object[] { "3357e32c-dddc-4cdb-98dc-1cb924504561", new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3100), new DateTime(2024, 4, 18, 9, 57, 13, 153, DateTimeKind.Utc).AddTicks(3099), "AQAAAAEAACcQAAAAEIioYzLxCw9ey+k42CPIPBp3+kaOLV0ME0JBlFgmfiV82HnA7uBGLAOGNxxW+xuoHQ==" });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("6f257125-8e3b-4f0a-9a03-7ebd90c650dd"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("9c06842d-8480-4a08-b0b1-d48f00375232"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("96c2fa9b-909f-4ccf-b606-1ae9d160b359"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("086b5627-ff77-4ce5-a40b-1d43ae835757"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("0e21cfb3-60e4-409a-b438-a8b60d236473"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("135e4f25-022c-47a3-9b0f-79e95d75074e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("2811492f-da88-4933-af5a-902094630cc1"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("2d283c82-87ad-4d20-b039-fc50734a07b7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("36fbce42-4c38-4a74-ac67-bb882983c80b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("42b51296-900b-429c-a89d-0cd0b5b7c9c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4b0eb34d-075a-4628-bd0b-dc52f7eec0a2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("4bdfebd8-8b39-418b-9a57-b2e617d0dc92"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("50a35b63-cff0-46d6-9ea7-4c4119bf2147"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("592eef25-0a3a-4d97-bcea-98cf0ec25340"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5f43caba-f688-44ed-8ce1-a207e262a298"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("608648d7-8d75-486a-8b19-fa329b4ca5cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("68aa16e1-3dbc-4b69-828d-966e519faa06"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("6e9bd497-728b-4101-bf4e-5c8c29cce61b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("6fdbc3ad-b568-490d-a349-97a5796b5ffb"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("74af2506-958e-41c6-9e13-764ae5990d34"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("887be67b-1c47-4273-ac91-a4dcdecbd8ac"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("7a815267-d029-4d12-8c32-709e7edbcec5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b6186c1b-241e-48da-aff1-7880140bfbbf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("b96f629c-5f5a-4945-902b-1ff7b3df6443"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("c5f9aa63-a0d6-41a0-8120-bd7c92d0e54b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c00d081d-6d82-4a47-a343-11ecf03cb199"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c2eb4422-ceb6-4209-b9d3-2d35ccdd0b9d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("57295cfe-75a1-4335-a2c4-bf677bfd099b"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c5a8f77d-2716-463f-83f7-13424066f239"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("1447408f-467f-4c15-88d8-df21affa0d07"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("ed688d1c-88bf-4035-836f-ac0ed8b93411"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("4c54e07d-a1a8-4457-bd72-32737ca4d0a7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f7c360f8-10ba-45bd-b1e8-0172e8c4b060"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("7d8ea06d-476e-425e-a136-366f3ffb6e50"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });
        }
    }
}
