﻿using Camunda.Worker;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using Platform.Dyno.AccessManagement.Business.IService.Reporting;
using Platform.Dyno.AccessManagement.DTO.SalesOrder;
using AutoMapper;
using Platform.Dyno.Shared.Enum;
using Platform.Dyno.Shared;
using Platform.Dyno.AccessManagement.Business.IService.SalesOrder;
using Camunda.Worker.Variables;

namespace Platform.Dyno.AccessManagement.Business.TaskHandle.SalesInvoice
{
    [HandlerTopics("SO_Status_Valid")]
    public class UpdateSalesOrderStatusValid : IExternalTaskHandler
    {
        private readonly ILogger<UpdateSalesOrderStatusValid> _logger;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IMapper _mapper;
        private readonly ConfigurationDefaultId _configurationDefaultId;

        public UpdateSalesOrderStatusValid(
        ILogger<UpdateSalesOrderStatusValid> logger,
        ISalesOrderService salesOrderService,
        IMapper mapper,
        ConfigurationDefaultId configurationDefaultId)

        {
            _logger = logger;
            _salesOrderService = salesOrderService;
            _mapper = mapper;
            _configurationDefaultId = configurationDefaultId;
        }

        public async Task<IExecutionResult> HandleAsync(ExternalTask externalTask, CancellationToken cancellationToken)
        {
            var outputVariables = new Dictionary<string, VariableBase>();
            try
            {
                var invoiceJson = externalTask.Variables["SalesInvoice"]?.ToString();
                SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(invoiceJson);
                //SalesInvoiceDTO? salesInvoiceDTO = JsonSerializer.Deserialize<SalesInvoiceDTO>(externalTask.Variables["SalesInvoice"].Value.ToString());

                if (salesInvoiceDTO == null)
                {
                    outputVariables.Add("Result_SO_Status_Valid", new BooleanVariable(false));
                    outputVariables.Add("Error_SO_Status_Valid", new StringVariable("json conversion failed"));

                }
                else
                {
                    var response = _salesOrderService.UpdateStatus(salesInvoiceDTO.SalesOrderId, SalesOrderStatus.Valid, true, null, null, true);
                    if (response != null && response.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        outputVariables.Add("Result_SO_Status_Valid", new BooleanVariable(true));
                    }
                    else
                    {
                        outputVariables.Add("Result_SO_Status_Valid", new BooleanVariable(false));
                        outputVariables.Add("Error_SO_Status_Valid", new JsonVariable(JsonSerializer.Serialize(response)));
                    }
                }
            }
            catch (Exception ex)
            {
                outputVariables.Add("Result_SO_Status_Valid", new BooleanVariable(false));
                outputVariables.Add("Error_SO_Status_Valid", new StringVariable($"an error occured: {ex}"));
            }

            var completeResult = new CompleteResult();
            completeResult.Variables = outputVariables;
            return completeResult;
        }
    }
}
