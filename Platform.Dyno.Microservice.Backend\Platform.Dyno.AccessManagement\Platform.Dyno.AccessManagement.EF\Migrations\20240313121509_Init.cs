﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Platform.Dyno.AccessManagement.EF.Migrations
{
    /// <inheritdoc />
    public partial class Init : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Company",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxRegistration = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: false),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    Picture = table.Column<string>(type: "text", nullable: true),
                    FeePercent = table.Column<double>(type: "double precision", nullable: true),
                    EntrepriseType = table.Column<int>(type: "integer", nullable: false),
                    ServiceType = table.Column<int>(type: "integer", nullable: false),
                    WalletId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Company", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Document",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Path = table.Column<string>(type: "text", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Document", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LogError",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Microservice = table.Column<int>(type: "integer", nullable: false),
                    API = table.Column<string>(type: "text", nullable: false),
                    Error = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    CreationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Level = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LogError", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageNotification",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Topic = table.Column<string>(type: "text", nullable: false),
                    TopicArn = table.Column<string>(type: "text", nullable: false),
                    MessageGroupId = table.Column<string>(type: "text", nullable: false),
                    Protocol = table.Column<string>(type: "text", nullable: false),
                    Title = table.Column<string>(type: "text", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: false),
                    Image = table.Column<string>(type: "text", nullable: false),
                    Rule = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageNotification", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Role",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    NormalizedName = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Role", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SignalRNotification",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Title = table.Column<string>(type: "text", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: false),
                    IsSeen = table.Column<bool>(type: "boolean", nullable: false),
                    CreatorType = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SignalRNotification", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "User",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PinCode = table.Column<string>(type: "text", nullable: false),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    Picture = table.Column<string>(type: "text", nullable: true),
                    Gender = table.Column<int>(type: "integer", nullable: true),
                    DateOfBirth = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UserType = table.Column<int>(type: "integer", nullable: false),
                    CountryCode = table.Column<string>(type: "text", nullable: false),
                    AuthentificationSource = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserName = table.Column<string>(type: "text", nullable: true),
                    NormalizedUserName = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    NormalizedEmail = table.Column<string>(type: "text", nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_User", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Group",
                columns: table => new
                {
                    GroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyName = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Group", x => x.GroupId);
                    table.ForeignKey(
                        name: "FK_Group_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PaymentDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    PaymentMethod = table.Column<int>(type: "integer", nullable: false),
                    RIB = table.Column<string>(type: "text", nullable: false),
                    PaymentDelay = table.Column<int>(type: "integer", nullable: false),
                    FeePercentage = table.Column<double>(type: "double precision", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PaymentDetails_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Ticket",
                columns: table => new
                {
                    TicketId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Amount = table.Column<double>(type: "double precision", nullable: false),
                    Quantity = table.Column<int>(type: "integer", nullable: false),
                    TotalAmount = table.Column<double>(type: "double precision", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsAutomatic = table.Column<bool>(type: "boolean", nullable: false),
                    PeriodType = table.Column<int>(type: "integer", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompanyEntityId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Ticket", x => x.TicketId);
                    table.ForeignKey(
                        name: "FK_Ticket_Company_CompanyEntityId",
                        column: x => x.CompanyEntityId,
                        principalTable: "Company",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesOrder",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProcessInstanceId = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    DynoAmount = table.Column<double>(type: "double precision", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    NetAmount = table.Column<double>(type: "double precision", nullable: false),
                    VATAmount = table.Column<double>(type: "double precision", nullable: false),
                    TotalAmount = table.Column<double>(type: "double precision", nullable: false),
                    PaymentMethod = table.Column<int>(type: "integer", nullable: false),
                    ProductType = table.Column<int>(type: "integer", nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    DocumentId = table.Column<Guid>(type: "uuid", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesOrder_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SalesOrder_Document_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Document",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Permission",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    IsLeaf = table.Column<bool>(type: "boolean", nullable: false),
                    IsRoot = table.Column<bool>(type: "boolean", nullable: false),
                    PartialSelected = table.Column<bool>(type: "boolean", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    PermissionEntityId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permission", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Permission_Permission_PermissionEntityId",
                        column: x => x.PermissionEntityId,
                        principalTable: "Permission",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Permission_Role_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Role",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CompanyNotification",
                columns: table => new
                {
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    NotificationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyNotification", x => new { x.CompanyId, x.NotificationId });
                    table.ForeignKey(
                        name: "FK_CompanyNotification_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CompanyNotification_SignalRNotification_NotificationId",
                        column: x => x.NotificationId,
                        principalTable: "SignalRNotification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Address",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Longitude = table.Column<double>(type: "double precision", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: false),
                    FullAddress = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Address", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Address_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Address_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MacAddress",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MacAddress = table.Column<string>(type: "text", nullable: false),
                    IsConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    IsSaved = table.Column<bool>(type: "boolean", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MacAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MacAddress_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RoleUser",
                columns: table => new
                {
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleUser", x => new { x.RoleId, x.UserId });
                    table.ForeignKey(
                        name: "FK_RoleUser_Role_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Role",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleUser_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SubscriberDeviceEntity",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EndPoint = table.Column<string>(type: "text", nullable: false),
                    EndPointArn = table.Column<string>(type: "text", nullable: false),
                    TargetArn = table.Column<string>(type: "text", nullable: false),
                    SubscriptionArn = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    UserEntityId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriberDeviceEntity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriberDeviceEntity_User_UserEntityId",
                        column: x => x.UserEntityId,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserOTP",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<int>(type: "integer", nullable: false),
                    IsConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserOTP", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserOTP_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserToken",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Token = table.Column<string>(type: "text", nullable: false),
                    RefreshToken = table.Column<string>(type: "text", nullable: false),
                    ExpiredDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserToken", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserToken_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Employee",
                columns: table => new
                {
                    EmployeeId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserName = table.Column<string>(type: "text", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyName = table.Column<string>(type: "text", nullable: false),
                    GroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Employee", x => x.EmployeeId);
                    table.ForeignKey(
                        name: "FK_Employee_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Employee_Group_GroupId",
                        column: x => x.GroupId,
                        principalTable: "Group",
                        principalColumn: "GroupId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Employee_User_UserId",
                        column: x => x.UserId,
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CashBack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    WalletBallance = table.Column<double>(type: "double precision", nullable: false),
                    DynoAmount = table.Column<double>(type: "double precision", nullable: false),
                    FeeAmount = table.Column<double>(type: "double precision", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    NetAmount = table.Column<double>(type: "double precision", nullable: false),
                    VATAmount = table.Column<double>(type: "double precision", nullable: false),
                    TotalAmount = table.Column<double>(type: "double precision", nullable: false),
                    PaymentDetailsId = table.Column<Guid>(type: "uuid", nullable: false),
                    ValidationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashBack", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CashBack_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CashBack_PaymentDetails_PaymentDetailsId",
                        column: x => x.PaymentDetailsId,
                        principalTable: "PaymentDetails",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "GroupTicket",
                columns: table => new
                {
                    GroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    TicketId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GroupTicket", x => new { x.GroupId, x.TicketId });
                    table.ForeignKey(
                        name: "FK_GroupTicket_Group_GroupId",
                        column: x => x.GroupId,
                        principalTable: "Group",
                        principalColumn: "GroupId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_GroupTicket_Ticket_TicketId",
                        column: x => x.TicketId,
                        principalTable: "Ticket",
                        principalColumn: "TicketId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FailedSalesOrder",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FailedSalesOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FailedSalesOrder_SalesOrder_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalTable: "SalesOrder",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SalesInvoice",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProcessInstanceId = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    DynoAmount = table.Column<double>(type: "double precision", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    NetAmount = table.Column<double>(type: "double precision", nullable: false),
                    VATAmount = table.Column<double>(type: "double precision", nullable: false),
                    TotalAmount = table.Column<double>(type: "double precision", nullable: false),
                    PaymentMethod = table.Column<int>(type: "integer", nullable: false),
                    ProductType = table.Column<int>(type: "integer", nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    SalesOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    DocumentId = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesInvoice", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesInvoice_Company_CompanyId",
                        column: x => x.CompanyId,
                        principalTable: "Company",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SalesInvoice_Document_DocumentId",
                        column: x => x.DocumentId,
                        principalTable: "Document",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesInvoice_SalesOrder_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalTable: "SalesOrder",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageNotificationEntitySubscriberDeviceEntity",
                columns: table => new
                {
                    MessageNotificationsId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscribersId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageNotificationEntitySubscriberDeviceEntity", x => new { x.MessageNotificationsId, x.SubscribersId });
                    table.ForeignKey(
                        name: "FK_MessageNotificationEntitySubscriberDeviceEntity_MessageNoti~",
                        column: x => x.MessageNotificationsId,
                        principalTable: "MessageNotification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MessageNotificationEntitySubscriberDeviceEntity_SubscriberD~",
                        column: x => x.SubscribersId,
                        principalTable: "SubscriberDeviceEntity",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FailedCashBack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CashbackId = table.Column<Guid>(type: "uuid", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatorUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatorUserEmail = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifierUserEmail = table.Column<string>(type: "text", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeleterUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterUserEmail = table.Column<string>(type: "text", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FailedCashBack", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FailedCashBack_CashBack_CashbackId",
                        column: x => x.CashbackId,
                        principalTable: "CashBack",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Company",
                columns: new[] { "Id", "CountryCode", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "Email", "EntrepriseType", "FeePercent", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PhoneNumber", "Picture", "ServiceType", "Status", "TaxRegistration", "WalletId" },
                values: new object[] { new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, null, null, null, "<EMAIL>", 0, 11.0, null, null, null, "Dyno & Motiva Systems", "+21699001000", null, 0, 0, "1803838P", new Guid("00000000-0000-0000-0000-000000000000") });

            migrationBuilder.InsertData(
                table: "Role",
                columns: new[] { "Id", "CompanyId", "ConcurrencyStamp", "CreationTime", "CreatorUserId", "DeleterUserId", "DeletionTime", "LastModificationTime", "LastModifierUserId", "Name", "NormalizedName", "Status", "UserType" },
                values: new object[,]
                {
                    { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"), null, "00db659a-43e8-4e48-b14f-402fde21c2bc", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9556), new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, "Client", "CLIENT", 0, 4 },
                    { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), null, "c5e8e0f5-0620-4b70-b718-3c87d4b0b561", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9531), null, null, null, null, null, "Default", "DEFAULT", 0, 3 },
                    { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), null, "adf1c387-cd96-4b08-87c3-a48f540e527f", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9540), new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, "SuperAdmin", "SUPERADMIN", 0, 3 },
                    { new Guid("6ce897f3-687b-4ff3-8328-d0854bc50769"), null, "f99ae2b1-9c80-482a-b293-644806477a94", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9573), new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, "Cashier", "CASHIER", 0, 2 },
                    { new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), null, "baa475f8-522b-4143-8fad-f2e47e1bb084", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9550), new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, "Shop Admin", "SHOP ADMIN", 0, 0 },
                    { new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), null, "622856f9-e8e4-46c3-adbe-b537c6a5259a", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9545), new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), null, null, null, null, "Admin", "ADMIN", 0, 1 }
                });

            migrationBuilder.InsertData(
                table: "User",
                columns: new[] { "Id", "AccessFailedCount", "AuthentificationSource", "CompanyId", "ConcurrencyStamp", "CountryCode", "CreationTime", "CreatorUserId", "DateOfBirth", "DeleterUserId", "DeletionTime", "Email", "EmailConfirmed", "FullName", "Gender", "LastModificationTime", "LastModifierUserId", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PhoneNumber", "PhoneNumberConfirmed", "Picture", "PinCode", "SecurityStamp", "Status", "TwoFactorEnabled", "UserName", "UserType" },
                values: new object[] { new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), 0, 0, new Guid("ec9beada-891a-44e5-97cc-9d3129cf84c6"), "c4bf2f2a-a846-4ae4-b264-ce3171efd9bb", "", new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9945), new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), new DateTime(2024, 3, 13, 12, 15, 9, 239, DateTimeKind.Utc).AddTicks(9944), null, null, "<EMAIL>", true, "Dyno Root", 2, null, null, false, null, "<EMAIL>", "CFBC54C0-97CE-40FF-9281-79D09713CB71", "AQAAAAEAACcQAAAAECssTfMVvC/F+jL3HBdzvwHh66CG4AOOPuWK4T2fvcpNQNN6ZAjGYtKFFJHgkt3bEg==", null, true, null, "ZFxn5dSGlaMFoTfAET4RbyhYkvs39oCKFHsXjahuXSg=", null, 0, false, "cfbc54c0-97ce-40ff-9281-79d09713cb71", 3 });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("1237313c-c28f-4f0e-b7d5-4191c0845aa8"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", null, null, new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", null, null, new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("cc46d60e-835d-4cb7-bf16-87d5620e47d7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Company Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f1860454-6320-448e-97cd-a289a85e871e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, true, null, null, null, "Auth Management", false, null, new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 }
                });

            migrationBuilder.InsertData(
                table: "RoleUser",
                columns: new[] { "RoleId", "UserId" },
                values: new object[,]
                {
                    { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46790"), new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb70") },
                    { new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71") }
                });

            migrationBuilder.InsertData(
                table: "Permission",
                columns: new[] { "Id", "CreationTime", "CreatorUserEmail", "CreatorUserId", "DeleterUserEmail", "DeleterUserId", "DeletionTime", "IsLeaf", "IsRoot", "LastModificationTime", "LastModifierUserEmail", "LastModifierUserId", "Name", "PartialSelected", "PermissionEntityId", "RoleId", "Status" },
                values: new object[,]
                {
                    { new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("cc46d60e-835d-4cb7-bf16-87d5620e47d7"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("f1860454-6320-448e-97cd-a289a85e871e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("1237313c-c28f-4f0e-b7d5-4191c0845aa8"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", null, new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("f1860454-6320-448e-97cd-a289a85e871e"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", null, new Guid("71f01a9a-c025-45cb-a265-b4c3f0529264"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", null, new Guid("c5cfc239-3cd1-4242-9d71-739d3602d720"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Company", false, new Guid("c5cfc239-3cd1-4242-9d71-739d3602d72c"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("fcb200ef-d7f2-4c48-884e-0aafbeb92b2f"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e2bf1834-becd-408f-bc84-56b826165271"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "Role", false, new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, false, false, null, null, null, "User", false, new Guid("8fc71647-a610-4e86-969a-6a4139012e2d"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("00ec879c-de9c-4410-8ab4-62e6f7169835"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("0d2c1dc2-5405-4b5d-bb08-f04a90922e10"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", null, new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("0ef2a3a1-95cf-47d2-9f01-83d22ff02359"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("154e6e38-4fbe-4d40-ae1b-02a907c1408e"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("155d0062-f896-48e3-af1e-e36429084a51"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("1706f554-764c-4314-91ba-56ab40d0b320"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", null, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("1706f554-764c-4314-91ba-56ab40d0b32f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("1dc03eb8-467b-43b2-b104-9124af5a62dd"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("21211758-30b2-4222-be22-9c4065c155ff"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("25e5a90f-6287-4033-829a-ecc417e54343"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", null, new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("2e384021-cbd8-405b-8b9b-365384a008c7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("3c0e20f8-fd44-43d9-8ed8-b9ec25a7add3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", null, new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("3d8043f6-297b-4de5-8b30-f7bd8a61bd38"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("404e1c94-d42b-4727-9285-646da8ce5623"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("540f6b1d-f177-4702-862b-edfe0f7033cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("54542c70-1670-41df-9a05-00cf29b0e221"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("5bd1f892-63ae-4bcf-9c60-339202fc9d82"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("602aa4d2-2f12-40c2-bb07-1a22621d725d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", null, new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("6c42303e-9466-40a3-9f04-59db7efd39c3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("709b16ea-18c1-4a87-92bb-e9c9c88dbcee"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", null, new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("72ca6f73-7afe-48ba-9306-09624fd8221b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("77b55a22-850d-4177-9a00-974f6d7107e2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", null, new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("78b8f760-f466-4aa0-aa9f-7fd2205cbf73"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("498abf08-3df1-4484-8677-649d09a9f2d6"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("7dec5f33-050e-42fc-9643-08461b2f878d"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", null, new Guid("666cbe17-0d1b-48c9-ad79-0411cb9958ab"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("7e004f36-b49e-4409-8351-e8ff7950d401"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("869dcfdc-9350-4538-99a8-87bcaeea34a2"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("af8f26d8-18b9-4a86-a297-df489298c1c3"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("91ee99a2-9756-4fdd-acf8-dc6aceca52a7"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("a0568a01-ae4e-4055-9262-3a817fb76e00"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", null, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("a0568a01-ae4e-4055-9262-3a817fb76e0f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", null, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("a0e23769-62e5-4a28-988b-ffe89b334ea4"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("a9ccc7d0-0765-4a94-9bd4-385631825d50"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Role", false, new Guid("e2bf1834-becd-408f-bc84-56b826165271"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("bf2ab398-4487-40f9-95be-e862083e4bec"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c186861f-08c1-4463-8d63-5484c5652918"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Roles", false, new Guid("e2bf1834-becd-408f-bc84-56b826165271"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("c3c0c6fe-249a-4ab0-b470-e0e8f3667bef"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("c5a29f14-3299-4ac4-998e-19fcd1b63c75"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("c697a4f3-c352-4279-8845-f7f0ca040b14"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New Role", false, new Guid("e2bf1834-becd-408f-bc84-56b826165271"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("ceceb370-cb0d-49ab-a458-301078a9d126"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", null, new Guid("393bd35b-5d3d-4694-9dda-5a87bc656a43"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("d5cae7c2-e640-417b-98d5-d559aaa87539"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("d6dbac4f-e8f4-458c-821f-f781bfd9ce2b"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("e79081c0-e52c-47fa-ac2a-404d2a474d27"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Create New User", false, new Guid("58e97429-64c6-4a99-a10a-a9770f357272"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("ee424456-2dd5-448b-a24a-a4ffa23db15f"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Users", false, new Guid("f68da15a-ee5d-4bd1-a712-60033a1d04bd"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("f20b2983-6d9b-4fe0-8ee8-5a0255d5a2e0"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Role", false, new Guid("e2bf1834-becd-408f-bc84-56b826165271"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 },
                    { new Guid("f364c2e8-49b4-437a-a8c9-696007b20854"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("157da159-f7da-4b21-ad38-2975391bf43b"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("f66b8182-afd4-4f6a-a453-b4ee05c862cf"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "View All Company", false, new Guid("230b7b24-97d6-4c11-bf47-6b10f7e274a5"), new Guid("c405a6c5-fabc-437a-a6f3-4c577a70f344"), 0 },
                    { new Guid("f9082292-f575-4cfd-9e31-a634426215f3"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Delete User", false, new Guid("1c31de79-2406-4365-ae90-2e77409f4972"), new Guid("e26f1a93-7603-4d13-b4e5-55ec581522e2"), 0 },
                    { new Guid("fd100341-f14e-4e90-908b-5fefe8f52520"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", null, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f5680"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46795"), 0 },
                    { new Guid("fd100341-f14e-4e90-908b-5fefe8f5252a"), null, null, new Guid("cfbc54c0-97ce-40ff-9281-79d09713cb71"), null, null, null, true, false, null, null, null, "Update Company", false, new Guid("8b67666b-b2e2-4560-bc22-02b46c5f568a"), new Guid("1fea8a74-0e14-43a4-bb5b-a72e31c46799"), 0 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Address_CompanyId",
                table: "Address",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Address_UserId",
                table: "Address",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CashBack_CompanyId",
                table: "CashBack",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_CashBack_PaymentDetailsId",
                table: "CashBack",
                column: "PaymentDetailsId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyNotification_NotificationId",
                table: "CompanyNotification",
                column: "NotificationId");

            migrationBuilder.CreateIndex(
                name: "IX_Employee_CompanyId",
                table: "Employee",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Employee_GroupId",
                table: "Employee",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Employee_UserId",
                table: "Employee",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_FailedCashBack_CashbackId",
                table: "FailedCashBack",
                column: "CashbackId");

            migrationBuilder.CreateIndex(
                name: "IX_FailedSalesOrder_SalesOrderId",
                table: "FailedSalesOrder",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Group_CompanyId",
                table: "Group",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_GroupTicket_TicketId",
                table: "GroupTicket",
                column: "TicketId");

            migrationBuilder.CreateIndex(
                name: "IX_MacAddress_UserId",
                table: "MacAddress",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageNotificationEntitySubscriberDeviceEntity_Subscribers~",
                table: "MessageNotificationEntitySubscriberDeviceEntity",
                column: "SubscribersId");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentDetails_CompanyId",
                table: "PaymentDetails",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Permission_PermissionEntityId",
                table: "Permission",
                column: "PermissionEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_Permission_RoleId",
                table: "Permission",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleUser_UserId",
                table: "RoleUser",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesInvoice_CompanyId",
                table: "SalesInvoice",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesInvoice_DocumentId",
                table: "SalesInvoice",
                column: "DocumentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SalesInvoice_SalesOrderId",
                table: "SalesInvoice",
                column: "SalesOrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CompanyId",
                table: "SalesOrder",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_DocumentId",
                table: "SalesOrder",
                column: "DocumentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubscriberDeviceEntity_UserEntityId",
                table: "SubscriberDeviceEntity",
                column: "UserEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_Ticket_CompanyEntityId",
                table: "Ticket",
                column: "CompanyEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_UserOTP_UserId",
                table: "UserOTP",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserToken_UserId",
                table: "UserToken",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Address");

            migrationBuilder.DropTable(
                name: "CompanyNotification");

            migrationBuilder.DropTable(
                name: "Employee");

            migrationBuilder.DropTable(
                name: "FailedCashBack");

            migrationBuilder.DropTable(
                name: "FailedSalesOrder");

            migrationBuilder.DropTable(
                name: "GroupTicket");

            migrationBuilder.DropTable(
                name: "LogError");

            migrationBuilder.DropTable(
                name: "MacAddress");

            migrationBuilder.DropTable(
                name: "MessageNotificationEntitySubscriberDeviceEntity");

            migrationBuilder.DropTable(
                name: "Permission");

            migrationBuilder.DropTable(
                name: "RoleUser");

            migrationBuilder.DropTable(
                name: "SalesInvoice");

            migrationBuilder.DropTable(
                name: "UserOTP");

            migrationBuilder.DropTable(
                name: "UserToken");

            migrationBuilder.DropTable(
                name: "SignalRNotification");

            migrationBuilder.DropTable(
                name: "CashBack");

            migrationBuilder.DropTable(
                name: "Group");

            migrationBuilder.DropTable(
                name: "Ticket");

            migrationBuilder.DropTable(
                name: "MessageNotification");

            migrationBuilder.DropTable(
                name: "SubscriberDeviceEntity");

            migrationBuilder.DropTable(
                name: "Role");

            migrationBuilder.DropTable(
                name: "SalesOrder");

            migrationBuilder.DropTable(
                name: "PaymentDetails");

            migrationBuilder.DropTable(
                name: "User");

            migrationBuilder.DropTable(
                name: "Document");

            migrationBuilder.DropTable(
                name: "Company");
        }
    }
}
