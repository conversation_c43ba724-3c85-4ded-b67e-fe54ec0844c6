﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Platform.Dyno.Shared.RefData
{
    public static class RefDataService<T> where T : ReferentialData
    {
        public static void CreateRefData(T dtoObject, Guid? creatorUserId,string? userEmail="")
        {
            dtoObject.CreationTime = DateTime.UtcNow;
            dtoObject.LastModificationTime = DateTime.UtcNow;
            dtoObject.CreatorUserId = creatorUserId;
            dtoObject.CreatorUserEmail = userEmail;
            dtoObject.Status = Enum.Status.Active;
        }

        public static void UpdateRefData(T dtoObject, Guid? updateUserId, string? userEmail="")
        {
            dtoObject.LastModificationTime = DateTime.UtcNow;
            dtoObject.LastModifierUserId = updateUserId;
            dtoObject.LastModifierUserEmail = userEmail;
        }

        public static void DeleteRefData(T dtoObject, Guid? deletorUserId, string? userEmail="") 
        {
            dtoObject.DeletionTime = DateTime.Now.ToUniversalTime();
            dtoObject.DeleterUserId = deletorUserId;
            dtoObject.DeleterUserEmail = userEmail;
            dtoObject.LastModificationTime = DateTime.UtcNow;
            dtoObject.Status = Enum.Status.Deleted;
        }
    }
}
